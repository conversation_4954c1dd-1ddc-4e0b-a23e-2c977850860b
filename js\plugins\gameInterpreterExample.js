/**
 * 🎯 Game_Interpreter消息拦截器使用示例 - 包含位置信息处理
 *
 * 展示如何在删除内置UI的情况下使用GameInterpreterMessageInterceptor
 * 完全接管消息处理流程，并处理UI组件的位置信息
 */

(() => {
    'use strict';

    // 🔑 重写Scene_Map的createAllWindows方法，删除内置UI
    const originalCreateAllWindows = Scene_Map.prototype.createAllWindows;
    Scene_Map.prototype.createAllWindows = function() {
        console.log('🎯 重写Scene_Map.createAllWindows - 删除内置UI');

        // 只创建地图名称窗口，不创建消息相关窗口
        this.createMapNameWindow();

        // 🔑 不调用Scene_Message.prototype.createAllWindows.call(this);
        // 这样就不会创建Window_Message等内置UI

        console.log('✅ 自定义createAllWindows完成 - 内置UI已删除');
    };

    // 等待拦截器加载完成
    setTimeout(() => {
        if (window.MessageInterceptor) {
            console.log('🎯 开始设置Game_Interpreter消息拦截器示例');

            // 🔑 设置统一消息回调 - 支持UI组件数组和位置信息
            window.MessageInterceptor.onUnifiedMessage = function(messageData) {
                console.log('📨 收到统一消息:', messageData);
                console.log('🔍 UI组件数组:', messageData.uiComponents);
                console.log('📊 组件统计:', messageData.componentStats);

                // 🔑 根据UI组件数组进行处理
                if (messageData.componentStats.hasScrollText) {
                    // 滚动文本
                    handleScrollText(messageData);
                } else if (messageData.componentStats.hasInteractive) {
                    // 有交互UI的消息
                    handleInteractiveMessage(messageData);
                } else {
                    // 纯文本消息
                    handleTextOnlyMessage(messageData);
                }
            };

            console.log('✅ Game_Interpreter消息拦截器示例设置完成');
        } else {
            console.warn('⚠️ MessageInterceptor 未找到');
        }
    }, 1000);

    /**
     * 🎯 处理滚动文本 - 使用UI组件数组和位置信息
     */
    function handleScrollText(messageData) {
        console.log('📜 处理滚动文本');

        // 获取滚动文本组件
        const scrollTextComponent = messageData.uiComponents.find(c => c.type === 'scrollText');
        if (scrollTextComponent) {
            console.log('📜 滚动文本数据:', scrollTextComponent.data);
            console.log('📍 滚动文本位置:', scrollTextComponent.position);
            createCustomScrollTextUI(scrollTextComponent.data, scrollTextComponent.position);
        }

        // 模拟处理完成
        setTimeout(() => {
            window.MessageInterceptor.completeMessage();
        }, 3000);
    }

    /**
     * 🎯 处理有交互UI的消息 - 使用UI组件数组和位置信息
     */
    function handleInteractiveMessage(messageData) {
        console.log('🎮 处理交互消息');

        // 🔑 遍历所有UI组件进行处理，包含位置信息
        messageData.uiComponents.forEach(component => {
            console.log(`🎨 处理${component.type}组件:`, component.data);
            console.log(`📍 ${component.type}位置信息:`, component.position);

            switch (component.type) {
                case 'text':
                    displayTextComponent(component);
                    break;
                case 'nameBox':
                    displayNameBoxComponent(component);
                    break;
                case 'gold':
                    displayGoldComponent(component);
                    break;
                case 'choice':
                    displayChoiceComponent(component);
                    break;
                case 'numberInput':
                    displayNumberInputComponent(component);
                    break;
                case 'itemChoice':
                    displayItemChoiceComponent(component);
                    break;
            }
        });
    }

    /**
     * 🎯 处理纯文本消息 - 使用UI组件数组和位置信息
     */
    function handleTextOnlyMessage(messageData) {
        console.log('📝 处理纯文本消息');

        // 🔑 遍历所有UI组件进行处理，包含位置信息
        messageData.uiComponents.forEach(component => {
            console.log(`🎨 处理${component.type}组件:`, component.data);
            console.log(`📍 ${component.type}位置信息:`, component.position);

            switch (component.type) {
                case 'text':
                    displayTextComponent(component);
                    break;
                case 'nameBox':
                    displayNameBoxComponent(component);
                    break;
                case 'gold':
                    displayGoldComponent(component);
                    break;
            }
        });

        // 模拟用户点击确认
        setTimeout(() => {
            window.MessageInterceptor.completeMessage();
        }, 2000);
    }

    // ===== 新的UI组件处理函数 - 包含位置信息 =====

    /**
     * 🎯 显示文本组件 - 包含位置信息
     */
    function displayTextComponent(component) {
        console.log('📄 显示文本组件:', component.data);
        console.log('📍 文本位置信息:', component.position);
        createCustomMessageUI(component.data, component.position);
    }

    /**
     * 🎯 显示姓名框组件 - 包含位置信息
     */
    function displayNameBoxComponent(component) {
        console.log('🏷️ 显示姓名框组件:', component.data);
        console.log('📍 姓名框位置信息:', component.position);
        createCustomNameBoxUI(component.data.speakerName, component.position);
    }

    /**
     * 🎯 显示金钱组件 - 包含位置信息
     */
    function displayGoldComponent(component) {
        console.log('💰 显示金钱组件:', component.data);
        console.log('📍 金钱窗口位置信息:', component.position);
        createCustomGoldUI(component.data.amount, component.position);
    }

    /**
     * 🎯 显示选择项组件 - 包含位置信息
     */
    function displayChoiceComponent(component) {
        console.log('🔘 显示选择项组件:', component.data);
        console.log('📍 选择项位置信息:', component.position);
        createCustomChoiceUI(component.data, component.position);

        // 模拟用户选择
        setTimeout(() => {
            const selectedIndex = 0; // 模拟选择第一项
            window.MessageInterceptor.handleChoiceSelected(selectedIndex);
        }, 2000);
    }

    /**
     * 🎯 显示数值输入组件 - 包含位置信息
     */
    function displayNumberInputComponent(component) {
        console.log('🔢 显示数值输入组件:', component.data);
        console.log('📍 数值输入位置信息:', component.position);
        createCustomNumberInputUI(component.data, component.position);

        // 模拟用户输入
        setTimeout(() => {
            const inputValue = 123; // 模拟输入值
            window.MessageInterceptor.handleNumberInputCompleted(inputValue);
        }, 2000);
    }

    /**
     * 🎯 显示物品选择组件 - 包含位置信息
     */
    function displayItemChoiceComponent(component) {
        console.log('🎒 显示物品选择组件:', component.data);
        console.log('📍 物品选择位置信息:', component.position);
        createCustomItemChoiceUI(component.data, component.position);

        // 模拟用户选择
        setTimeout(() => {
            const selectedItemId = 1; // 模拟选择物品ID
            window.MessageInterceptor.handleItemChoiceCompleted(selectedItemId);
        }, 2000);
    }

    // ===== 自定义UI创建函数 - 支持位置信息 =====

    function createCustomMessageUI(textData, position) {
        console.log('🎨 创建自定义消息UI:', textData);
        console.log('📍 应用位置信息:', position);

        // 🔑 新功能：处理RTL语言
        if (textData.isRTL) {
            console.log('🔄 检测到RTL语言，调整文本方向');
        }

        // 🔑 新功能：处理转义字符
        if (textData.escapeCharacters) {
            console.log('🎭 转义字符信息:', textData.escapeCharacters);

            // 处理变量替换
            if (textData.escapeCharacters.variables.length > 0) {
                console.log('📊 变量替换:', textData.escapeCharacters.variables);
            }

            // 处理角色名替换
            if (textData.escapeCharacters.actorNames.length > 0) {
                console.log('👤 角色名替换:', textData.escapeCharacters.actorNames);
            }

            // 处理颜色代码
            if (textData.escapeCharacters.colors.length > 0) {
                console.log('🎨 颜色代码:', textData.escapeCharacters.colors);
            }

            // 处理图标
            if (textData.escapeCharacters.icons.length > 0) {
                console.log('🖼️ 图标:', textData.escapeCharacters.icons);
            }

            // 处理等待时间
            if (textData.escapeCharacters.waitTimes.length > 0) {
                console.log('⏱️ 等待时间:', textData.escapeCharacters.waitTimes);
            }

            // 处理控制代码
            if (textData.escapeCharacters.controlCodes.length > 0) {
                console.log('🎮 控制代码:', textData.escapeCharacters.controlCodes);
            }
        }

        // 🔑 新功能：消息继续标志
        if (textData.doesContinue) {
            console.log('➡️ 消息将继续，不关闭窗口');
        }
    }

    function createCustomNameBoxUI(speakerName, position) {
        console.log('🎨 创建自定义姓名框UI:', speakerName);
        console.log('📍 应用位置信息:', position);
        // position.horizontal: 'left'/'right' (根据RTL)
        // position.vertical: 'above'/'below' (根据消息窗口位置)
    }

    function createCustomGoldUI(goldAmount, position) {
        console.log('🎨 创建自定义金钱UI:', goldAmount);
        console.log('📍 应用位置信息:', position);
        // position.horizontal: 'right' (总是右对齐)
        // position.vertical: 'top'/'bottom' (根据消息窗口位置)
    }

    function createCustomChoiceUI(choiceData, position) {
        console.log('🎨 创建自定义选择项UI:', choiceData);
        console.log('📍 应用位置信息:', position);
        // position.horizontal: 'left'/'center'/'right' (0/1/2)
        // position.vertical: 'auto' (根据消息窗口位置自动调整)
    }

    function createCustomNumberInputUI(numberInputData, position) {
        console.log('🎨 创建自定义数值输入UI:', numberInputData);
        console.log('📍 应用位置信息:', position);
        // position.horizontal: 'center' (总是居中)
        // position.vertical: 'auto' (根据消息窗口位置自动调整)
    }

    function createCustomItemChoiceUI(itemChoiceData, position) {
        console.log('🎨 创建自定义物品选择UI:', itemChoiceData);
        console.log('📍 应用位置信息:', position);
        // position.horizontal: 'center' (总是居中)
        // position.vertical: 'opposite' (与消息窗口相对)
        // position.fullWidth: true (占据全宽)
    }

    function createCustomScrollTextUI(scrollTextData, position) {
        console.log('🎨 创建自定义滚动文本UI:', scrollTextData);
        console.log('📍 应用位置信息:', position);
        // position.horizontal: 'center' (居中)
        // position.vertical: 'center' (居中)
        // position.fullScreen: true (占据全屏)
    }

})();
