/**
 * 🎯 Game_Interpreter消息拦截器使用示例
 * 
 * 展示如何在删除内置UI的情况下使用GameInterpreterMessageInterceptor
 * 完全接管消息处理流程
 */

(() => {
    'use strict';

    // 🔑 重写Scene_Map的createAllWindows方法，删除内置UI
    const originalCreateAllWindows = Scene_Map.prototype.createAllWindows;
    Scene_Map.prototype.createAllWindows = function() {
        console.log('🎯 重写Scene_Map.createAllWindows - 删除内置UI');
        
        // 只创建地图名称窗口，不创建消息相关窗口
        this.createMapNameWindow();
        
        // 🔑 不调用Scene_Message.prototype.createAllWindows.call(this);
        // 这样就不会创建Window_Message等内置UI
        
        console.log('✅ 自定义createAllWindows完成 - 内置UI已删除');
    };

    // 等待拦截器加载完成
    setTimeout(() => {
        if (window.MessageInterceptor) {
            console.log('🎯 开始设置Game_Interpreter消息拦截器示例');

            // 🔑 设置统一消息回调
            window.MessageInterceptor.onUnifiedMessage = function(messageData) {
                console.log('📨 收到统一消息:', messageData);

                // 🔑 根据消息类型进行处理
                if (messageData.isScrollText) {
                    // 滚动文本
                    handleScrollText(messageData);
                } else if (messageData.hasInteractiveUI) {
                    // 有交互UI的消息
                    handleInteractiveMessage(messageData);
                } else {
                    // 纯文本消息
                    handleTextOnlyMessage(messageData);
                }
            };

            console.log('✅ Game_Interpreter消息拦截器示例设置完成');
        } else {
            console.warn('⚠️ MessageInterceptor 未找到');
        }
    }, 1000);

    /**
     * 🎯 处理滚动文本
     */
    function handleScrollText(messageData) {
        console.log('📜 处理滚动文本:', messageData);
        
        // 创建自定义滚动文本UI
        createCustomScrollTextUI(messageData);
        
        // 模拟处理完成
        setTimeout(() => {
            window.MessageInterceptor.completeMessage();
        }, 3000);
    }

    /**
     * 🎯 处理有交互UI的消息
     */
    function handleInteractiveMessage(messageData) {
        console.log('🎮 处理交互消息:', messageData.activeInteractiveUI);

        // 显示文本内容
        displayMessageText(messageData);

        // 显示依赖UI（姓名框、金钱窗口）
        displayDependentUI(messageData);

        // 根据交互UI类型显示相应的UI
        switch (messageData.activeInteractiveUI.type) {
            case 'choice':
                displayChoiceUI(messageData.activeInteractiveUI.data);
                break;
            case 'numberInput':
                displayNumberInputUI(messageData.activeInteractiveUI.data);
                break;
            case 'itemChoice':
                displayItemChoiceUI(messageData.activeInteractiveUI.data);
                break;
        }
    }

    /**
     * 🎯 处理纯文本消息
     */
    function handleTextOnlyMessage(messageData) {
        console.log('📝 处理纯文本消息:', messageData.allText);

        // 显示文本内容
        displayMessageText(messageData);

        // 显示依赖UI（姓名框、金钱窗口）
        displayDependentUI(messageData);

        // 模拟用户点击确认
        setTimeout(() => {
            window.MessageInterceptor.completeMessage();
        }, 2000);
    }

    /**
     * 🎯 显示消息文本
     */
    function displayMessageText(messageData) {
        console.log('📄 显示文本:', {
            text: messageData.allText,
            face: messageData.faceName ? `${messageData.faceName}[${messageData.faceIndex}]` : 'none',
            background: messageData.background,
            position: messageData.positionType
        });

        // 创建自定义文本显示UI
        createCustomMessageUI(messageData);
    }

    /**
     * 🎯 显示依赖UI（姓名框、金钱窗口）
     */
    function displayDependentUI(messageData) {
        if (messageData.dependentUI.hasNameBox) {
            console.log('🏷️ 显示姓名框:', messageData.speakerName);
            createCustomNameBoxUI(messageData.speakerName);
        }

        if (messageData.dependentUI.hasGold) {
            console.log('💰 显示金钱窗口:', messageData.dependentUI.goldAmount);
            createCustomGoldUI(messageData.dependentUI.goldAmount);
        }
    }

    /**
     * 🎯 显示选择项UI
     */
    function displayChoiceUI(choiceData) {
        console.log('🔘 显示选择项:', choiceData);

        createCustomChoiceUI(choiceData);

        // 模拟用户选择
        setTimeout(() => {
            const selectedIndex = 0; // 模拟选择第一项
            window.MessageInterceptor.handleChoiceSelected(selectedIndex);
        }, 2000);
    }

    /**
     * 🎯 显示数值输入UI
     */
    function displayNumberInputUI(numberInputData) {
        console.log('🔢 显示数值输入:', numberInputData);

        createCustomNumberInputUI(numberInputData);

        // 模拟用户输入
        setTimeout(() => {
            const inputValue = 123; // 模拟输入值
            window.MessageInterceptor.handleNumberInputCompleted(inputValue, numberInputData.variableId);
        }, 2000);
    }

    /**
     * 🎯 显示物品选择UI
     */
    function displayItemChoiceUI(itemChoiceData) {
        console.log('🎒 显示物品选择:', itemChoiceData);

        createCustomItemChoiceUI(itemChoiceData);

        // 模拟用户选择
        setTimeout(() => {
            const selectedItemId = 1; // 模拟选择物品ID
            window.MessageInterceptor.handleItemChoiceCompleted(selectedItemId, itemChoiceData.variableId);
        }, 2000);
    }

    // ===== 自定义UI创建函数 =====

    function createCustomMessageUI(messageData) {
        console.log('🎨 创建自定义消息UI');
        // 在这里实现你的自定义消息UI
        // 例如：创建HTML元素、Canvas绘制、或者使用你的UI框架
    }

    function createCustomNameBoxUI(speakerName) {
        console.log('🎨 创建自定义姓名框UI:', speakerName);
        // 在这里实现你的自定义姓名框UI
    }

    function createCustomGoldUI(goldAmount) {
        console.log('🎨 创建自定义金钱UI:', goldAmount);
        // 在这里实现你的自定义金钱UI
    }

    function createCustomChoiceUI(choiceData) {
        console.log('🎨 创建自定义选择项UI:', choiceData);
        // 在这里实现你的自定义选择项UI
    }

    function createCustomNumberInputUI(numberInputData) {
        console.log('🎨 创建自定义数值输入UI:', numberInputData);
        // 在这里实现你的自定义数值输入UI
    }

    function createCustomItemChoiceUI(itemChoiceData) {
        console.log('🎨 创建自定义物品选择UI:', itemChoiceData);
        // 在这里实现你的自定义物品选择UI
    }

    function createCustomScrollTextUI(messageData) {
        console.log('🎨 创建自定义滚动文本UI:', messageData);
        // 在这里实现你的自定义滚动文本UI
    }

})();
