[null, {"id": 1, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "以下是你想在恢复物品上方显示\n物品的空间。", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "-----保留", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 2, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 3, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 4, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 5, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 6, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "-----恢复物品", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 7, "animationId": 41, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "恢复500点HP。", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 500}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "药水", "note": "", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 8, "animationId": 41, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "恢复1500点HP。", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 1500}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "超级药水", "note": "", "occasion": 0, "price": 250, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 9, "animationId": 41, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "完全恢复HP。", "effects": [{"code": 11, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "痊愈药水", "note": "", "occasion": 0, "price": 550, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 10, "animationId": 41, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "恢复300点MP。", "effects": [{"code": 12, "dataId": 0, "value1": 0, "value2": 300}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "魔力水", "note": "", "occasion": 0, "price": 300, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 11, "animationId": 49, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "b.mhp / 2", "type": 3, "variance": 20}, "description": "将使用者从死亡中恢复。（恢复一半HP值。）", "effects": [{"code": 22, "dataId": 1, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "兴奋剂", "note": "", "occasion": 0, "price": 500, "repeats": 1, "scope": 9, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 12, "animationId": 46, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "解除使用者除死亡以外的异常状态。", "effects": [{"code": 22, "dataId": 4, "value1": 1, "value2": 0}, {"code": 22, "dataId": 5, "value1": 1, "value2": 0}, {"code": 22, "dataId": 6, "value1": 1, "value2": 0}, {"code": 22, "dataId": 7, "value1": 1, "value2": 0}, {"code": 22, "dataId": 8, "value1": 1, "value2": 0}, {"code": 22, "dataId": 9, "value1": 1, "value2": 0}, {"code": 22, "dataId": 10, "value1": 1, "value2": 0}, {"code": 22, "dataId": 13, "value1": 1, "value2": 0}, {"code": 22, "dataId": 12, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "驱散草药", "note": "", "occasion": 0, "price": 200, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 13, "animationId": 45, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "治愈中毒或麻痹状态。", "effects": [{"code": 22, "dataId": 4, "value1": 1, "value2": 0}, {"code": 22, "dataId": 12, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "解毒剂", "note": "", "occasion": 0, "price": 80, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 14, "animationId": 41, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "恢复一半HP和MP。", "effects": [{"code": 11, "dataId": 0, "value1": 0.5, "value2": 0}, {"code": 12, "dataId": 0, "value1": 0.5, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "奇迹水滴", "note": "", "occasion": 0, "price": 450, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 15, "animationId": 41, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "恢复全部HP和MP。", "effects": [{"code": 11, "dataId": 0, "value1": 1, "value2": 0}, {"code": 12, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "万能药", "note": "", "occasion": 0, "price": 1200, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 16, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 17, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "-----特别物品", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 18, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "遇敌次数暂时减少一半。", "effects": [{"code": 21, "dataId": 29, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "遇敌减少器", "note": "", "occasion": 2, "price": 100, "repeats": 1, "scope": 11, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 19, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "暂时地提升在战斗里物品和金币\n掉落率。", "effects": [{"code": 21, "dataId": 30, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 176, "itypeId": 1, "name": "掉落增加器", "note": "", "occasion": 0, "price": 300, "repeats": 1, "scope": 11, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 20, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 21, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 22, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "-----增强物品", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 23, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "最大HP增加50点。", "effects": [{"code": 42, "dataId": 0, "value1": 50, "value2": 0}], "hitType": 0, "iconIndex": 32, "itypeId": 1, "name": "HP增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 24, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "最大MP增加10点。", "effects": [{"code": 42, "dataId": 1, "value1": 10, "value2": 0}], "hitType": 0, "iconIndex": 33, "itypeId": 1, "name": "MP增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 25, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "攻击力增加3点。", "effects": [{"code": 42, "dataId": 2, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 34, "itypeId": 1, "name": "攻击增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 26, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "防御力增加3点。", "effects": [{"code": 42, "dataId": 3, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 35, "itypeId": 1, "name": "防御增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 27, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "魔法攻击增加3点。", "effects": [{"code": 42, "dataId": 4, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 36, "itypeId": 1, "name": "魔力增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 28, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "魔法防御增加3点。", "effects": [{"code": 42, "dataId": 5, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 37, "itypeId": 1, "name": "抗性增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 29, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "敏捷增加3点。", "effects": [{"code": 42, "dataId": 6, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 38, "itypeId": 1, "name": "敏捷增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 30, "animationId": 51, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "幸运增加3点。", "effects": [{"code": 42, "dataId": 7, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 39, "itypeId": 1, "name": "幸运增强剂", "note": "", "occasion": 2, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}]