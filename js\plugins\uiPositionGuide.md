# RPG Maker MZ UI位置系统详解

## 🎯 概述

基于对RPG Maker MZ源码的深入分析，每个UI组件都有详细的位置设置功能。我们在UI组件数组中添加了完整的`position`属性来描述每个组件的位置信息。

## 📍 位置信息结构

```javascript
position: {
    type: 'UI类型',           // UI组件类型
    relativeTo: '相对对象',    // 相对于什么定位
    horizontal: '水平位置',    // 水平对齐方式 (字符串)
    vertical: '垂直位置',      // 垂直对齐方式 (字符串)
    background: '背景类型',    // 背景类型 (字符串)
    fullScreen: false,        // 是否全屏
    fullWidth: false          // 是否全宽
}
```

## 🔧 统一的字符串格式

**所有位置和背景属性都使用描述性字符串，而不是数字，提高可读性和一致性。**

## 🔍 各UI组件位置详解

### 1. 文本组件 (text)

```javascript
position: {
    type: 'message',
    relativeTo: 'screen',     // 相对于屏幕
    horizontal: 'center',     // 总是居中
    vertical: 'top|center|bottom',  // 字符串格式
    background: 'window|dim|transparent'  // 字符串格式
}
```

**位置计算逻辑：**
```javascript
// 源码：Window_Message.prototype.updatePlacement
this.y = (this._positionType * (Graphics.boxHeight - this.height)) / 2;
// positionType: 0=顶部, 1=中间, 2=底部
```

### 2. 姓名框组件 (nameBox)

```javascript
position: {
    type: 'nameBox',
    relativeTo: 'message',    // 相对于消息窗口
    horizontal: isRTL ? 'right' : 'left',  // RTL语言右对齐，否则左对齐
    vertical: messageY > 0 ? 'above' : 'below',  // 消息在上时显示在下方
    background: messageBackground
}
```

**位置计算逻辑：**
```javascript
// 源码：Window_NameBox.prototype.updatePlacement
// 水平位置
if ($gameMessage.isRTL()) {
    this.x = messageWindow.x + messageWindow.width - this.width;  // 右对齐
} else {
    this.x = messageWindow.x;  // 左对齐
}
// 垂直位置
if (messageWindow.y > 0) {
    this.y = messageWindow.y - this.height;  // 消息窗口上方
} else {
    this.y = messageWindow.y + messageWindow.height;  // 消息窗口下方
}
```

### 3. 金钱窗口组件 (gold)

```javascript
position: {
    type: 'gold',
    relativeTo: 'message',    // 相对于消息窗口
    horizontal: 'right',      // 总是右对齐
    vertical: messageY > 0 ? 'top' : 'bottom',  // 消息在上时显示在顶部
    background: 0             // 总是窗口背景
}
```

**位置计算逻辑：**
```javascript
// 源码：Window_Message.prototype.updatePlacement
if (goldWindow) {
    goldWindow.y = this.y > 0 ? 0 : Graphics.boxHeight - goldWindow.height;
    // 消息在上时金钱窗口在顶部，消息在下时金钱窗口在底部
}
```

### 4. 选择项组件 (choice)

```javascript
position: {
    type: 'choice',
    relativeTo: 'message',    // 相对于消息窗口
    horizontal: positionType, // 0=左, 1=中, 2=右
    vertical: 'auto',         // 根据消息窗口位置自动调整
    background: background    // 背景类型
}
```

**位置计算逻辑：**
```javascript
// 源码：Window_ChoiceList.prototype.windowX
const positionType = $gameMessage.choicePositionType();
if (positionType === 1) {
    return (Graphics.boxWidth - this.windowWidth()) / 2;  // 居中
} else if (positionType === 2) {
    return Graphics.boxWidth - this.windowWidth();        // 右对齐
} else {
    return 0;  // 左对齐
}

// 源码：Window_ChoiceList.prototype.windowY
const messageY = this._messageWindow.y;
if (messageY >= Graphics.boxHeight / 2) {
    return messageY - this.windowHeight();  // 消息在下半部分时，选择项在上方
} else {
    return messageY + this._messageWindow.height;  // 消息在上半部分时，选择项在下方
}
```

### 5. 数值输入组件 (numberInput)

```javascript
position: {
    type: 'numberInput',
    relativeTo: 'message',    // 相对于消息窗口
    horizontal: 'center',     // 总是居中
    vertical: 'auto',         // 根据消息窗口位置自动调整
    background: 0             // 总是窗口背景
}
```

**位置计算逻辑：**
```javascript
// 源码：Window_NumberInput.prototype.updatePlacement
this.x = (Graphics.boxWidth - this.width) / 2;  // 水平居中
if (messageY >= Graphics.boxHeight / 2) {
    this.y = messageY - this.height - spacing;  // 消息在下时，数值输入在上
} else {
    this.y = messageY + this._messageWindow.height + spacing;  // 消息在上时，数值输入在下
}
```

### 6. 物品选择组件 (itemChoice)

```javascript
position: {
    type: 'itemChoice',
    relativeTo: 'message',    // 相对于消息窗口
    horizontal: 'center',     // 总是居中
    vertical: 'opposite',     // 与消息窗口相对
    background: 0,            // 总是窗口背景
    fullWidth: true           // 占据全宽
}
```

**位置计算逻辑：**
```javascript
// 源码：Window_EventItem.prototype.updatePlacement
if (this._messageWindow.y >= Graphics.boxHeight / 2) {
    this.y = 0;  // 消息在下时，物品选择在顶部
} else {
    this.y = Graphics.boxHeight - this.height;  // 消息在上时，物品选择在底部
}
```

### 7. 滚动文本组件 (scrollText)

```javascript
position: {
    type: 'scrollText',
    relativeTo: 'screen',     // 相对于屏幕
    horizontal: 'center',     // 居中
    vertical: 'center',       // 居中
    background: 1,            // 总是暗化背景
    fullScreen: true          // 占据全屏
}
```

## 📊 位置类型枚举

### 水平位置 (horizontal)
- `'left'` - 左对齐
- `'center'` - 居中
- `'right'` - 右对齐

### 垂直位置 (vertical)
- `'top'` - 顶部
- `'center'` - 居中
- `'bottom'` - 底部
- `'above'` - 在参考对象上方
- `'below'` - 在参考对象下方
- `'opposite'` - 与参考对象相对
- `'auto'` - 自动调整

### 背景类型 (background)
- `'window'` - 窗口背景 (原数值: 0)
- `'dim'` - 暗化背景 (原数值: 1)
- `'transparent'` - 透明背景 (原数值: 2)

### 相对对象 (relativeTo)
- `'screen'` - 相对于屏幕
- `'message'` - 相对于消息窗口

## 🔄 RPG MZ原生数值对照表

### 垂直位置转换
| 原生数值 | 字符串值 | 含义 |
|----------|----------|------|
| 0 | `'top'` | 顶部 |
| 1 | `'center'` | 居中 |
| 2 | `'bottom'` | 底部 |

### 水平位置转换 (选择项)
| 原生数值 | 字符串值 | 含义 |
|----------|----------|------|
| 0 | `'left'` | 左对齐 |
| 1 | `'center'` | 居中 |
| 2 | `'right'` | 右对齐 |

### 背景类型转换
| 原生数值 | 字符串值 | 含义 |
|----------|----------|------|
| 0 | `'window'` | 窗口背景 |
| 1 | `'dim'` | 暗化背景 |
| 2 | `'transparent'` | 透明背景 |

## 🎨 实际应用示例

```javascript
// 处理UI组件位置
function applyUIPosition(component) {
    const pos = component.position;

    switch (pos.type) {
        case 'message':
            // 消息窗口位置
            const y = pos.vertical * (Graphics.boxHeight - windowHeight) / 2;
            break;

        case 'choice':
            // 选择项位置
            let x = 0;
            if (pos.horizontal === 'center') {
                x = (Graphics.boxWidth - windowWidth) / 2;
            } else if (pos.horizontal === 'right') {
                x = Graphics.boxWidth - windowWidth;
            }
            break;

        case 'nameBox':
            // 姓名框位置
            if (pos.horizontal === 'right') {
                x = messageWindow.x + messageWindow.width - windowWidth;
            } else {
                x = messageWindow.x;
            }
            break;
    }
}
```

这个位置系统完全基于RPG Maker MZ的原生逻辑，确保了自定义UI的位置与原生UI完全一致！
