/**
 * 🎯 统一消息拦截器使用示例 - 重写源码方法版本
 *
 * 展示如何使用重写源码方法的UnifiedMessageInterceptor
 * 完全按照RPG Maker MZ的原生逻辑，在关键节点插入自定义处理
 */

(() => {
    'use strict';

    // 等待拦截器加载完成
    setTimeout(() => {
        if (window.MessageInterceptor) {
            console.log('🎯 开始设置消息拦截器示例 - 重写源码版本');

            // 🔑 设置统一消息回调 - 处理所有类型的消息
            window.MessageInterceptor.onUnifiedMessage = function(messageData) {
                console.log('📨 收到统一消息:', messageData);

                // 🔑 根据消息类型进行统一处理
                if (messageData.isScrollText) {
                    // 滚动文本（独立显示，与普通消息互斥）
                    handleScrollText(messageData);
                } else if (messageData.hasInteractiveUI) {
                    // 有交互UI的消息（选择项、数值输入、物品选择）
                    handleInteractiveMessage(messageData);
                } else if (messageData.hasText) {
                    // 纯文本消息（可能包含依赖UI：姓名框、金钱窗口）
                    handleTextOnlyMessage(messageData);
                }
            };

            console.log('✅ 消息拦截器示例设置完成 - 重写源码版本');
        } else {
            console.warn('⚠️ MessageInterceptor 未找到');
        }
    }, 500);

    /**
     * 🎯 处理滚动文本 - 独立显示
     */
    function handleScrollText(messageData) {
        console.log('📜 处理滚动文本:', messageData.scrollTextData);

        // 创建自定义滚动文本UI
        createCustomScrollTextUI(messageData.scrollTextData);

        // 模拟滚动文本播放完成
        setTimeout(() => {
            console.log('📜 滚动文本播放完成');
            window.MessageInterceptor.completeScrollText(); // 调用专用的滚动文本完成方法
        }, 3000);
    }

    /**
     * 🎯 处理有交互UI的消息 - 文本+交互UI一体化显示
     */
    function handleInteractiveMessage(messageData) {
        console.log('🎮 处理交互消息:', messageData.activeInteractiveUI);

        // 🔑 一体化显示：文本内容 + 依赖UI + 交互UI
        const unifiedUI = createUnifiedMessageUI(messageData);

        // 根据交互UI类型添加相应的交互组件
        switch (messageData.activeInteractiveUI.type) {
            case 'choice':
                addChoiceInteraction(unifiedUI, messageData.activeInteractiveUI.data);
                break;
            case 'numberInput':
                addNumberInputInteraction(unifiedUI, messageData.activeInteractiveUI.data);
                break;
            case 'itemChoice':
                addItemChoiceInteraction(unifiedUI, messageData.activeInteractiveUI.data);
                break;
        }
    }

    /**
     * 🎯 处理纯文本消息 - 文本+依赖UI一体化显示
     */
    function handleTextOnlyMessage(messageData) {
        console.log('📝 处理纯文本消息:', messageData.allText);

        // 🔑 一体化显示：文本内容 + 依赖UI（姓名框、金钱窗口）
        const unifiedUI = createUnifiedMessageUI(messageData);

        // 添加点击确认功能
        addClickToConfirm(unifiedUI);
    }

    /**
     * 🎯 创建统一消息UI - 一体化显示所有组件
     */
    function createUnifiedMessageUI(messageData) {
        console.log('🎨 创建统一消息UI:', {
            text: messageData.allText,
            face: messageData.faceName ? `${messageData.faceName}[${messageData.faceIndex}]` : 'none',
            background: messageData.background,
            position: messageData.positionType,
            hasNameBox: messageData.dependentUI.hasNameBox,
            hasGold: messageData.dependentUI.hasGold
        });

        // 🔑 创建统一的UI容器
        const unifiedUI = {
            container: null, // 主容器
            textArea: null,  // 文本显示区域
            nameBox: null,   // 姓名框
            goldBox: null,   // 金钱窗口
            interactionArea: null // 交互区域
        };

        // 创建主容器
        // unifiedUI.container = createMainContainer(messageData);

        // 创建文本显示区域
        // unifiedUI.textArea = createTextArea(messageData);

        // 创建姓名框（如果需要）
        if (messageData.dependentUI.hasNameBox) {
            console.log('🏷️ 添加姓名框:', messageData.speakerName);
            // unifiedUI.nameBox = createNameBox(messageData.speakerName);
        }

        // 创建金钱窗口（如果需要）
        if (messageData.dependentUI.hasGold) {
            console.log('💰 添加金钱窗口:', messageData.dependentUI.goldAmount);
            // unifiedUI.goldBox = createGoldBox(messageData.dependentUI.goldAmount);
        }

        return unifiedUI;
    }

    /**
     * 🎯 创建自定义滚动文本UI
     */
    function createCustomScrollTextUI(scrollTextData) {
        console.log('📜 创建滚动文本UI:', scrollTextData);

        // 创建滚动文本显示组件
        // ... 你的自定义滚动文本UI代码 ...
    }

    /**
     * 🎯 显示选择项UI
     */
    function displayChoiceUI(choiceData) {
        console.log('🔘 显示选择项:', choiceData);

        // 创建自定义选择项UI
        // ... 你的自定义UI代码 ...

        // 模拟用户选择
        setTimeout(() => {
            const selectedIndex = 0; // 模拟选择第一项
            window.MessageInterceptor.handleChoiceSelected(selectedIndex);
        }, 2000);
    }

    /**
     * 🎯 显示数值输入UI
     */
    function displayNumberInputUI(numberInputData) {
        console.log('🔢 显示数值输入:', numberInputData);

        // 创建自定义数值输入UI
        // ... 你的自定义UI代码 ...

        // 模拟用户输入
        setTimeout(() => {
            const inputValue = 123; // 模拟输入值
            window.MessageInterceptor.handleNumberInputCompleted(inputValue, numberInputData.variableId);
        }, 2000);
    }

    /**
     * 🎯 显示物品选择UI
     */
    function displayItemChoiceUI(itemChoiceData) {
        console.log('🎒 显示物品选择:', itemChoiceData);

        // 创建自定义物品选择UI
        // ... 你的自定义UI代码 ...

        // 模拟用户选择
        setTimeout(() => {
            const selectedItemId = 1; // 模拟选择物品ID
            window.MessageInterceptor.handleItemChoiceCompleted(selectedItemId, itemChoiceData.variableId);
        }, 2000);
    }

})();
