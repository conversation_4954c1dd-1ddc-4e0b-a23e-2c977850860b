/**
 * 🎯 统一消息拦截器使用示例
 * 
 * 展示如何使用重新设计的UnifiedMessageInterceptor
 * 按照RPG Maker MZ的原生逻辑处理消息和UI组件
 */

(() => {
    'use strict';

    // 等待拦截器加载完成
    setTimeout(() => {
        if (window.MessageInterceptor) {
            console.log('🎯 开始设置消息拦截器示例');

            // 🔑 设置统一消息回调
            window.MessageInterceptor.onUnifiedMessage = function(messageData) {
                console.log('📨 收到统一消息:', messageData);

                // 🔑 根据消息类型进行处理
                if (messageData.isScrollText) {
                    // 滚动文本（独立显示）
                    handleScrollText(messageData);
                } else if (messageData.hasInteractiveUI) {
                    // 有交互UI的消息
                    handleInteractiveMessage(messageData);
                } else {
                    // 纯文本消息（可能有依赖UI如姓名框、金钱窗口）
                    handleTextOnlyMessage(messageData);
                }
            };

            console.log('✅ 消息拦截器示例设置完成');
        } else {
            console.warn('⚠️ MessageInterceptor 未找到');
        }
    }, 500);

    /**
     * 🎯 处理滚动文本
     */
    function handleScrollText(messageData) {
        console.log('📜 处理滚动文本:', messageData.scrollTextData);
        
        // 创建自定义滚动文本UI
        // ... 你的自定义UI代码 ...
        
        // 模拟处理完成
        setTimeout(() => {
            window.MessageInterceptor.completeMessage();
        }, 3000);
    }

    /**
     * 🎯 处理有交互UI的消息
     */
    function handleInteractiveMessage(messageData) {
        console.log('🎮 处理交互消息:', messageData.activeInteractiveUI);

        // 显示文本内容
        displayMessageText(messageData);

        // 显示依赖UI（姓名框、金钱窗口）
        displayDependentUI(messageData);

        // 根据交互UI类型显示相应的UI
        switch (messageData.activeInteractiveUI.type) {
            case 'choice':
                displayChoiceUI(messageData.activeInteractiveUI.data);
                break;
            case 'numberInput':
                displayNumberInputUI(messageData.activeInteractiveUI.data);
                break;
            case 'itemChoice':
                displayItemChoiceUI(messageData.activeInteractiveUI.data);
                break;
        }
    }

    /**
     * 🎯 处理纯文本消息
     */
    function handleTextOnlyMessage(messageData) {
        console.log('📝 处理纯文本消息:', messageData.allText);

        // 显示文本内容
        displayMessageText(messageData);

        // 显示依赖UI（姓名框、金钱窗口）
        displayDependentUI(messageData);

        // 模拟用户点击确认
        setTimeout(() => {
            window.MessageInterceptor.completeMessage();
        }, 2000);
    }

    /**
     * 🎯 显示消息文本
     */
    function displayMessageText(messageData) {
        console.log('📄 显示文本:', {
            text: messageData.allText,
            face: messageData.faceName ? `${messageData.faceName}[${messageData.faceIndex}]` : 'none',
            background: messageData.background,
            position: messageData.positionType
        });

        // 创建自定义文本显示UI
        // ... 你的自定义UI代码 ...
    }

    /**
     * 🎯 显示依赖UI（姓名框、金钱窗口）
     */
    function displayDependentUI(messageData) {
        if (messageData.dependentUI.hasNameBox) {
            console.log('🏷️ 显示姓名框:', messageData.speakerName);
            // 创建自定义姓名框UI
            // ... 你的自定义UI代码 ...
        }

        if (messageData.dependentUI.hasGold) {
            console.log('💰 显示金钱窗口:', messageData.dependentUI.goldAmount);
            // 创建自定义金钱窗口UI
            // ... 你的自定义UI代码 ...
        }
    }

    /**
     * 🎯 显示选择项UI
     */
    function displayChoiceUI(choiceData) {
        console.log('🔘 显示选择项:', choiceData);

        // 创建自定义选择项UI
        // ... 你的自定义UI代码 ...

        // 模拟用户选择
        setTimeout(() => {
            const selectedIndex = 0; // 模拟选择第一项
            window.MessageInterceptor.handleChoiceSelected(selectedIndex);
        }, 2000);
    }

    /**
     * 🎯 显示数值输入UI
     */
    function displayNumberInputUI(numberInputData) {
        console.log('🔢 显示数值输入:', numberInputData);

        // 创建自定义数值输入UI
        // ... 你的自定义UI代码 ...

        // 模拟用户输入
        setTimeout(() => {
            const inputValue = 123; // 模拟输入值
            window.MessageInterceptor.handleNumberInputCompleted(inputValue, numberInputData.variableId);
        }, 2000);
    }

    /**
     * 🎯 显示物品选择UI
     */
    function displayItemChoiceUI(itemChoiceData) {
        console.log('🎒 显示物品选择:', itemChoiceData);

        // 创建自定义物品选择UI
        // ... 你的自定义UI代码 ...

        // 模拟用户选择
        setTimeout(() => {
            const selectedItemId = 1; // 模拟选择物品ID
            window.MessageInterceptor.handleItemChoiceCompleted(selectedItemId, itemChoiceData.variableId);
        }, 2000);
    }

})();
