/**
 * 🎯 Game_Interpreter消息拦截器 - 专门针对删除内置UI的情况
 * 
 * 核心策略：
 * 1. 如果重写了Scene_Map.createAllWindows()，Window_Message不会被创建
 * 2. 直接拦截Game_Interpreter的消息相关命令，在源头处理
 * 3. 阻止原生的消息处理流程，完全由自定义UI接管
 */

(() => {
    'use strict';

    /**
     * 🔧 Game_Interpreter消息拦截器
     */
    class GameInterpreterMessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;
            
            // 🔑 统一消息回调
            this._onUnifiedMessage = null;
            
            // 🔑 原生方法备份
            this.originalCommand101 = null; // Show Text
            this.originalCommand102 = null; // Show Choices
            this.originalCommand103 = null; // Input Number
            this.originalCommand104 = null; // Select Item
            this.originalCommand105 = null; // Show Scrolling Text
            
            // 🔑 当前消息数据
            this.currentMessageData = null;
        }

        // 🔑 设置统一消息回调
        set onUnifiedMessage(callback) {
            console.log('🔧 GameInterpreterMessageInterceptor: 设置统一消息回调');
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截 - 拦截Game_Interpreter的消息命令
         */
        setupInterception() {
            setTimeout(() => {
                if (typeof Game_Interpreter === 'undefined') {
                    console.log('GameInterpreterMessageInterceptor: Game_Interpreter 尚未加载，延迟设置拦截');
                    setTimeout(() => {
                        this.isSetup = false;
                        this.ensureSetup();
                    }, 100);
                    return;
                }

                if (this.isSetup) {
                    return; // 避免重复设置
                }

                // 🔑 备份原生方法
                this.originalCommand101 = Game_Interpreter.prototype.command101;
                this.originalCommand102 = Game_Interpreter.prototype.command102;
                this.originalCommand103 = Game_Interpreter.prototype.command103;
                this.originalCommand104 = Game_Interpreter.prototype.command104;
                this.originalCommand105 = Game_Interpreter.prototype.command105;
                const self = this;

                this.isSetup = true;

                // 🔑 拦截显示文本命令
                Game_Interpreter.prototype.command101 = function(params) {
                    if (self.isEnabled) {
                        return self.handleShowText(this, params);
                    } else {
                        return self.originalCommand101.call(this, params);
                    }
                };

                // 🔑 拦截显示选择项命令
                Game_Interpreter.prototype.command102 = function(params) {
                    if (self.isEnabled) {
                        return self.handleShowChoices(this, params);
                    } else {
                        return self.originalCommand102.call(this, params);
                    }
                };

                // 🔑 拦截数值输入命令
                Game_Interpreter.prototype.command103 = function(params) {
                    if (self.isEnabled) {
                        return self.handleInputNumber(this, params);
                    } else {
                        return self.originalCommand103.call(this, params);
                    }
                };

                // 🔑 拦截物品选择命令
                Game_Interpreter.prototype.command104 = function(params) {
                    if (self.isEnabled) {
                        return self.handleSelectItem(this, params);
                    } else {
                        return self.originalCommand104.call(this, params);
                    }
                };

                // 🔑 拦截滚动文本命令
                Game_Interpreter.prototype.command105 = function(params) {
                    if (self.isEnabled) {
                        return self.handleScrollingText(this, params);
                    } else {
                        return self.originalCommand105.call(this, params);
                    }
                };

                console.log('✅ Game_Interpreter消息拦截器已启用');
            }, 100);
        }

        /**
         * 🎯 处理显示文本命令
         */
        handleShowText(interpreter, params) {
            console.log('🎯 拦截显示文本命令:', params);

            // 检查$gameMessage是否忙碌
            if (window.$gameMessage && window.$gameMessage.isBusy()) {
                return false;
            }

            // 🔑 按照原生逻辑设置消息数据
            if (window.$gameMessage) {
                window.$gameMessage.setFaceImage(params[0], params[1]);
                window.$gameMessage.setBackground(params[2]);
                window.$gameMessage.setPositionType(params[3]);
                window.$gameMessage.setSpeakerName(params[4]);

                // 添加文本内容
                while (interpreter.nextEventCode() === 401) {
                    interpreter._index++;
                    window.$gameMessage.add(interpreter.currentCommand().parameters[0]);
                }

                // 🔑 检查后续的交互命令
                let hasInteractiveUI = false;
                let interactiveUIData = null;

                switch (interpreter.nextEventCode()) {
                    case 102: // Show Choices
                        interpreter._index++;
                        const choiceParams = interpreter.currentCommand().parameters;
                        this.setupChoices(choiceParams, interpreter);
                        hasInteractiveUI = true;
                        interactiveUIData = {
                            type: 'choice',
                            data: {
                                choices: choiceParams[0].clone(),
                                defaultType: choiceParams.length > 2 ? choiceParams[2] : 0,
                                cancelType: choiceParams[1] < choiceParams[0].length ? choiceParams[1] : -2,
                                positionType: choiceParams.length > 3 ? choiceParams[3] : 2,
                                background: choiceParams.length > 4 ? choiceParams[4] : 0
                            }
                        };
                        break;
                    case 103: // Input Number
                        interpreter._index++;
                        const numParams = interpreter.currentCommand().parameters;
                        window.$gameMessage.setNumberInput(numParams[0], numParams[1]);
                        hasInteractiveUI = true;
                        interactiveUIData = {
                            type: 'numberInput',
                            data: {
                                variableId: numParams[0],
                                maxDigits: numParams[1],
                                currentValue: window.$gameVariables ? window.$gameVariables.value(numParams[0]) : 0
                            }
                        };
                        break;
                    case 104: // Select Item
                        interpreter._index++;
                        const itemParams = interpreter.currentCommand().parameters;
                        window.$gameMessage.setItemChoice(itemParams[0], itemParams[1] || 2);
                        hasInteractiveUI = true;
                        interactiveUIData = {
                            type: 'itemChoice',
                            data: {
                                variableId: itemParams[0],
                                itemType: itemParams[1] || 2
                            }
                        };
                        break;
                }

                // 🔑 收集完整消息数据并触发回调
                const messageData = {
                    type: 'unified',
                    timestamp: Date.now(),
                    
                    // 文本内容
                    allText: window.$gameMessage.allText(),
                    
                    // 消息样式
                    faceName: window.$gameMessage.faceName(),
                    faceIndex: window.$gameMessage.faceIndex(),
                    background: window.$gameMessage.background(),
                    positionType: window.$gameMessage.positionType(),
                    speakerName: window.$gameMessage.speakerName(),
                    
                    // 交互UI信息
                    hasInteractiveUI: hasInteractiveUI,
                    activeInteractiveUI: interactiveUIData,
                    
                    // 依赖UI信息
                    dependentUI: {
                        hasNameBox: !!window.$gameMessage.speakerName(),
                        hasGold: this.checkForGoldDisplay(window.$gameMessage.allText()),
                        goldAmount: window.$gameParty ? window.$gameParty.gold() : 0
                    },
                    
                    // 滚动文本
                    isScrollText: false,
                    
                    // 解释器信息
                    interpreter: interpreter
                };

                this.currentMessageData = messageData;
                this.triggerUnifiedCallback(messageData);

                // 设置等待模式
                interpreter.setWaitMode("message");
                return true;
            }

            return false;
        }

        /**
         * 🎯 设置选择项（模拟原生逻辑）
         */
        setupChoices(params, interpreter) {
            const choices = params[0].clone();
            const cancelType = params[1] < choices.length ? params[1] : -2;
            const defaultType = params.length > 2 ? params[2] : 0;
            const positionType = params.length > 3 ? params[3] : 2;
            const background = params.length > 4 ? params[4] : 0;
            
            window.$gameMessage.setChoices(choices, defaultType, cancelType);
            window.$gameMessage.setChoiceBackground(background);
            window.$gameMessage.setChoicePositionType(positionType);
            window.$gameMessage.setChoiceCallback(n => {
                interpreter._branch[interpreter._indent] = n;
            });
        }

        /**
         * 🎯 检查文本中是否包含金钱显示标记
         */
        checkForGoldDisplay(text) {
            return text && text.includes('\\$');
        }

        /**
         * 🎯 触发统一回调
         */
        triggerUnifiedCallback(messageData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    console.log('✅ GameInterpreterMessageInterceptor: 执行统一回调');
                    this.onUnifiedMessage(messageData);
                } catch (error) {
                    console.error('❌ GameInterpreterMessageInterceptor: 统一回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ GameInterpreterMessageInterceptor: 没有设置统一回调方法');
            }
        }

        /**
         * 🎯 处理选择项选中
         */
        handleChoiceSelected(index) {
            console.log('🎯 GameInterpreterMessageInterceptor: 选择项被选中:', index);

            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }
            
            this.completeMessage();
        }

        /**
         * 🎯 处理数值输入完成
         */
        handleNumberInputCompleted(value, variableId) {
            console.log('🎯 GameInterpreterMessageInterceptor: 数值输入完成:', { value, variableId });

            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, value);
            }
            
            this.completeMessage();
        }

        /**
         * 🎯 处理物品选择完成
         */
        handleItemChoiceCompleted(itemId, variableId) {
            console.log('🎯 GameInterpreterMessageInterceptor: 物品选择完成:', { itemId, variableId });

            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, itemId);
            }
            
            this.completeMessage();
        }

        /**
         * 🎯 完成消息处理
         */
        completeMessage() {
            console.log('🎯 GameInterpreterMessageInterceptor: 完成消息处理');

            if (window.$gameMessage) {
                window.$gameMessage.clear();
                console.log('✅ GameInterpreterMessageInterceptor: 消息已清除，事件可以继续');
            }
            
            this.currentMessageData = null;
        }

        // 🔑 其他命令的处理方法（暂时使用原生逻辑）
        handleShowChoices(interpreter, params) {
            return this.originalCommand102.call(interpreter, params);
        }

        handleInputNumber(interpreter, params) {
            return this.originalCommand103.call(interpreter, params);
        }

        handleSelectItem(interpreter, params) {
            return this.originalCommand104.call(interpreter, params);
        }

        handleScrollingText(interpreter, params) {
            return this.originalCommand105.call(interpreter, params);
        }
    }

    // 🚀 创建全局拦截器实例
    window.MessageInterceptor = new GameInterpreterMessageInterceptor();

    console.log('✅ Game_Interpreter消息拦截器已加载');

})();
