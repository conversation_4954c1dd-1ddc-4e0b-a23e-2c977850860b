/**
 * 🎯 Game_Interpreter消息拦截器 - 专门针对删除内置UI的情况
 *
 * 核心策略：
 * 1. 如果重写了Scene_Map.createAllWindows()，Window_Message不会被创建
 * 2. 直接拦截Game_Interpreter的消息相关命令，在源头处理
 * 3. 阻止原生的消息处理流程，完全由自定义UI接管
 * 一次消息最多包含的UI组合：

UI类型	数量限制	触发条件
文本内容	1个	必须有（command101）
交互UI	最多1个	选择项/数值输入/物品选择 三选一
姓名框	0-1个	如果设置了speakerName
金钱窗口	0-1个	如果文本包含\$转义字符
滚动文本	独立	command105，与普通消息互斥
3. 同时触发的机制和条件
核心机制：

文本优先：先处理所有文本内容（401命令）
交互UI互斥：只检查下一个命令，只能是102/103/104之一
依赖UI自动：姓名框和金钱窗口根据文本内容自动判断
const messageData = {
    type: 'unified',
    timestamp: Date.now(),

    // 文本内容（必须）
    text: {
        type: 'text',
        content: 'Hello World',
        faceName: 'Actor1',
        faceIndex: 0,
        background: 0,
        positionType: 2
    },

    // 🔑 UI组件数组
    uiComponents: [
        // 依赖UI（可能有多个）
        {
            type: 'nameBox',
            data: { speakerName: 'Hero' },
            isDependent: true  // 依赖于文本，同步显示/隐藏
        },
        {
            type: 'gold',
            data: { amount: 1000 },
            isDependent: true  // 依赖于文本
        },

        // 交互UI（最多一个）
        {
            type: 'choice',
            data: {
                choices: ['Yes', 'No'],
                defaultType: 0,
                cancelType: 1
            },
            isInteractive: true  // 需要用户交互
        }
    ],

    // 🔑 UI组件统计
    componentStats: {
        total: 3,
        dependent: 2,      // 依赖UI数量
        interactive: 1,    // 交互UI数量（0或1）
        hasText: true
    }
};
 */

(() => {
    'use strict';

    /**
     * 🔧 Game_Interpreter消息拦截器
     */
    class GameInterpreterMessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 统一消息回调
            this._onUnifiedMessage = null;

            // 🔑 原生方法备份
            this.originalCommand101 = null; // Show Text
            this.originalCommand102 = null; // Show Choices
            this.originalCommand103 = null; // Input Number
            this.originalCommand104 = null; // Select Item
            this.originalCommand105 = null; // Show Scrolling Text

            // 🔑 当前消息数据
            this.currentMessageData = null;
        }

        // 🔑 设置统一消息回调
        set onUnifiedMessage(callback) {
            console.log('🔧 GameInterpreterMessageInterceptor: 设置统一消息回调');
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截 - 拦截Game_Interpreter的消息命令
         */
        setupInterception() {
            setTimeout(() => {
                if (typeof Game_Interpreter === 'undefined') {
                    console.log('GameInterpreterMessageInterceptor: Game_Interpreter 尚未加载，延迟设置拦截');
                    setTimeout(() => {
                        this.isSetup = false;
                        this.ensureSetup();
                    }, 100);
                    return;
                }

                if (this.isSetup) {
                    return; // 避免重复设置
                }

                // 🔑 备份原生方法
                this.originalCommand101 = Game_Interpreter.prototype.command101;
                this.originalCommand102 = Game_Interpreter.prototype.command102;
                this.originalCommand103 = Game_Interpreter.prototype.command103;
                this.originalCommand104 = Game_Interpreter.prototype.command104;
                this.originalCommand105 = Game_Interpreter.prototype.command105;
                const self = this;

                this.isSetup = true;

                // 🔑 拦截显示文本命令
                Game_Interpreter.prototype.command101 = function(params) {
                    if (self.isEnabled) {
                        return self.handleShowText(this, params);
                    } else {
                        return self.originalCommand101.call(this, params);
                    }
                };

                // 🔑 拦截显示选择项命令
                Game_Interpreter.prototype.command102 = function(params) {
                    if (self.isEnabled) {
                        return self.handleShowChoices(this, params);
                    } else {
                        return self.originalCommand102.call(this, params);
                    }
                };

                // 🔑 拦截数值输入命令
                Game_Interpreter.prototype.command103 = function(params) {
                    if (self.isEnabled) {
                        return self.handleInputNumber(this, params);
                    } else {
                        return self.originalCommand103.call(this, params);
                    }
                };

                // 🔑 拦截物品选择命令
                Game_Interpreter.prototype.command104 = function(params) {
                    if (self.isEnabled) {
                        return self.handleSelectItem(this, params);
                    } else {
                        return self.originalCommand104.call(this, params);
                    }
                };

                // 🔑 拦截滚动文本命令
                Game_Interpreter.prototype.command105 = function(params) {
                    if (self.isEnabled) {
                        return self.handleScrollingText(this, params);
                    } else {
                        return self.originalCommand105.call(this, params);
                    }
                };

                console.log('✅ Game_Interpreter消息拦截器已启用');
            }, 100);
        }

        /**
         * 🎯 处理显示文本命令 - 使用UI组件数组方案
         */
        handleShowText(interpreter, params) {
            console.log('🎯 拦截显示文本命令:', params);

            // 检查$gameMessage是否忙碌
            if (window.$gameMessage && window.$gameMessage.isBusy()) {
                return false;
            }

            // 🔑 按照原生逻辑设置消息数据
            if (window.$gameMessage) {
                window.$gameMessage.setFaceImage(params[0], params[1]);
                window.$gameMessage.setBackground(params[2]);
                window.$gameMessage.setPositionType(params[3]);
                window.$gameMessage.setSpeakerName(params[4]);

                // 添加文本内容
                while (interpreter.nextEventCode() === 401) {
                    interpreter._index++;
                    window.$gameMessage.add(interpreter.currentCommand().parameters[0]);
                }

                // 🔑 构建UI组件数组
                const uiComponents = [];

                // 1. 文本组件（必须）
                const rawText = window.$gameMessage.allText();
                const textComponent = {
                    type: 'text',
                    data: {
                        content: rawText,
                        faceName: window.$gameMessage.faceName(),
                        faceIndex: window.$gameMessage.faceIndex(),
                        background: window.$gameMessage.background(),
                        positionType: window.$gameMessage.positionType(),
                        speakerName: window.$gameMessage.speakerName(),
                        // 🔑 新增：RTL语言支持
                        isRTL: this.checkRTLText(rawText),
                        // 🔑 新增：转义字符信息
                        escapeCharacters: this.extractEscapeCharacters(rawText),
                        // 🔑 新增：消息继续标志
                        doesContinue: this.checkMessageContinue()
                    },
                    // 🔑 位置信息
                    position: {
                        type: 'message',
                        vertical: this.getVerticalPositionString(window.$gameMessage.positionType()), // 'top', 'center', 'bottom'
                        horizontal: 'center', // 消息窗口总是居中
                        background: this.getBackgroundString(window.$gameMessage.background()) // 'window', 'dim', 'transparent'
                    },
                    isDependent: false,
                    isInteractive: false,
                    isRequired: true
                };
                uiComponents.push(textComponent);

                // 2. 依赖UI组件
                // 姓名框
                if (window.$gameMessage.speakerName()) {
                    uiComponents.push({
                        type: 'nameBox',
                        data: {
                            speakerName: window.$gameMessage.speakerName()
                        },
                        // 🔑 位置信息 - 姓名框依赖于消息窗口位置
                        position: {
                            type: 'nameBox',
                            relativeTo: 'message', // 相对于消息窗口
                            horizontal: window.$gameMessage.isRTL() ? 'right' : 'left', // RTL语言右对齐
                            vertical: window.$gameMessage.positionType() === 0 ? 'below' : 'above', // 消息在上时显示在下方
                            background: this.getBackgroundString(window.$gameMessage.background())
                        },
                        isDependent: true,
                        isInteractive: false,
                        isRequired: false
                    });
                }

                // 金钱窗口
                if (this.checkForGoldDisplay(window.$gameMessage.allText())) {
                    uiComponents.push({
                        type: 'gold',
                        data: {
                            amount: window.$gameParty ? window.$gameParty.gold() : 0
                        },
                        // 🔑 位置信息 - 金钱窗口位置
                        position: {
                            type: 'gold',
                            relativeTo: 'message', // 相对于消息窗口
                            horizontal: 'right', // 总是右对齐
                            vertical: window.$gameMessage.positionType() === 0 ? 'top' : 'bottom', // 消息在上时显示在顶部
                            background: 'window' // 金钱窗口总是窗口背景
                        },
                        isDependent: true,
                        isInteractive: false,
                        isRequired: false
                    });
                }

                // 3. 交互UI组件（最多一个）
                switch (interpreter.nextEventCode()) {
                    case 102: // Show Choices
                        interpreter._index++;
                        const choiceParams = interpreter.currentCommand().parameters;
                        this.setupChoices(choiceParams, interpreter);
                        const choicePositionType = choiceParams.length > 3 ? choiceParams[3] : 2;
                        uiComponents.push({
                            type: 'choice',
                            data: {
                                choices: choiceParams[0].clone(),
                                defaultType: choiceParams.length > 2 ? choiceParams[2] : 0,
                                cancelType: choiceParams[1] < choiceParams[0].length ? choiceParams[1] : -2,
                                positionType: choicePositionType,
                                background: choiceParams.length > 4 ? choiceParams[4] : 0,
                                // 🔑 新增：选择项回调函数引用
                                hasCallback: !!window.$gameMessage._choiceCallback
                            },
                            // 🔑 位置信息 - 选择项窗口位置
                            position: {
                                type: 'choice',
                                relativeTo: 'message', // 相对于消息窗口
                                horizontal: this.getHorizontalPositionString(choicePositionType), // 'left', 'center', 'right'
                                vertical: 'auto', // 根据消息窗口位置自动调整（上方或下方）
                                background: this.getBackgroundString(choiceParams.length > 4 ? choiceParams[4] : 0)
                            },
                            isDependent: false,
                            isInteractive: true,
                            isRequired: false
                        });
                        break;
                    case 103: // Input Number
                        interpreter._index++;
                        const numParams = interpreter.currentCommand().parameters;
                        window.$gameMessage.setNumberInput(numParams[0], numParams[1]);
                        uiComponents.push({
                            type: 'numberInput',
                            data: {
                                variableId: numParams[0],
                                maxDigits: numParams[1],
                                currentValue: window.$gameVariables ? window.$gameVariables.value(numParams[0]) : 0
                            },
                            // 🔑 位置信息 - 数值输入窗口位置
                            position: {
                                type: 'numberInput',
                                relativeTo: 'message', // 相对于消息窗口
                                horizontal: 'center', // 总是居中
                                vertical: 'auto', // 根据消息窗口位置自动调整（上方或下方）
                                background: 'window' // 数值输入窗口总是窗口背景
                            },
                            isDependent: false,
                            isInteractive: true,
                            isRequired: false
                        });
                        break;
                    case 104: // Select Item
                        interpreter._index++;
                        const itemParams = interpreter.currentCommand().parameters;
                        window.$gameMessage.setItemChoice(itemParams[0], itemParams[1] || 2);
                        uiComponents.push({
                            type: 'itemChoice',
                            data: {
                                variableId: itemParams[0],
                                itemType: itemParams[1] || 2
                            },
                            // 🔑 位置信息 - 物品选择窗口位置
                            position: {
                                type: 'itemChoice',
                                relativeTo: 'message', // 相对于消息窗口
                                horizontal: 'center', // 总是居中
                                vertical: 'opposite', // 与消息窗口相对（消息在上时显示在下，消息在下时显示在上）
                                background: 'window', // 物品选择窗口总是窗口背景
                                fullWidth: true // 物品选择窗口通常占据全宽
                            },
                            isDependent: false,
                            isInteractive: true,
                            isRequired: false
                        });
                        break;
                }

                // 🔑 构建完整的消息数据
                const messageData = {
                    type: 'unified',
                    timestamp: Date.now(),

                    // 🔑 UI组件数组
                    uiComponents: uiComponents,

                    // 🔑 UI组件统计
                    componentStats: {
                        total: uiComponents.length,
                        dependent: uiComponents.filter(c => c.isDependent).length,
                        interactive: uiComponents.filter(c => c.isInteractive).length,
                        hasText: true,
                        hasInteractive: uiComponents.some(c => c.isInteractive)
                    },

                    // 解释器信息
                    interpreter: interpreter
                };

                this.currentMessageData = messageData;
                this.triggerUnifiedCallback(messageData);

                // 设置等待模式
                interpreter.setWaitMode("message");
                return true;
            }

            return false;
        }

        /**
         * 🎯 设置选择项（模拟原生逻辑）
         */
        setupChoices(params, interpreter) {
            const choices = params[0].clone();
            const cancelType = params[1] < choices.length ? params[1] : -2;
            const defaultType = params.length > 2 ? params[2] : 0;
            const positionType = params.length > 3 ? params[3] : 2;
            const background = params.length > 4 ? params[4] : 0;

            window.$gameMessage.setChoices(choices, defaultType, cancelType);
            window.$gameMessage.setChoiceBackground(background);
            window.$gameMessage.setChoicePositionType(positionType);
            window.$gameMessage.setChoiceCallback(n => {
                interpreter._branch[interpreter._indent] = n;
            });
        }

        /**
         * 🎯 检查文本中是否包含金钱显示标记
         */
        checkForGoldDisplay(text) {
            return text && text.includes('\\$');
        }

        /**
         * 🎯 将数字位置类型转换为字符串 - 垂直位置
         */
        getVerticalPositionString(positionType) {
            switch (positionType) {
                case 0: return 'top';
                case 1: return 'center';
                case 2: return 'bottom';
                default: return 'bottom';
            }
        }

        /**
         * 🎯 将数字位置类型转换为字符串 - 水平位置
         */
        getHorizontalPositionString(positionType) {
            switch (positionType) {
                case 0: return 'left';
                case 1: return 'center';
                case 2: return 'right';
                default: return 'right';
            }
        }

        /**
         * 🎯 将数字背景类型转换为字符串
         */
        getBackgroundString(backgroundType) {
            switch (backgroundType) {
                case 0: return 'window';
                case 1: return 'dim';
                case 2: return 'transparent';
                default: return 'window';
            }
        }

        /**
         * 🎯 检查文本是否为RTL（从右到左）语言
         */
        checkRTLText(text) {
            // 基于RPG MZ的Utils.containsArabic逻辑
            if (!text) return false;

            // 检查是否包含阿拉伯文字符
            const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
            return arabicRegex.test(text);
        }

        /**
         * 🎯 提取文本中的转义字符信息
         */
        extractEscapeCharacters(text) {
            if (!text) return {};

            const escapeInfo = {
                hasGold: false,           // \$ 金钱显示
                variables: [],            // \V[n] 变量
                actorNames: [],           // \N[n] 角色名
                partyMembers: [],         // \P[n] 队伍成员名
                hasCurrencyUnit: false,   // \G 货币单位
                colors: [],               // \C[n] 颜色
                icons: [],                // \I[n] 图标
                waitTimes: [],            // \. \| 等待时间
                controlCodes: []          // 其他控制代码
            };

            // 检查金钱显示
            if (text.includes('\\$')) {
                escapeInfo.hasGold = true;
            }

            // 提取变量 \V[n]
            const variableMatches = text.match(/\\V\[(\d+)\]/gi);
            if (variableMatches) {
                escapeInfo.variables = variableMatches.map(match => {
                    const id = parseInt(match.match(/\d+/)[0]);
                    return {
                        id: id,
                        value: window.$gameVariables ? window.$gameVariables.value(id) : 0,
                        original: match
                    };
                });
            }

            // 提取角色名 \N[n]
            const actorMatches = text.match(/\\N\[(\d+)\]/gi);
            if (actorMatches) {
                escapeInfo.actorNames = actorMatches.map(match => {
                    const id = parseInt(match.match(/\d+/)[0]);
                    const actor = window.$gameActors ? window.$gameActors.actor(id) : null;
                    return {
                        id: id,
                        name: actor ? actor.name() : '',
                        original: match
                    };
                });
            }

            // 提取队伍成员名 \P[n]
            const partyMatches = text.match(/\\P\[(\d+)\]/gi);
            if (partyMatches) {
                escapeInfo.partyMembers = partyMatches.map(match => {
                    const index = parseInt(match.match(/\d+/)[0]);
                    const actor = window.$gameParty ? window.$gameParty._actors[index - 1] : null;
                    const actorObj = actor ? window.$gameActors.actor(actor) : null;
                    return {
                        index: index,
                        name: actorObj ? actorObj.name() : '',
                        original: match
                    };
                });
            }

            // 检查货币单位 \G
            if (text.includes('\\G')) {
                escapeInfo.hasCurrencyUnit = true;
            }

            // 提取颜色代码 \C[n]
            const colorMatches = text.match(/\\C\[(\d+)\]/gi);
            if (colorMatches) {
                escapeInfo.colors = colorMatches.map(match => {
                    const colorIndex = parseInt(match.match(/\d+/)[0]);
                    return {
                        index: colorIndex,
                        original: match
                    };
                });
            }

            // 提取图标 \I[n]
            const iconMatches = text.match(/\\I\[(\d+)\]/gi);
            if (iconMatches) {
                escapeInfo.icons = iconMatches.map(match => {
                    const iconIndex = parseInt(match.match(/\d+/)[0]);
                    return {
                        index: iconIndex,
                        original: match
                    };
                });
            }

            // 检查等待时间控制
            if (text.includes('\\.')) {
                escapeInfo.waitTimes.push({ type: 'short', frames: 15, original: '\\.' });
            }
            if (text.includes('\\|')) {
                escapeInfo.waitTimes.push({ type: 'long', frames: 60, original: '\\|' });
            }

            // 检查其他控制代码
            const controlCodes = [];
            if (text.includes('\\!')) controlCodes.push({ type: 'pause', original: '\\!' });
            if (text.includes('\\>')) controlCodes.push({ type: 'speedUp', original: '\\>' });
            if (text.includes('\\<')) controlCodes.push({ type: 'speedDown', original: '\\<' });
            if (text.includes('\\^')) controlCodes.push({ type: 'skipPause', original: '\\^' });
            escapeInfo.controlCodes = controlCodes;

            return escapeInfo;
        }

        /**
         * 🎯 检查消息是否会继续（用于判断是否需要关闭窗口）
         */
        checkMessageContinue() {
            if (!window.$gameMessage) return false;

            // 检查是否有后续文本且设置没有改变
            return window.$gameMessage.hasText() && !this.areSettingsChanged();
        }

        /**
         * 🎯 检查消息设置是否改变
         */
        areSettingsChanged() {
            if (!window.$gameMessage) return false;

            // 这里需要与当前消息的设置进行比较
            // 由于我们在拦截器中，可以简化为总是返回false
            return false;
        }

        /**
         * 🎯 触发统一回调
         */
        triggerUnifiedCallback(messageData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    console.log('✅ GameInterpreterMessageInterceptor: 执行统一回调');
                    this.onUnifiedMessage(messageData);
                } catch (error) {
                    console.error('❌ GameInterpreterMessageInterceptor: 统一回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ GameInterpreterMessageInterceptor: 没有设置统一回调方法');
            }
        }

        /**
         * 🎯 处理选择项选中 - 支持UI组件数组
         */
        handleChoiceSelected(index) {
            console.log('🎯 GameInterpreterMessageInterceptor: 选择项被选中:', index);

            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }

            this.completeMessage();
        }

        /**
         * 🎯 处理数值输入完成 - 支持UI组件数组
         */
        handleNumberInputCompleted(value) {
            console.log('🎯 GameInterpreterMessageInterceptor: 数值输入完成:', value);

            // 从当前消息数据中获取variableId
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                const numberInputComponent = this.currentMessageData.uiComponents.find(c => c.type === 'numberInput');
                if (numberInputComponent && window.$gameVariables) {
                    const variableId = numberInputComponent.data.variableId;
                    window.$gameVariables.setValue(variableId, value);
                    console.log('✅ 设置变量:', { variableId, value });
                }
            }

            this.completeMessage();
        }

        /**
         * 🎯 处理物品选择完成 - 支持UI组件数组
         */
        handleItemChoiceCompleted(itemId) {
            console.log('🎯 GameInterpreterMessageInterceptor: 物品选择完成:', itemId);

            // 从当前消息数据中获取variableId
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                const itemChoiceComponent = this.currentMessageData.uiComponents.find(c => c.type === 'itemChoice');
                if (itemChoiceComponent && window.$gameVariables) {
                    const variableId = itemChoiceComponent.data.variableId;
                    window.$gameVariables.setValue(variableId, itemId);
                    console.log('✅ 设置变量:', { variableId, itemId });
                }
            }

            this.completeMessage();
        }

        /**
         * 🎯 获取指定类型的UI组件
         */
        getUIComponent(type) {
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                return this.currentMessageData.uiComponents.find(c => c.type === type);
            }
            return null;
        }

        /**
         * 🎯 获取所有依赖UI组件
         */
        getDependentUIComponents() {
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                return this.currentMessageData.uiComponents.filter(c => c.isDependent);
            }
            return [];
        }

        /**
         * 🎯 获取交互UI组件
         */
        getInteractiveUIComponent() {
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                return this.currentMessageData.uiComponents.find(c => c.isInteractive);
            }
            return null;
        }

        /**
         * 🎯 完成消息处理
         */
        completeMessage() {
            console.log('🎯 GameInterpreterMessageInterceptor: 完成消息处理');

            if (window.$gameMessage) {
                window.$gameMessage.clear();
                console.log('✅ GameInterpreterMessageInterceptor: 消息已清除，事件可以继续');
            }

            this.currentMessageData = null;
        }

        // 🔑 其他命令的处理方法
        handleShowChoices(interpreter, params) {
            return this.originalCommand102.call(interpreter, params);
        }

        handleInputNumber(interpreter, params) {
            return this.originalCommand103.call(interpreter, params);
        }

        handleSelectItem(interpreter, params) {
            return this.originalCommand104.call(interpreter, params);
        }

        /**
         * 🎯 处理滚动文本命令 - 使用UI组件数组方案
         */
        handleScrollingText(interpreter, params) {
            console.log('🎯 拦截滚动文本命令:', params);

            // 检查$gameMessage是否忙碌
            if (window.$gameMessage && window.$gameMessage.isBusy()) {
                return false;
            }

            // 🔑 按照原生逻辑设置滚动文本
            if (window.$gameMessage) {
                window.$gameMessage.setScroll(params[0], params[1]);

                // 添加滚动文本内容
                while (interpreter.nextEventCode() === 405) {
                    interpreter._index++;
                    window.$gameMessage.add(interpreter.currentCommand().parameters[0]);
                }

                // 🔑 构建滚动文本UI组件数组
                const uiComponents = [{
                    type: 'scrollText',
                    data: {
                        content: window.$gameMessage.allText(),
                        speed: params[0],
                        noFast: params[1]
                    },
                    // 🔑 位置信息 - 滚动文本位置
                    position: {
                        type: 'scrollText',
                        relativeTo: 'screen', // 相对于屏幕
                        horizontal: 'center', // 居中
                        vertical: 'center', // 居中
                        background: 'dim', // 滚动文本总是暗化背景
                        fullScreen: true // 占据全屏
                    },
                    isDependent: false,
                    isInteractive: false,
                    isRequired: true
                }];

                // 🔑 构建滚动文本消息数据
                const messageData = {
                    type: 'unified',
                    timestamp: Date.now(),

                    // 🔑 UI组件数组
                    uiComponents: uiComponents,

                    // 🔑 UI组件统计
                    componentStats: {
                        total: 1,
                        dependent: 0,
                        interactive: 0,
                        hasText: false,
                        hasScrollText: true,
                        hasInteractive: false
                    },

                    // 解释器信息
                    interpreter: interpreter
                };

                this.currentMessageData = messageData;
                this.triggerUnifiedCallback(messageData);

                // 设置等待模式
                interpreter.setWaitMode("message");
                return true;
            }

            return false;
        }
    }

    // 🚀 创建全局拦截器实例
    window.MessageInterceptor = new GameInterpreterMessageInterceptor();
// 设置回调
MessageInterceptor.onUnifiedMessage = function(messageData) {
    console.log('处理UI组件 componentStats:', messageData.componentStats);
    // 处理UI组件数组
    messageData.uiComponents.forEach(component => {
        // 根据组件类型进行处理
        // ...
        console.log('处理UI组件:', component);
    });
};


    console.log('✅ Game_Interpreter消息拦截器已加载');

})();
