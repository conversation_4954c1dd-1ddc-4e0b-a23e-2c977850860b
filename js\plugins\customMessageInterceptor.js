



(() => {
    'use strict';

    /**
     * 🎯 统一消息系统拦截器 - 直接重写源码方法
     *
     * 核心策略：
     * 1. 重写 Window_Message.startInput() - 所有交互UI的统一入口
     * 2. 重写 Window_Message.startMessage() - 添加自定义消息处理
     * 3. 重写 Window_ScrollText.startMessage() - 处理滚动文本
     * 4. 保持原生逻辑的完整性，只在关键节点插入自定义处理
     */

    /**
     * 🔧 统一消息拦截器 - 重写源码方法
     */
    class UnifiedMessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 统一消息回调
            this._onUnifiedMessage = null;

            // 🔑 原生方法备份
            this.originalMethods = {};
        }

        // 🔑 设置统一消息回调时自动初始化拦截器
        set onUnifiedMessage(callback) {
            console.log('🔧 UnifiedMessageInterceptor: 设置统一消息回调');
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置（延迟初始化）
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截 - 重写源码关键方法
         */
        setupInterception() {
            // 延迟设置，等待相关类加载
            setTimeout(() => {
                if (typeof Window_Message === 'undefined' || typeof Window_ScrollText === 'undefined') {
                    console.log('UnifiedMessageInterceptor: 窗口类尚未加载，延迟设置拦截');
                    setTimeout(() => {
                        this.isSetup = false;
                        this.ensureSetup();
                    }, 100);
                    return;
                }

                if (this.isSetup) {
                    return; // 避免重复设置
                }

                this.isSetup = true;
                const self = this;

                // 🔑 备份原生方法
                this.originalMethods = {
                    startInput: Window_Message.prototype.startInput,
                    startMessage: Window_Message.prototype.startMessage,
                    terminateMessage: Window_Message.prototype.terminateMessage,
                    scrollTextStartMessage: Window_ScrollText.prototype.startMessage
                };

                // 🔑 重写 Window_Message.startInput - 交互UI统一入口
                Window_Message.prototype.startInput = function() {
                    if (self.isEnabled) {
                        const messageData = self.createUnifiedMessageData();

                        if (messageData.hasInteractiveUI) {
                            // 触发自定义处理
                            self.triggerUnifiedCallback(messageData);
                            return true; // 阻止原生UI
                        }
                    }
                    // 使用原生逻辑
                    return self.originalMethods.startInput.call(this);
                };

                // 🔑 重写 Window_Message.startMessage - 添加消息开始处理
                Window_Message.prototype.startMessage = function() {
                    // 先调用原生逻辑
                    self.originalMethods.startMessage.call(this);

                    if (self.isEnabled) {
                        // 检查是否为纯文本消息（无交互UI）
                        if (!$gameMessage.isChoice() && !$gameMessage.isNumberInput() && !$gameMessage.isItemChoice()) {
                            const messageData = self.createUnifiedMessageData();
                            if (!messageData.isScrollText) {
                                // 延迟触发，等待文本显示完成
                                setTimeout(() => {
                                    self.triggerUnifiedCallback(messageData);
                                }, 100);
                            }
                        }
                    }
                };

                // 🔑 重写 Window_ScrollText.startMessage - 处理滚动文本
                Window_ScrollText.prototype.startMessage = function() {
                    if (self.isEnabled) {
                        const messageData = self.createUnifiedMessageData();
                        if (messageData.isScrollText) {
                            // 触发自定义滚动文本处理
                            self.triggerUnifiedCallback(messageData);
                            return; // 阻止原生滚动文本
                        }
                    }
                    // 使用原生逻辑
                    self.originalMethods.scrollTextStartMessage.call(this);
                };

                console.log('✅ 统一消息拦截器已启用 - 重写源码方法');
            }, 100);
        }

        /**
         * 🎯 创建统一消息数据 - 核心数据收集方法
         */
        createUnifiedMessageData() {
            if (!window.$gameMessage) {
                console.warn('⚠️ $gameMessage 不存在');
                return null;
            }

            // 🔑 按照RPG MZ的优先级判断当前活跃的交互UI类型
            let activeInteractiveUI = null;
            if (window.$gameMessage.isChoice()) {
                activeInteractiveUI = {
                    type: 'choice',
                    data: {
                        choices: window.$gameMessage.choices(),
                        defaultType: window.$gameMessage.choiceDefaultType(),
                        cancelType: window.$gameMessage.choiceCancelType(),
                        background: window.$gameMessage.choiceBackground(),
                        positionType: window.$gameMessage.choicePositionType()
                    }
                };
            } else if (window.$gameMessage.isNumberInput()) {
                activeInteractiveUI = {
                    type: 'numberInput',
                    data: {
                        variableId: window.$gameMessage.numInputVariableId(),
                        maxDigits: window.$gameMessage.numInputMaxDigits(),
                        currentValue: window.$gameVariables ? window.$gameVariables.value(window.$gameMessage.numInputVariableId()) : 0
                    }
                };
            } else if (window.$gameMessage.isItemChoice()) {
                activeInteractiveUI = {
                    type: 'itemChoice',
                    data: {
                        variableId: window.$gameMessage.itemChoiceVariableId(),
                        itemType: window.$gameMessage.itemChoiceItypeId()
                    }
                };
            }

            // 🔑 创建统一消息数据结构
            const messageData = {
                // 基础信息
                type: 'unified',
                timestamp: Date.now(),

                // 文本内容
                allText: window.$gameMessage.allText(),
                hasText: window.$gameMessage.hasText(),

                // 消息样式
                faceName: window.$gameMessage.faceName(),
                faceIndex: window.$gameMessage.faceIndex(),
                background: window.$gameMessage.background(),
                positionType: window.$gameMessage.positionType(),
                speakerName: window.$gameMessage.speakerName(),

                // 🔑 交互UI信息（互斥）
                hasInteractiveUI: !!activeInteractiveUI,
                activeInteractiveUI: activeInteractiveUI,

                // 🔑 依赖UI信息（与Message窗口同步显示）
                dependentUI: {
                    hasNameBox: !!window.$gameMessage.speakerName(),
                    hasGold: this.checkForGoldDisplay(window.$gameMessage.allText()),
                    goldAmount: window.$gameParty ? window.$gameParty.gold() : 0
                },

                // 🔑 滚动文本（独立显示，与普通消息互斥）
                isScrollText: window.$gameMessage.scrollMode(),
                scrollTextData: window.$gameMessage.scrollMode() ? {
                    speed: window.$gameMessage.scrollSpeed(),
                    noFast: window.$gameMessage.scrollNoFast(),
                    text: window.$gameMessage.allText()
                } : null
            };

            console.log('🎯 UnifiedMessageInterceptor: 创建统一消息数据:', messageData);
            return messageData;
        }

        /**
         * 🎯 检查文本中是否包含金钱显示标记
         */
        checkForGoldDisplay(text) {
            return text && text.includes('\\$');
        }

        /**
         * 🎯 触发统一回调
         */
        triggerUnifiedCallback(messageData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    console.log('✅ UnifiedMessageInterceptor: 执行统一回调');
                    this.onUnifiedMessage(messageData);
                } catch (error) {
                    console.error('❌ UnifiedMessageInterceptor: 统一回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ UnifiedMessageInterceptor: 没有设置统一回调方法');
            }
        }



        /**
         * 🎯 处理选择项选中（由UI组件调用）
         */
        handleChoiceSelected(index) {
            console.log('🎯 UnifiedMessageInterceptor: 选择项被选中:', index);

            // 调用原生的选择处理
            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }

            // 完成消息处理
            this.completeMessage();
        }

        /**
         * 🎯 处理数值输入完成（由UI组件调用）
         */
        handleNumberInputCompleted(value, variableId) {
            console.log('🎯 UnifiedMessageInterceptor: 数值输入完成:', { value, variableId });

            // 设置变量值
            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, value);
            }

            // 完成消息处理
            this.completeMessage();
        }

        /**
         * 🎯 处理物品选择完成（由UI组件调用）
         */
        handleItemChoiceCompleted(itemId, variableId) {
            console.log('🎯 UnifiedMessageInterceptor: 物品选择完成:', { itemId, variableId });

            // 设置变量值
            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, itemId);
            }

            // 完成消息处理
            this.completeMessage();
        }

        /**
         * 🎯 完成消息处理（由UI组件调用）
         * 调用原生的terminateMessage方法，完全按照RPG MZ的逻辑
         */
        completeMessage() {
            console.log('🎯 UnifiedMessageInterceptor: 完成消息处理');

            // 🔑 找到当前的Message窗口并调用原生的terminateMessage
            if (typeof SceneManager !== 'undefined' && SceneManager._scene && SceneManager._scene._messageWindow) {
                const messageWindow = SceneManager._scene._messageWindow;
                if (this.originalMethods.terminateMessage) {
                    this.originalMethods.terminateMessage.call(messageWindow);
                    console.log('✅ UnifiedMessageInterceptor: 调用原生terminateMessage完成');
                } else {
                    // 备用方案：直接清除消息
                    window.$gameMessage.clear();
                    console.log('✅ UnifiedMessageInterceptor: 备用方案清除消息完成');
                }
            } else {
                // 备用方案：直接清除消息
                if (window.$gameMessage) {
                    window.$gameMessage.clear();
                    console.log('✅ UnifiedMessageInterceptor: 备用方案清除消息完成');
                }
            }
        }

        /**
         * 🎯 完成滚动文本处理（由UI组件调用）
         */
        completeScrollText() {
            console.log('🎯 UnifiedMessageInterceptor: 完成滚动文本处理');

            // 🔑 找到当前的ScrollText窗口并调用原生的terminateMessage
            if (typeof SceneManager !== 'undefined' && SceneManager._scene && SceneManager._scene._scrollTextWindow) {
                const scrollTextWindow = SceneManager._scene._scrollTextWindow;
                if (scrollTextWindow.terminateMessage) {
                    scrollTextWindow.terminateMessage();
                    console.log('✅ UnifiedMessageInterceptor: 调用原生ScrollText terminateMessage完成');
                } else {
                    // 备用方案：直接清除消息
                    window.$gameMessage.clear();
                    console.log('✅ UnifiedMessageInterceptor: 备用方案清除滚动文本完成');
                }
            } else {
                // 备用方案：直接清除消息
                if (window.$gameMessage) {
                    window.$gameMessage.clear();
                    console.log('✅ UnifiedMessageInterceptor: 备用方案清除滚动文本完成');
                }
            }
        }
    }

    // 🚀 创建全局统一拦截器实例
    window.MessageInterceptor = new UnifiedMessageInterceptor();

    console.log('✅ 统一消息拦截器已加载');

})();
