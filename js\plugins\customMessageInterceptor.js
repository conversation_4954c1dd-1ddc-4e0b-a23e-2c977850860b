/**
 * 🎯 Game_Interpreter消息拦截器 - 专门针对删除内置UI的情况
 *
 * 核心策略：
 * 1. 如果重写了Scene_Map.createAllWindows()，Window_Message不会被创建
 * 2. 直接拦截Game_Interpreter的消息相关命令，在源头处理
 * 3. 阻止原生的消息处理流程，完全由自定义UI接管
 */

(() => {
    'use strict';

    /**
     * 🔧 Game_Interpreter消息拦截器
     */
    class GameInterpreterMessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 统一消息回调
            this._onUnifiedMessage = null;

            // 🔑 原生方法备份
            this.originalCommand101 = null; // Show Text
            this.originalCommand102 = null; // Show Choices
            this.originalCommand103 = null; // Input Number
            this.originalCommand104 = null; // Select Item
            this.originalCommand105 = null; // Show Scrolling Text

            // 🔑 当前消息数据
            this.currentMessageData = null;
        }

        // 🔑 设置统一消息回调
        set onUnifiedMessage(callback) {
            console.log('🔧 GameInterpreterMessageInterceptor: 设置统一消息回调');
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截 - 拦截Game_Interpreter的消息命令
         */
        setupInterception() {
            setTimeout(() => {
                if (typeof Game_Interpreter === 'undefined') {
                    console.log('GameInterpreterMessageInterceptor: Game_Interpreter 尚未加载，延迟设置拦截');
                    setTimeout(() => {
                        this.isSetup = false;
                        this.ensureSetup();
                    }, 100);
                    return;
                }

                if (this.isSetup) {
                    return; // 避免重复设置
                }

                // 🔑 备份原生方法
                this.originalCommand101 = Game_Interpreter.prototype.command101;
                this.originalCommand102 = Game_Interpreter.prototype.command102;
                this.originalCommand103 = Game_Interpreter.prototype.command103;
                this.originalCommand104 = Game_Interpreter.prototype.command104;
                this.originalCommand105 = Game_Interpreter.prototype.command105;
                const self = this;

                this.isSetup = true;

                // 🔑 拦截显示文本命令
                Game_Interpreter.prototype.command101 = function(params) {
                    if (self.isEnabled) {
                        return self.handleShowText(this, params);
                    } else {
                        return self.originalCommand101.call(this, params);
                    }
                };

                // 🔑 拦截显示选择项命令
                Game_Interpreter.prototype.command102 = function(params) {
                    if (self.isEnabled) {
                        return self.handleShowChoices(this, params);
                    } else {
                        return self.originalCommand102.call(this, params);
                    }
                };

                // 🔑 拦截数值输入命令
                Game_Interpreter.prototype.command103 = function(params) {
                    if (self.isEnabled) {
                        return self.handleInputNumber(this, params);
                    } else {
                        return self.originalCommand103.call(this, params);
                    }
                };

                // 🔑 拦截物品选择命令
                Game_Interpreter.prototype.command104 = function(params) {
                    if (self.isEnabled) {
                        return self.handleSelectItem(this, params);
                    } else {
                        return self.originalCommand104.call(this, params);
                    }
                };

                // 🔑 拦截滚动文本命令
                Game_Interpreter.prototype.command105 = function(params) {
                    if (self.isEnabled) {
                        return self.handleScrollingText(this, params);
                    } else {
                        return self.originalCommand105.call(this, params);
                    }
                };

                console.log('✅ Game_Interpreter消息拦截器已启用');
            }, 100);
        }

        /**
         * 🎯 处理显示文本命令 - 使用UI组件数组方案
         */
        handleShowText(interpreter, params) {
            console.log('🎯 拦截显示文本命令:', params);

            // 检查$gameMessage是否忙碌
            if (window.$gameMessage && window.$gameMessage.isBusy()) {
                return false;
            }

            // 🔑 按照原生逻辑设置消息数据
            if (window.$gameMessage) {
                window.$gameMessage.setFaceImage(params[0], params[1]);
                window.$gameMessage.setBackground(params[2]);
                window.$gameMessage.setPositionType(params[3]);
                window.$gameMessage.setSpeakerName(params[4]);

                // 添加文本内容
                while (interpreter.nextEventCode() === 401) {
                    interpreter._index++;
                    window.$gameMessage.add(interpreter.currentCommand().parameters[0]);
                }

                // 🔑 构建UI组件数组
                const uiComponents = [];

                // 1. 文本组件（必须）
                const textComponent = {
                    type: 'text',
                    data: {
                        content: window.$gameMessage.allText(),
                        faceName: window.$gameMessage.faceName(),
                        faceIndex: window.$gameMessage.faceIndex(),
                        background: window.$gameMessage.background(),
                        positionType: window.$gameMessage.positionType(),
                        speakerName: window.$gameMessage.speakerName()
                    },
                    isDependent: false,
                    isInteractive: false,
                    isRequired: true
                };
                uiComponents.push(textComponent);

                // 2. 依赖UI组件
                // 姓名框
                if (window.$gameMessage.speakerName()) {
                    uiComponents.push({
                        type: 'nameBox',
                        data: {
                            speakerName: window.$gameMessage.speakerName()
                        },
                        isDependent: true,
                        isInteractive: false,
                        isRequired: false
                    });
                }

                // 金钱窗口
                if (this.checkForGoldDisplay(window.$gameMessage.allText())) {
                    uiComponents.push({
                        type: 'gold',
                        data: {
                            amount: window.$gameParty ? window.$gameParty.gold() : 0
                        },
                        isDependent: true,
                        isInteractive: false,
                        isRequired: false
                    });
                }

                // 3. 交互UI组件（最多一个）
                switch (interpreter.nextEventCode()) {
                    case 102: // Show Choices
                        interpreter._index++;
                        const choiceParams = interpreter.currentCommand().parameters;
                        this.setupChoices(choiceParams, interpreter);
                        uiComponents.push({
                            type: 'choice',
                            data: {
                                choices: choiceParams[0].clone(),
                                defaultType: choiceParams.length > 2 ? choiceParams[2] : 0,
                                cancelType: choiceParams[1] < choiceParams[0].length ? choiceParams[1] : -2,
                                positionType: choiceParams.length > 3 ? choiceParams[3] : 2,
                                background: choiceParams.length > 4 ? choiceParams[4] : 0
                            },
                            isDependent: false,
                            isInteractive: true,
                            isRequired: false
                        });
                        break;
                    case 103: // Input Number
                        interpreter._index++;
                        const numParams = interpreter.currentCommand().parameters;
                        window.$gameMessage.setNumberInput(numParams[0], numParams[1]);
                        uiComponents.push({
                            type: 'numberInput',
                            data: {
                                variableId: numParams[0],
                                maxDigits: numParams[1],
                                currentValue: window.$gameVariables ? window.$gameVariables.value(numParams[0]) : 0
                            },
                            isDependent: false,
                            isInteractive: true,
                            isRequired: false
                        });
                        break;
                    case 104: // Select Item
                        interpreter._index++;
                        const itemParams = interpreter.currentCommand().parameters;
                        window.$gameMessage.setItemChoice(itemParams[0], itemParams[1] || 2);
                        uiComponents.push({
                            type: 'itemChoice',
                            data: {
                                variableId: itemParams[0],
                                itemType: itemParams[1] || 2
                            },
                            isDependent: false,
                            isInteractive: true,
                            isRequired: false
                        });
                        break;
                }

                // 🔑 构建完整的消息数据
                const messageData = {
                    type: 'unified',
                    timestamp: Date.now(),

                    // 🔑 UI组件数组
                    uiComponents: uiComponents,

                    // 🔑 UI组件统计
                    componentStats: {
                        total: uiComponents.length,
                        dependent: uiComponents.filter(c => c.isDependent).length,
                        interactive: uiComponents.filter(c => c.isInteractive).length,
                        hasText: true,
                        hasInteractive: uiComponents.some(c => c.isInteractive)
                    },

                    // 解释器信息
                    interpreter: interpreter
                };

                this.currentMessageData = messageData;
                this.triggerUnifiedCallback(messageData);

                // 设置等待模式
                interpreter.setWaitMode("message");
                return true;
            }

            return false;
        }

        /**
         * 🎯 设置选择项（模拟原生逻辑）
         */
        setupChoices(params, interpreter) {
            const choices = params[0].clone();
            const cancelType = params[1] < choices.length ? params[1] : -2;
            const defaultType = params.length > 2 ? params[2] : 0;
            const positionType = params.length > 3 ? params[3] : 2;
            const background = params.length > 4 ? params[4] : 0;

            window.$gameMessage.setChoices(choices, defaultType, cancelType);
            window.$gameMessage.setChoiceBackground(background);
            window.$gameMessage.setChoicePositionType(positionType);
            window.$gameMessage.setChoiceCallback(n => {
                interpreter._branch[interpreter._indent] = n;
            });
        }

        /**
         * 🎯 检查文本中是否包含金钱显示标记
         */
        checkForGoldDisplay(text) {
            return text && text.includes('\\$');
        }

        /**
         * 🎯 触发统一回调
         */
        triggerUnifiedCallback(messageData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    console.log('✅ GameInterpreterMessageInterceptor: 执行统一回调');
                    this.onUnifiedMessage(messageData);
                } catch (error) {
                    console.error('❌ GameInterpreterMessageInterceptor: 统一回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ GameInterpreterMessageInterceptor: 没有设置统一回调方法');
            }
        }

        /**
         * 🎯 处理选择项选中 - 支持UI组件数组
         */
        handleChoiceSelected(index) {
            console.log('🎯 GameInterpreterMessageInterceptor: 选择项被选中:', index);

            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }

            this.completeMessage();
        }

        /**
         * 🎯 处理数值输入完成 - 支持UI组件数组
         */
        handleNumberInputCompleted(value) {
            console.log('🎯 GameInterpreterMessageInterceptor: 数值输入完成:', value);

            // 从当前消息数据中获取variableId
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                const numberInputComponent = this.currentMessageData.uiComponents.find(c => c.type === 'numberInput');
                if (numberInputComponent && window.$gameVariables) {
                    const variableId = numberInputComponent.data.variableId;
                    window.$gameVariables.setValue(variableId, value);
                    console.log('✅ 设置变量:', { variableId, value });
                }
            }

            this.completeMessage();
        }

        /**
         * 🎯 处理物品选择完成 - 支持UI组件数组
         */
        handleItemChoiceCompleted(itemId) {
            console.log('🎯 GameInterpreterMessageInterceptor: 物品选择完成:', itemId);

            // 从当前消息数据中获取variableId
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                const itemChoiceComponent = this.currentMessageData.uiComponents.find(c => c.type === 'itemChoice');
                if (itemChoiceComponent && window.$gameVariables) {
                    const variableId = itemChoiceComponent.data.variableId;
                    window.$gameVariables.setValue(variableId, itemId);
                    console.log('✅ 设置变量:', { variableId, itemId });
                }
            }

            this.completeMessage();
        }

        /**
         * 🎯 获取指定类型的UI组件
         */
        getUIComponent(type) {
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                return this.currentMessageData.uiComponents.find(c => c.type === type);
            }
            return null;
        }

        /**
         * 🎯 获取所有依赖UI组件
         */
        getDependentUIComponents() {
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                return this.currentMessageData.uiComponents.filter(c => c.isDependent);
            }
            return [];
        }

        /**
         * 🎯 获取交互UI组件
         */
        getInteractiveUIComponent() {
            if (this.currentMessageData && this.currentMessageData.uiComponents) {
                return this.currentMessageData.uiComponents.find(c => c.isInteractive);
            }
            return null;
        }

        /**
         * 🎯 完成消息处理
         */
        completeMessage() {
            console.log('🎯 GameInterpreterMessageInterceptor: 完成消息处理');

            if (window.$gameMessage) {
                window.$gameMessage.clear();
                console.log('✅ GameInterpreterMessageInterceptor: 消息已清除，事件可以继续');
            }

            this.currentMessageData = null;
        }

        // 🔑 其他命令的处理方法
        handleShowChoices(interpreter, params) {
            return this.originalCommand102.call(interpreter, params);
        }

        handleInputNumber(interpreter, params) {
            return this.originalCommand103.call(interpreter, params);
        }

        handleSelectItem(interpreter, params) {
            return this.originalCommand104.call(interpreter, params);
        }

        /**
         * 🎯 处理滚动文本命令 - 使用UI组件数组方案
         */
        handleScrollingText(interpreter, params) {
            console.log('🎯 拦截滚动文本命令:', params);

            // 检查$gameMessage是否忙碌
            if (window.$gameMessage && window.$gameMessage.isBusy()) {
                return false;
            }

            // 🔑 按照原生逻辑设置滚动文本
            if (window.$gameMessage) {
                window.$gameMessage.setScroll(params[0], params[1]);

                // 添加滚动文本内容
                while (interpreter.nextEventCode() === 405) {
                    interpreter._index++;
                    window.$gameMessage.add(interpreter.currentCommand().parameters[0]);
                }

                // 🔑 构建滚动文本UI组件数组
                const uiComponents = [{
                    type: 'scrollText',
                    data: {
                        content: window.$gameMessage.allText(),
                        speed: params[0],
                        noFast: params[1]
                    },
                    isDependent: false,
                    isInteractive: false,
                    isRequired: true
                }];

                // 🔑 构建滚动文本消息数据
                const messageData = {
                    type: 'unified',
                    timestamp: Date.now(),

                    // 🔑 UI组件数组
                    uiComponents: uiComponents,

                    // 🔑 UI组件统计
                    componentStats: {
                        total: 1,
                        dependent: 0,
                        interactive: 0,
                        hasText: false,
                        hasScrollText: true,
                        hasInteractive: false
                    },

                    // 解释器信息
                    interpreter: interpreter
                };

                this.currentMessageData = messageData;
                this.triggerUnifiedCallback(messageData);

                // 设置等待模式
                interpreter.setWaitMode("message");
                return true;
            }

            return false;
        }
    }

    // 🚀 创建全局拦截器实例
    window.MessageInterceptor = new GameInterpreterMessageInterceptor();
// 设置回调
MessageInterceptor.onUnifiedMessage = function(messageData) {
    console.log('处理UI组件 componentStats:', messageData.componentStats);
    // 处理UI组件数组
    messageData.uiComponents.forEach(component => {
        // 根据组件类型进行处理
        // ...
        console.log('处理UI组件:', component);
    });
};


    console.log('✅ Game_Interpreter消息拦截器已加载');

})();
