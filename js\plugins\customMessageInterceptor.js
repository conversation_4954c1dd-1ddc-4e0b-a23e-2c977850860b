



(() => {
    'use strict';

    /**
     * 🎯 统一消息系统拦截器 - 按照RPG MZ原生逻辑重新设计
     *
     * 核心发现：
     * 1. RPG MZ中同一时间只能有一个交互UI活跃（选择项、数值输入、物品选择互斥）
     * 2. Message窗口先显示文本，文本显示完毕后才启动交互UI
     * 3. NameBox和Gold是依赖型UI，与Message窗口同步显示
     * 4. 关键拦截点：Window_Message.startInput() - 这是所有交互UI的统一入口
     *
     * 新策略：
     * 1. 拦截 Window_Message.startInput() 方法
     * 2. 在这个时机收集完整的消息数据（文本已显示完毕，交互UI即将启动）
     * 3. 通过统一回调派发，包含当前活跃的交互UI类型
     * 4. UI组件处理完成后调用原生的terminateMessage()
     */

    /**
     * 🔧 统一消息拦截器 - 按照RPG MZ原生逻辑
     */
    class UnifiedMessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 统一消息回调 - 在交互UI即将启动时触发
            this._onUnifiedMessage = null;

            // 🔑 原生方法备份
            this.originalStartInput = null;
            this.originalTerminateMessage = null;
        }

        // 🔑 设置统一消息回调时自动初始化拦截器
        set onUnifiedMessage(callback) {
            console.log('🔧 UnifiedMessageInterceptor: 设置统一消息回调');
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置（延迟初始化）
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截 - 拦截Window_Message的关键方法
         */
        setupInterception() {
            // 延迟设置，等待Window_Message类加载
            setTimeout(() => {
                if (typeof Window_Message === 'undefined') {
                    console.log('UnifiedMessageInterceptor: Window_Message 尚未加载，延迟设置拦截');
                    setTimeout(() => {
                        this.isSetup = false;
                        this.ensureSetup();
                    }, 100);
                    return;
                }

                if (this.isSetup) {
                    return; // 避免重复设置
                }

                // 🔑 拦截 Window_Message.startInput - 这是所有交互UI的统一入口
                this.originalStartInput = Window_Message.prototype.startInput;
                this.originalTerminateMessage = Window_Message.prototype.terminateMessage;
                const self = this;

                this.isSetup = true;

                // 🔑 拦截startInput方法
                Window_Message.prototype.startInput = function() {
                    if (self.isEnabled) {
                        // 🔑 在交互UI即将启动时收集完整消息数据
                        const messageData = self.collectMessageData(this);

                        if (messageData.hasInteractiveUI) {
                            // 有交互UI，触发自定义处理
                            self.triggerUnifiedCallback(messageData);
                            return true; // 阻止原生UI显示
                        } else {
                            // 没有交互UI，使用原生逻辑
                            return self.originalStartInput.call(this);
                        }
                    } else {
                        // 使用原生系统
                        return self.originalStartInput.call(this);
                    }
                };

                console.log('✅ 统一消息拦截器已启用 - 拦截Window_Message.startInput');
            }, 100);
        }

        /**
         * 🎯 收集消息数据 - 在startInput时机收集
         */
        collectMessageData(messageWindow) {
            if (!window.$gameMessage) {
                console.warn('⚠️ $gameMessage 不存在');
                return null;
            }

            // 🔑 按照RPG MZ的优先级判断当前活跃的交互UI类型
            let activeInteractiveUI = null;
            if (window.$gameMessage.isChoice()) {
                activeInteractiveUI = {
                    type: 'choice',
                    data: {
                        choices: window.$gameMessage.choices(),
                        defaultType: window.$gameMessage.choiceDefaultType(),
                        cancelType: window.$gameMessage.choiceCancelType(),
                        background: window.$gameMessage.choiceBackground(),
                        positionType: window.$gameMessage.choicePositionType()
                    }
                };
            } else if (window.$gameMessage.isNumberInput()) {
                activeInteractiveUI = {
                    type: 'numberInput',
                    data: {
                        variableId: window.$gameMessage.numInputVariableId(),
                        maxDigits: window.$gameMessage.numInputMaxDigits(),
                        currentValue: window.$gameVariables ? window.$gameVariables.value(window.$gameMessage.numInputVariableId()) : 0
                    }
                };
            } else if (window.$gameMessage.isItemChoice()) {
                activeInteractiveUI = {
                    type: 'itemChoice',
                    data: {
                        variableId: window.$gameMessage.itemChoiceVariableId(),
                        itemType: window.$gameMessage.itemChoiceItypeId()
                    }
                };
            }

            // 🔑 收集完整的消息数据
            const messageData = {
                // 基础信息
                type: 'unified',
                timestamp: Date.now(),

                // 文本内容（此时已显示完毕）
                allText: window.$gameMessage.allText(),

                // 消息样式
                faceName: window.$gameMessage.faceName(),
                faceIndex: window.$gameMessage.faceIndex(),
                background: window.$gameMessage.background(),
                positionType: window.$gameMessage.positionType(),
                speakerName: window.$gameMessage.speakerName(),

                // 🔑 交互UI信息
                hasInteractiveUI: !!activeInteractiveUI,
                activeInteractiveUI: activeInteractiveUI,

                // 🔑 依赖UI信息（与Message窗口同步显示）
                dependentUI: {
                    hasNameBox: !!window.$gameMessage.speakerName(),
                    hasGold: this.checkForGoldDisplay(window.$gameMessage.allText()),
                    goldAmount: window.$gameParty ? window.$gameParty.gold() : 0
                },

                // 🔑 滚动文本（独立显示）
                isScrollText: window.$gameMessage.scrollMode(),
                scrollTextData: window.$gameMessage.scrollMode() ? {
                    speed: window.$gameMessage.scrollSpeed(),
                    noFast: window.$gameMessage.scrollNoFast()
                } : null
            };

            console.log('🎯 UnifiedMessageInterceptor: 收集消息数据:', messageData);
            return messageData;
        }

        /**
         * 🎯 检查文本中是否包含金钱显示标记
         */
        checkForGoldDisplay(text) {
            return text && text.includes('\\$');
        }

        /**
         * 🎯 触发统一回调
         */
        triggerUnifiedCallback(messageData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    console.log('✅ UnifiedMessageInterceptor: 执行统一回调');
                    this.onUnifiedMessage(messageData);
                } catch (error) {
                    console.error('❌ UnifiedMessageInterceptor: 统一回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ UnifiedMessageInterceptor: 没有设置统一回调方法');
            }
        }



        /**
         * 🎯 处理选择项选中（由UI组件调用）
         */
        handleChoiceSelected(index) {
            console.log('🎯 UnifiedMessageInterceptor: 选择项被选中:', index);

            // 调用原生的选择处理
            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }

            // 完成消息处理
            this.completeMessage();
        }

        /**
         * 🎯 处理数值输入完成（由UI组件调用）
         */
        handleNumberInputCompleted(value, variableId) {
            console.log('🎯 UnifiedMessageInterceptor: 数值输入完成:', { value, variableId });

            // 设置变量值
            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, value);
            }

            // 完成消息处理
            this.completeMessage();
        }

        /**
         * 🎯 处理物品选择完成（由UI组件调用）
         */
        handleItemChoiceCompleted(itemId, variableId) {
            console.log('🎯 UnifiedMessageInterceptor: 物品选择完成:', { itemId, variableId });

            // 设置变量值
            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, itemId);
            }

            // 完成消息处理
            this.completeMessage();
        }

        /**
         * 🎯 完成消息处理（由UI组件调用）
         * 调用原生的terminateMessage方法，完全按照RPG MZ的逻辑
         */
        completeMessage() {
            console.log('🎯 UnifiedMessageInterceptor: 完成消息处理');

            // 🔑 找到当前的Message窗口并调用原生的terminateMessage
            if (typeof SceneManager !== 'undefined' && SceneManager._scene && SceneManager._scene._messageWindow) {
                const messageWindow = SceneManager._scene._messageWindow;
                if (this.originalTerminateMessage) {
                    this.originalTerminateMessage.call(messageWindow);
                    console.log('✅ UnifiedMessageInterceptor: 调用原生terminateMessage完成');
                } else {
                    // 备用方案：直接清除消息
                    window.$gameMessage.clear();
                    console.log('✅ UnifiedMessageInterceptor: 备用方案清除消息完成');
                }
            } else {
                // 备用方案：直接清除消息
                if (window.$gameMessage) {
                    window.$gameMessage.clear();
                    console.log('✅ UnifiedMessageInterceptor: 备用方案清除消息完成');
                }
            }
        }
    }

    // 🚀 创建全局统一拦截器实例
    window.MessageInterceptor = new UnifiedMessageInterceptor();

    console.log('✅ 统一消息拦截器已加载');

})();
