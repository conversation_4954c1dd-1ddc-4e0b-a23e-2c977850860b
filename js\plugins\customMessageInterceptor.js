



(() => {
    'use strict';

    /**
     * 🎯 消息系统拦截器 - 简化回调设计
     *
     * 职责：
     * 1. 拦截 $gameMessage.add() 和 $gameMessage.setChoices()
     * 2. 提供简单的回调方法，UI组件直接赋值即可
     * 3. 不同类型的消息调用不同的回调方法
     */

    /**
     * 🔧 消息拦截器 - 统一拦截所有UI事件
     */
    class MessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 回调方法 - UI组件直接赋值这些方法
            this._onMessage = null;      // 文本消息回调
            this._onChoice = null;       // 选择项回调
            this._onNumberInput = null;  // 数值输入回调
            this._onItemChoice = null;   // 物品选择回调
            this._onScrollText = null;   // 滚动文本回调
            this._onGold = null;         // 金钱窗口回调
            this._onNameBox = null;      // 姓名框回调

            // 🔑 统一消息回调 - 所有UI事件都通过这个回调派发
            this._onUnifiedMessage = null;
        }

        // 🔑 设置消息回调时自动初始化拦截器
        set onMessage(callback) {
            console.log('🔧 MessageInterceptor: 设置 onMessage 回调');
            this._onMessage = callback;
            this.ensureSetup();
        }

        get onMessage() {
            return this._onMessage;
        }

        // 🔑 设置选择回调时自动初始化拦截器
        set onChoice(callback) {
            this._onChoice = callback;
            this.ensureSetup();
        }

        get onChoice() {
            return this._onChoice;
        }

        // 🔑 设置数字输入回调时自动初始化拦截器
        set onNumberInput(callback) {
            this._onNumberInput = callback;
            this.ensureSetup();
        }

        get onNumberInput() {
            return this._onNumberInput;
        }

        // 🔑 设置物品选择回调时自动初始化拦截器
        set onItemChoice(callback) {
            this._onItemChoice = callback;
            this.ensureSetup();
        }

        get onItemChoice() {
            return this._onItemChoice;
        }

        // 🔑 设置滚动文本回调时自动初始化拦截器
        set onScrollText(callback) {
            this._onScrollText = callback;
            this.ensureSetup();
        }

        get onScrollText() {
            return this._onScrollText;
        }

        // 🔑 设置金钱窗口回调时自动初始化拦截器
        set onGold(callback) {
            this._onGold = callback;
            this.ensureSetup();
        }

        get onGold() {
            return this._onGold;
        }

        // 🔑 设置姓名框回调时自动初始化拦截器
        set onNameBox(callback) {
            this._onNameBox = callback;
            this.ensureSetup();
        }

        get onNameBox() {
            return this._onNameBox;
        }

        // 🔑 设置统一消息回调时自动初始化拦截器
        set onUnifiedMessage(callback) {
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置（延迟初始化）
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截
         */
        setupInterception() {
            // 检查 $gameMessage 是否存在
            if (typeof window.$gameMessage === 'undefined' || window.$gameMessage === null) {
                console.log('MessageInterceptor: $gameMessage 尚未初始化，延迟设置拦截');
                // 延迟重试
                setTimeout(() => {
                    this.isSetup = false; // 重置状态
                    this.ensureSetup();
                }, 100);
                return;
            }

            if (this.isSetup) {
                return; // 避免重复设置
            }

            // 保存原始方法
            const originalAdd = window.$gameMessage.add;
            const originalSetChoices = window.$gameMessage.setChoices;
            const originalSetNumberInput = window.$gameMessage.setNumberInput;
            const originalSetItemChoice = window.$gameMessage.setItemChoice;
            const originalSetScroll = window.$gameMessage.setScroll;
            const originalSetSpeakerName = window.$gameMessage.setSpeakerName;
            const self = this;

            this.isSetup = true;

            // 🔑 拦截消息添加
            window.$gameMessage.add = function(text) {
                if (self.isEnabled) {
                    // 🔑 关键：先调用原生方法保持消息状态
                    originalAdd.call(this, text);

                    // 触发自定义消息事件
                    self.triggerCustomMessageEvent({
                        type: 'message',
                        text: text,
                        faceName: window.$gameMessage.faceName(),
                        faceIndex: window.$gameMessage.faceIndex(),
                        background: window.$gameMessage.background(),
                        positionType: window.$gameMessage.positionType()
                    });

                    // 注意：不阻止原生消息，让$gameMessage.isBusy()正常工作
                } else {
                    // 使用原生系统
                    originalAdd.call(this, text);
                }
            };

            // 🔑 拦截选择项设置
            window.$gameMessage.setChoices = function(choices, defaultType, cancelType) {
                if (self.isEnabled) {
                    // 触发自定义选择事件
                    self.triggerCustomChoiceEvent({
                        type: 'choices',
                        choices: choices,
                        defaultType: defaultType,
                        cancelType: cancelType,
                        background: window.$gameMessage.choiceBackground()
                    });

                    // 阻止原生选择显示
                    return;
                } else {
                    // 使用原生系统
                    originalSetChoices.call(this, choices, defaultType, cancelType);
                }
            };

            // 🔑 拦截数值输入设置
            window.$gameMessage.setNumberInput = function(variableId, maxDigits) {
                if (self.isEnabled) {
                    // 触发自定义数值输入事件
                    self.triggerCustomNumberInputEvent({
                        type: 'numberInput',
                        variableId: variableId,
                        maxDigits: maxDigits,
                        currentValue: window.$gameVariables ? window.$gameVariables.value(variableId) : 0
                    });

                    // 阻止原生数值输入显示
                    return;
                } else {
                    // 使用原生系统
                    originalSetNumberInput.call(this, variableId, maxDigits);
                }
            };

            // 🔑 拦截物品选择设置
            window.$gameMessage.setItemChoice = function(variableId, itemType) {
                if (self.isEnabled) {
                    // 触发自定义物品选择事件
                    self.triggerCustomItemChoiceEvent({
                        type: 'itemChoice',
                        variableId: variableId,
                        itemType: itemType
                    });

                    // 阻止原生物品选择显示
                    return;
                } else {
                    // 使用原生系统
                    originalSetItemChoice.call(this, variableId, itemType);
                }
            };

            // 🔑 拦截滚动文本设置
            window.$gameMessage.setScroll = function(speed, noFast) {
                if (self.isEnabled) {
                    // 先调用原生方法设置滚动模式
                    originalSetScroll.call(this, speed, noFast);

                    // 触发自定义滚动文本事件
                    self.triggerCustomScrollTextEvent({
                        type: 'scrollText',
                        speed: speed,
                        noFast: noFast,
                        text: '' // 文本会通过后续的add()调用添加
                    });
                } else {
                    // 使用原生系统
                    originalSetScroll.call(this, speed, noFast);
                }
            };

            // 🔑 拦截姓名设置
            window.$gameMessage.setSpeakerName = function(speakerName) {
                if (self.isEnabled) {
                    // 先调用原生方法
                    originalSetSpeakerName.call(this, speakerName);

                    // 触发自定义姓名框事件
                    if (speakerName) {
                        self.triggerCustomNameBoxEvent({
                            type: 'nameBox',
                            speakerName: speakerName
                        });
                    }
                } else {
                    // 使用原生系统
                    originalSetSpeakerName.call(this, speakerName);
                }
            };

            // 🔑 拦截金钱窗口显示（通过拦截Window_Message的processEscapeCharacter）
            // 需要等待Window_Message类加载后再设置
            this.setupGoldWindowInterception();

            console.log('✅ 消息拦截器已启用 - 支持所有UI类型');
        }

        /**
         * 🎯 触发消息回调
         */
        triggerCustomMessageEvent(messageData) {
            console.log('🎯 MessageInterceptor: 触发消息回调:', messageData);
            console.log('🔍 MessageInterceptor: 当前回调状态:', {
                hasCallback: !!this.onMessage,
                callbackType: typeof this.onMessage,
                isEnabled: this.isEnabled,
                isSetup: this.isSetup
            });

            // 调用统一回调
            this.triggerUnifiedCallback(messageData);

            // 调用专用回调
            if (this.onMessage && typeof this.onMessage === 'function') {
                try {
                    console.log('✅ MessageInterceptor: 执行消息回调');
                    this.onMessage(messageData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 消息回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ MessageInterceptor: 没有设置消息回调方法');
            }
        }

        /**
         * 🎯 触发选择回调
         */
        triggerCustomChoiceEvent(choiceData) {
            console.log('🎯 触发选择回调:', choiceData);

            // 调用统一回调
            this.triggerUnifiedCallback(choiceData);

            // 调用专用回调
            if (this.onChoice && typeof this.onChoice === 'function') {
                try {
                    this.onChoice(choiceData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 选择回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 触发数值输入回调
         */
        triggerCustomNumberInputEvent(numberInputData) {
            console.log('🎯 触发数值输入回调:', numberInputData);

            // 调用统一回调
            this.triggerUnifiedCallback(numberInputData);

            // 调用专用回调
            if (this.onNumberInput && typeof this.onNumberInput === 'function') {
                try {
                    this.onNumberInput(numberInputData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 数值输入回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 触发物品选择回调
         */
        triggerCustomItemChoiceEvent(itemChoiceData) {
            console.log('🎯 触发物品选择回调:', itemChoiceData);

            // 调用统一回调
            this.triggerUnifiedCallback(itemChoiceData);

            // 调用专用回调
            if (this.onItemChoice && typeof this.onItemChoice === 'function') {
                try {
                    this.onItemChoice(itemChoiceData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 物品选择回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 触发滚动文本回调
         */
        triggerCustomScrollTextEvent(scrollTextData) {
            console.log('🎯 触发滚动文本回调:', scrollTextData);

            // 调用统一回调
            this.triggerUnifiedCallback(scrollTextData);

            // 调用专用回调
            if (this.onScrollText && typeof this.onScrollText === 'function') {
                try {
                    this.onScrollText(scrollTextData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 滚动文本回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 触发姓名框回调
         */
        triggerCustomNameBoxEvent(nameBoxData) {
            console.log('🎯 触发姓名框回调:', nameBoxData);

            // 调用统一回调
            this.triggerUnifiedCallback(nameBoxData);

            // 调用专用回调
            if (this.onNameBox && typeof this.onNameBox === 'function') {
                try {
                    this.onNameBox(nameBoxData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 姓名框回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 触发统一回调 - 所有UI事件都会调用这个方法
         */
        triggerUnifiedCallback(eventData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    this.onUnifiedMessage(eventData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 统一回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 设置金钱窗口拦截
         */
        setupGoldWindowInterception() {
            // 延迟设置，等待Window_Message类加载
            setTimeout(() => {
                if (typeof Window_Message !== 'undefined' && Window_Message.prototype.processEscapeCharacter) {
                    const originalProcessEscapeCharacter = Window_Message.prototype.processEscapeCharacter;
                    const self = this;

                    Window_Message.prototype.processEscapeCharacter = function(code, textState) {
                        if (code === '$' && self.isEnabled) {
                            // 触发自定义金钱窗口事件
                            self.triggerCustomGoldEvent({
                                type: 'gold',
                                currentGold: window.$gameParty ? window.$gameParty.gold() : 0
                            });
                            // 不调用原生方法，阻止原生金钱窗口显示
                            return;
                        } else {
                            // 调用原生方法
                            originalProcessEscapeCharacter.call(this, code, textState);
                        }
                    };

                    console.log('✅ 金钱窗口拦截已设置');
                } else {
                    console.warn('⚠️ Window_Message 尚未加载，金钱窗口拦截设置失败');
                }
            }, 500);
        }

        /**
         * 🎯 触发金钱窗口回调
         */
        triggerCustomGoldEvent(goldData) {
            console.log('🎯 触发金钱窗口回调:', goldData);

            // 调用统一回调
            this.triggerUnifiedCallback(goldData);

            // 调用专用回调
            if (this.onGold && typeof this.onGold === 'function') {
                try {
                    this.onGold(goldData);
                } catch (error) {
                    console.error('❌ MessageInterceptor: 金钱窗口回调执行失败:', error);
                }
            }
        }

        /**
         * 🎯 处理选择项选中（由UILayout中的按钮调用）
         */
        handleChoiceSelected(index) {
            console.log('选择项被选中:', index);

            // 调用原生的选择处理
            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }
        }

        /**
         * 🎯 完成消息显示（由UI组件调用）
         */
        completeMessage() {
            console.log('🎯 MessageInterceptor: 完成消息显示');

            // 清除消息文本，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage.clear();
                console.log('✅ MessageInterceptor: 消息已清除，事件可以继续');
            }
        }

        /**
         * 🎯 完成选择项（由UI组件调用）
         */
        completeChoice() {
            console.log('🎯 MessageInterceptor: 完成选择项');

            // 清除选择项，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage._choices = [];
                window.$gameMessage._choiceDefaultType = 0;
                window.$gameMessage._choiceCancelType = 0;
                window.$gameMessage._choiceBackground = 0;
                console.log('✅ MessageInterceptor: 选择项已清除，事件可以继续');
            }
        }

        /**
         * 🎯 完成数值输入（由UI组件调用）
         */
        completeNumberInput(value, variableId) {
            console.log('🎯 MessageInterceptor: 完成数值输入', { value, variableId });

            // 设置变量值
            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, value);
            }

            // 清除数值输入状态，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage._numInputVariableId = 0;
                window.$gameMessage._numInputMaxDigits = 0;
                console.log('✅ MessageInterceptor: 数值输入已清除，事件可以继续');
            }
        }

        /**
         * 🎯 完成物品选择（由UI组件调用）
         */
        completeItemChoice(itemId, variableId) {
            console.log('🎯 MessageInterceptor: 完成物品选择', { itemId, variableId });

            // 设置变量值
            if (window.$gameVariables && variableId) {
                window.$gameVariables.setValue(variableId, itemId);
            }

            // 清除物品选择状态，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage._itemChoiceVariableId = 0;
                window.$gameMessage._itemChoiceItypeId = 0;
                console.log('✅ MessageInterceptor: 物品选择已清除，事件可以继续');
            }
        }

        /**
         * 🎯 完成滚动文本（由UI组件调用）
         */
        completeScrollText() {
            console.log('🎯 MessageInterceptor: 完成滚动文本');

            // 清除滚动文本状态，让事件继续
            if (window.$gameMessage) {
                window.$gameMessage._scrollMode = false;
                window.$gameMessage._scrollSpeed = 2;
                window.$gameMessage._scrollNoFast = false;
                window.$gameMessage.clear(); // 清除文本内容
                console.log('✅ MessageInterceptor: 滚动文本已清除，事件可以继续');
            }
        }
    }

    // 🚀 创建全局拦截器实例
    window.MessageInterceptor = new MessageInterceptor();

    console.log('✅ 消息拦截器已加载');

})();
