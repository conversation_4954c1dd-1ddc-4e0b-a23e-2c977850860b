



(() => {
    'use strict';

    /**
     * 🎯 统一消息系统拦截器 - 重新设计
     *
     * 核心思想：
     * 1. 只拦截 $gameMessage.add() 作为入口点
     * 2. 在Message处理时收集所有依赖的UI组件（选择项、数值输入、物品选择、姓名框、金钱等）
     * 3. 将完整的消息数据（包含所有UI组件）通过统一回调派发
     * 4. UI组件处理完成后统一清除，让事件继续
     */

    /**
     * 🔧 统一消息拦截器
     */
    class UnifiedMessageInterceptor {
        constructor() {
            this.isEnabled = true;
            this.isSetup = false;

            // 🔑 统一消息回调 - 包含完整的消息和所有依赖UI
            this._onUnifiedMessage = null;

            // 🔑 当前消息数据缓存
            this.currentMessageData = null;
        }

        // 🔑 设置统一消息回调时自动初始化拦截器
        set onUnifiedMessage(callback) {
            console.log('🔧 UnifiedMessageInterceptor: 设置统一消息回调');
            this._onUnifiedMessage = callback;
            this.ensureSetup();
        }

        get onUnifiedMessage() {
            return this._onUnifiedMessage;
        }

        /**
         * 🎯 确保拦截器已设置（延迟初始化）
         */
        ensureSetup() {
            if (!this.isSetup) {
                this.setupInterception();
            }
        }

        /**
         * 🎯 设置拦截 - 重新设计为统一拦截
         */
        setupInterception() {
            // 检查 $gameMessage 是否存在
            if (typeof window.$gameMessage === 'undefined' || window.$gameMessage === null) {
                console.log('UnifiedMessageInterceptor: $gameMessage 尚未初始化，延迟设置拦截');
                // 延迟重试
                setTimeout(() => {
                    this.isSetup = false; // 重置状态
                    this.ensureSetup();
                }, 100);
                return;
            }

            if (this.isSetup) {
                return; // 避免重复设置
            }

            // 保存原始方法
            const originalAdd = window.$gameMessage.add;
            const self = this;

            this.isSetup = true;

            // 🔑 只拦截消息添加作为统一入口点
            window.$gameMessage.add = function(text) {
                if (self.isEnabled) {
                    // 🔑 关键：先调用原生方法保持消息状态
                    originalAdd.call(this, text);

                    // 🔑 延迟收集完整消息数据，等待所有设置完成
                    setTimeout(() => {
                        self.collectAndTriggerUnifiedMessage();
                    }, 0);
                } else {
                    // 使用原生系统
                    originalAdd.call(this, text);
                }
            };

            console.log('✅ 统一消息拦截器已启用');
        }

        /**
         * 🎯 收集并触发统一消息 - 核心方法
         */
        collectAndTriggerUnifiedMessage() {
            if (!window.$gameMessage) {
                console.warn('⚠️ $gameMessage 不存在');
                return;
            }

            // 🔑 收集完整的消息数据
            const unifiedMessageData = {
                // 基础消息信息
                type: 'unified',
                hasText: window.$gameMessage.hasText(),
                allText: window.$gameMessage.allText(),

                // 消息样式
                faceName: window.$gameMessage.faceName(),
                faceIndex: window.$gameMessage.faceIndex(),
                background: window.$gameMessage.background(),
                positionType: window.$gameMessage.positionType(),
                speakerName: window.$gameMessage.speakerName(),

                // 🔑 依赖的UI组件
                dependencies: {
                    // 选择项
                    hasChoices: window.$gameMessage.isChoice(),
                    choices: window.$gameMessage.isChoice() ? {
                        list: window.$gameMessage.choices(),
                        defaultType: window.$gameMessage.choiceDefaultType(),
                        cancelType: window.$gameMessage.choiceCancelType(),
                        background: window.$gameMessage.choiceBackground(),
                        positionType: window.$gameMessage.choicePositionType()
                    } : null,

                    // 数值输入
                    hasNumberInput: window.$gameMessage.isNumberInput(),
                    numberInput: window.$gameMessage.isNumberInput() ? {
                        variableId: window.$gameMessage.numInputVariableId(),
                        maxDigits: window.$gameMessage.numInputMaxDigits(),
                        currentValue: window.$gameVariables ? window.$gameVariables.value(window.$gameMessage.numInputVariableId()) : 0
                    } : null,

                    // 物品选择
                    hasItemChoice: window.$gameMessage.isItemChoice(),
                    itemChoice: window.$gameMessage.isItemChoice() ? {
                        variableId: window.$gameMessage.itemChoiceVariableId(),
                        itemType: window.$gameMessage.itemChoiceItypeId()
                    } : null,

                    // 滚动文本
                    hasScrollText: window.$gameMessage.scrollMode(),
                    scrollText: window.$gameMessage.scrollMode() ? {
                        speed: window.$gameMessage.scrollSpeed(),
                        noFast: window.$gameMessage.scrollNoFast()
                    } : null,

                    // 姓名框（依赖于speakerName）
                    hasNameBox: !!window.$gameMessage.speakerName(),

                    // 金钱窗口（需要检查文本中是否包含\$）
                    hasGold: this.checkForGoldDisplay(window.$gameMessage.allText()),
                    gold: window.$gameParty ? {
                        currentGold: window.$gameParty.gold()
                    } : null
                }
            };

            console.log('🎯 UnifiedMessageInterceptor: 收集到完整消息数据:', unifiedMessageData);

            // 缓存当前消息数据
            this.currentMessageData = unifiedMessageData;

            // 触发统一回调
            this.triggerUnifiedCallback(unifiedMessageData);
        }

        /**
         * 🎯 检查文本中是否包含金钱显示标记
         */
        checkForGoldDisplay(text) {
            return text && text.includes('\\$');
        }

        /**
         * 🎯 触发统一回调
         */
        triggerUnifiedCallback(messageData) {
            if (this.onUnifiedMessage && typeof this.onUnifiedMessage === 'function') {
                try {
                    console.log('✅ UnifiedMessageInterceptor: 执行统一回调');
                    this.onUnifiedMessage(messageData);
                } catch (error) {
                    console.error('❌ UnifiedMessageInterceptor: 统一回调执行失败:', error);
                }
            } else {
                console.warn('⚠️ UnifiedMessageInterceptor: 没有设置统一回调方法');
            }
        }



        /**
         * 🎯 处理选择项选中（由UI组件调用）
         */
        handleChoiceSelected(index) {
            console.log('🎯 UnifiedMessageInterceptor: 选择项被选中:', index);

            // 调用原生的选择处理
            if (window.$gameMessage && window.$gameMessage.onChoice) {
                window.$gameMessage.onChoice(index);
            }
        }

        /**
         * 🎯 处理数值输入完成（由UI组件调用）
         */
        handleNumberInputCompleted(value) {
            console.log('🎯 UnifiedMessageInterceptor: 数值输入完成:', value);

            const messageData = this.currentMessageData;
            if (messageData && messageData.dependencies.numberInput) {
                const variableId = messageData.dependencies.numberInput.variableId;

                // 设置变量值
                if (window.$gameVariables && variableId) {
                    window.$gameVariables.setValue(variableId, value);
                }
            }
        }

        /**
         * 🎯 处理物品选择完成（由UI组件调用）
         */
        handleItemChoiceCompleted(itemId) {
            console.log('🎯 UnifiedMessageInterceptor: 物品选择完成:', itemId);

            const messageData = this.currentMessageData;
            if (messageData && messageData.dependencies.itemChoice) {
                const variableId = messageData.dependencies.itemChoice.variableId;

                // 设置变量值
                if (window.$gameVariables && variableId) {
                    window.$gameVariables.setValue(variableId, itemId);
                }
            }
        }

        /**
         * 🎯 完成整个消息处理（由UI组件调用）
         * 这是统一的完成方法，会清除所有相关状态
         */
        completeUnifiedMessage() {
            console.log('🎯 UnifiedMessageInterceptor: 完成统一消息处理');

            if (!window.$gameMessage) {
                console.warn('⚠️ $gameMessage 不存在');
                return;
            }

            // 🔑 统一清除所有消息状态，让事件继续
            window.$gameMessage.clear();

            // 清除当前消息数据缓存
            this.currentMessageData = null;

            console.log('✅ UnifiedMessageInterceptor: 所有消息状态已清除，事件可以继续');
        }
    }

    // 🚀 创建全局拦截器实例
    window.MessageInterceptor = new MessageInterceptor();

    console.log('✅ 消息拦截器已加载');

})();
