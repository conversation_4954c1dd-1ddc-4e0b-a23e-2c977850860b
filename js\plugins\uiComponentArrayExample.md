# UI组件数组方案 - 完整使用指南

## 🎯 核心概念

基于RPG Maker MZ的消息机制分析，我们设计了UI组件数组方案：
- **每次消息是一份一份触发的**
- **每一份消息包含一个UI组件数组**
- **数组中的组件有明确的类型和依赖关系**

## 📊 UI组件数组结构

### 消息数据结构
```javascript
const messageData = {
    type: 'unified',
    timestamp: Date.now(),
    
    // 🔑 UI组件数组 - 核心数据
    uiComponents: [
        // 文本组件（必须）
        {
            type: 'text',
            data: {
                content: 'Hello World!',
                faceName: 'Actor1',
                faceIndex: 0,
                background: 0,
                positionType: 2,
                speakerName: 'Hero'
            },
            isDependent: false,
            isInteractive: false,
            isRequired: true
        },
        
        // 姓名框组件（依赖）
        {
            type: 'nameBox',
            data: {
                speakerName: 'Hero'
            },
            isDependent: true,
            isInteractive: false,
            isRequired: false
        },
        
        // 金钱窗口组件（依赖）
        {
            type: 'gold',
            data: {
                amount: 1000
            },
            isDependent: true,
            isInteractive: false,
            isRequired: false
        },
        
        // 选择项组件（交互）
        {
            type: 'choice',
            data: {
                choices: ['Yes', 'No', 'Maybe'],
                defaultType: 0,
                cancelType: 1,
                positionType: 2,
                background: 0
            },
            isDependent: false,
            isInteractive: true,
            isRequired: false
        }
    ],
    
    // 🔑 UI组件统计
    componentStats: {
        total: 4,           // 总组件数
        dependent: 2,       // 依赖组件数（姓名框、金钱窗口）
        interactive: 1,     // 交互组件数（最多1个）
        hasText: true,      // 是否有文本
        hasInteractive: true // 是否有交互UI
    },
    
    // 解释器信息
    interpreter: interpreterInstance
};
```

## 🎮 UI组件类型详解

### 1. 文本组件 (text)
```javascript
{
    type: 'text',
    data: {
        content: '显示的文本内容',
        faceName: '头像文件名',
        faceIndex: 0,
        background: 0,      // 0=窗口, 1=暗化, 2=透明
        positionType: 2,    // 0=上, 1=中, 2=下
        speakerName: '说话者姓名'
    },
    isDependent: false,
    isInteractive: false,
    isRequired: true        // 文本组件总是必须的
}
```

### 2. 姓名框组件 (nameBox)
```javascript
{
    type: 'nameBox',
    data: {
        speakerName: '说话者姓名'
    },
    isDependent: true,      // 依赖于文本组件
    isInteractive: false,
    isRequired: false
}
```

### 3. 金钱窗口组件 (gold)
```javascript
{
    type: 'gold',
    data: {
        amount: 1000        // 当前金钱数量
    },
    isDependent: true,      // 依赖于文本组件
    isInteractive: false,
    isRequired: false
}
```

### 4. 选择项组件 (choice)
```javascript
{
    type: 'choice',
    data: {
        choices: ['选项1', '选项2', '选项3'],
        defaultType: 0,     // 默认选择
        cancelType: 1,      // 取消时的选择
        positionType: 2,    // 位置类型
        background: 0       // 背景类型
    },
    isDependent: false,
    isInteractive: true,    // 需要用户交互
    isRequired: false
}
```

### 5. 数值输入组件 (numberInput)
```javascript
{
    type: 'numberInput',
    data: {
        variableId: 1,      // 存储变量ID
        maxDigits: 6,       // 最大位数
        currentValue: 0     // 当前值
    },
    isDependent: false,
    isInteractive: true,
    isRequired: false
}
```

### 6. 物品选择组件 (itemChoice)
```javascript
{
    type: 'itemChoice',
    data: {
        variableId: 2,      // 存储变量ID
        itemType: 2         // 物品类型 (1=普通物品, 2=重要物品, 3=隐藏物品A, 4=隐藏物品B)
    },
    isDependent: false,
    isInteractive: true,
    isRequired: false
}
```

### 7. 滚动文本组件 (scrollText)
```javascript
{
    type: 'scrollText',
    data: {
        content: '滚动显示的文本内容',
        speed: 2,           // 滚动速度
        noFast: false       // 是否禁止快进
    },
    isDependent: false,
    isInteractive: false,
    isRequired: true        // 滚动文本模式下是必须的
}
```

## 🔄 处理流程

### 1. 接收消息
```javascript
window.MessageInterceptor.onUnifiedMessage = function(messageData) {
    console.log('收到UI组件数组:', messageData.uiComponents);
    console.log('组件统计:', messageData.componentStats);
    
    // 根据组件类型进行处理
    processUIComponents(messageData.uiComponents);
};
```

### 2. 处理UI组件
```javascript
function processUIComponents(uiComponents) {
    uiComponents.forEach(component => {
        switch (component.type) {
            case 'text':
                createTextUI(component.data);
                break;
            case 'nameBox':
                createNameBoxUI(component.data);
                break;
            case 'gold':
                createGoldUI(component.data);
                break;
            case 'choice':
                createChoiceUI(component.data);
                break;
            case 'numberInput':
                createNumberInputUI(component.data);
                break;
            case 'itemChoice':
                createItemChoiceUI(component.data);
                break;
            case 'scrollText':
                createScrollTextUI(component.data);
                break;
        }
    });
}
```

### 3. 完成交互
```javascript
// 选择项完成
window.MessageInterceptor.handleChoiceSelected(selectedIndex);

// 数值输入完成
window.MessageInterceptor.handleNumberInputCompleted(inputValue);

// 物品选择完成
window.MessageInterceptor.handleItemChoiceCompleted(selectedItemId);

// 消息完成（纯文本或滚动文本）
window.MessageInterceptor.completeMessage();
```

## 📋 常见UI组合示例

### 1. 纯文本消息
```javascript
uiComponents: [
    { type: 'text', data: {...}, isRequired: true }
]
```

### 2. 带姓名框的文本
```javascript
uiComponents: [
    { type: 'text', data: {...}, isRequired: true },
    { type: 'nameBox', data: {...}, isDependent: true }
]
```

### 3. 带选择项的对话
```javascript
uiComponents: [
    { type: 'text', data: {...}, isRequired: true },
    { type: 'nameBox', data: {...}, isDependent: true },
    { type: 'choice', data: {...}, isInteractive: true }
]
```

### 4. 最大组合（文本+姓名框+金钱+数值输入）
```javascript
uiComponents: [
    { type: 'text', data: {...}, isRequired: true },
    { type: 'nameBox', data: {...}, isDependent: true },
    { type: 'gold', data: {...}, isDependent: true },
    { type: 'numberInput', data: {...}, isInteractive: true }
]
```

### 5. 滚动文本（独立）
```javascript
uiComponents: [
    { type: 'scrollText', data: {...}, isRequired: true }
]
```

## 🎯 优势总结

1. **结构清晰**：每个UI组件都有明确的类型和属性
2. **依赖明确**：通过`isDependent`标识依赖关系
3. **交互明确**：通过`isInteractive`标识是否需要用户交互
4. **扩展性强**：可以轻松添加新的UI组件类型
5. **统计便利**：`componentStats`提供快速的组件统计信息
6. **原生兼容**：完全按照RPG MZ的原生逻辑设计
