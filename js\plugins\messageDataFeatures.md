# 消息数据层面功能详解

## 🎯 概述

基于对RPG Maker MZ源码的深入分析，我们在customMessageInterceptor.js中补充了重要的**消息数据层面**功能，这些功能属于数据处理而非UI渲染。

## 🔍 新增功能详解

### 1. RTL（从右到左）语言支持

```javascript
// 文本组件中的新属性
data: {
    isRTL: true/false,  // 是否为RTL语言
    // ...其他属性
}
```

**功能说明：**
- 自动检测文本中是否包含阿拉伯文字符
- 基于RPG MZ的`Utils.containsArabic`逻辑
- 影响姓名框的位置（RTL时右对齐，否则左对齐）

**检测范围：**
- 阿拉伯文：`\u0600-\u06FF`
- 阿拉伯文补充：`\u0750-\u077F`
- 阿拉伯文扩展A：`\u08A0-\u08FF`
- 阿拉伯文表现形式A：`\uFB50-\uFDFF`
- 阿拉伯文表现形式B：`\uFE70-\uFEFF`

### 2. 转义字符信息提取

```javascript
// 文本组件中的新属性
data: {
    escapeCharacters: {
        hasGold: true,              // 是否包含\$
        variables: [...],           // \V[n] 变量信息
        actorNames: [...],          // \N[n] 角色名信息
        partyMembers: [...],        // \P[n] 队伍成员信息
        hasCurrencyUnit: true,      // 是否包含\G
        colors: [...],              // \C[n] 颜色代码
        icons: [...],               // \I[n] 图标
        waitTimes: [...],           // \. \| 等待时间
        controlCodes: [...]         // 其他控制代码
    }
}
```

#### 2.1 变量信息 (variables)
```javascript
variables: [
    {
        id: 1,                      // 变量ID
        value: 100,                 // 当前值
        original: "\\V[1]"          // 原始转义字符
    }
]
```

#### 2.2 角色名信息 (actorNames)
```javascript
actorNames: [
    {
        id: 1,                      // 角色ID
        name: "Harold",             // 角色名
        original: "\\N[1]"          // 原始转义字符
    }
]
```

#### 2.3 队伍成员信息 (partyMembers)
```javascript
partyMembers: [
    {
        index: 1,                   // 队伍位置（1-4）
        name: "Harold",             // 成员名
        original: "\\P[1]"          // 原始转义字符
    }
]
```

#### 2.4 颜色代码 (colors)
```javascript
colors: [
    {
        index: 2,                   // 颜色索引
        original: "\\C[2]"          // 原始转义字符
    }
]
```

#### 2.5 图标信息 (icons)
```javascript
icons: [
    {
        index: 64,                  // 图标索引
        original: "\\I[64]"         // 原始转义字符
    }
]
```

#### 2.6 等待时间 (waitTimes)
```javascript
waitTimes: [
    {
        type: "short",              // 等待类型
        frames: 15,                 // 等待帧数
        original: "\\."             // 原始转义字符
    },
    {
        type: "long",
        frames: 60,
        original: "\\|"
    }
]
```

#### 2.7 控制代码 (controlCodes)
```javascript
controlCodes: [
    { type: "pause", original: "\\!" },        // 暂停
    { type: "speedUp", original: "\\>" },      // 加速显示
    { type: "speedDown", original: "\\<" },    // 减速显示
    { type: "skipPause", original: "\\^" }     // 跳过暂停
]
```

### 3. 消息继续标志

```javascript
// 文本组件中的新属性
data: {
    doesContinue: true/false,   // 消息是否会继续
    // ...其他属性
}
```

**功能说明：**
- 判断当前消息结束后是否还有后续消息
- 用于决定是否需要关闭消息窗口
- 基于RPG MZ的`Window_Message.prototype.doesContinue`逻辑

### 4. 选择项回调信息

```javascript
// 选择项组件中的新属性
data: {
    hasCallback: true/false,    // 是否设置了回调函数
    // ...其他属性
}
```

**功能说明：**
- 检查是否设置了选择项回调函数
- 用于自定义UI判断如何处理选择结果

## 🎨 实际应用示例

### 处理RTL语言
```javascript
function createCustomMessageUI(textData, position) {
    if (textData.isRTL) {
        // 设置文本方向为从右到左
        element.style.direction = 'rtl';
        element.style.textAlign = 'right';
    }
}
```

### 处理转义字符
```javascript
function processEscapeCharacters(escapeInfo) {
    // 替换变量
    escapeInfo.variables.forEach(variable => {
        text = text.replace(variable.original, variable.value);
    });
    
    // 替换角色名
    escapeInfo.actorNames.forEach(actor => {
        text = text.replace(actor.original, actor.name);
    });
    
    // 应用颜色
    escapeInfo.colors.forEach(color => {
        // 应用颜色样式
    });
    
    // 显示图标
    escapeInfo.icons.forEach(icon => {
        // 插入图标元素
    });
    
    // 处理等待时间
    escapeInfo.waitTimes.forEach(wait => {
        // 设置文本显示延迟
    });
}
```

### 处理消息继续
```javascript
function handleMessageComplete(textData) {
    if (textData.doesContinue) {
        // 不关闭窗口，等待下一条消息
        keepWindowOpen();
    } else {
        // 关闭窗口
        closeMessageWindow();
    }
}
```

## 🔧 与UI渲染的区别

### 消息数据层面（✅ 在拦截器中处理）
- RTL语言检测
- 转义字符信息提取
- 变量值获取
- 角色名获取
- 消息继续判断

### UI渲染层面（❌ 不在拦截器中处理）
- 文本逐字显示动画
- 字体大小变化效果
- 颜色渐变动画
- 图标绘制
- 等待时间的实际延迟执行
- 窗口开关动画

## 📊 完整的数据结构示例

```javascript
// 包含所有新功能的完整文本组件
{
    type: 'text',
    data: {
        content: "Hello \\N[1]! You have \\V[1] \\G.",
        faceName: "Actor1",
        faceIndex: 0,
        speakerName: "System",
        isRTL: false,
        escapeCharacters: {
            hasGold: false,
            variables: [{ id: 1, value: 100, original: "\\V[1]" }],
            actorNames: [{ id: 1, name: "Harold", original: "\\N[1]" }],
            partyMembers: [],
            hasCurrencyUnit: true,
            colors: [],
            icons: [],
            waitTimes: [],
            controlCodes: []
        },
        doesContinue: false
    },
    position: { /* 位置信息 */ },
    isDependent: false,
    isInteractive: false,
    isRequired: true
}
```

这些新功能确保了消息拦截器能够提供完整的消息数据信息，为自定义UI的实现提供了强大的数据支持！
