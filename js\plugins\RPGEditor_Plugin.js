/*:
 * @target MZ
 * @plugindesc RPGEditor_GeneratedPlugin v1.0.0
 * <AUTHOR> Editor
 * @version 1.0.0
 * @description Auto-generated plugin from RPG Editor
 *
 * @help RPGEditor_GeneratedPlugin.js
 *
 * This plugin was automatically generated by RPG Editor.
 * It recreates the scene objects and UI elements based on saved data.
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 * Free for commercial and non-commercial use.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 * Version 1.0.0: Initial release
 */

// ===== 插件代码立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始加载插件代码");

  // ===== uiScriptManager.js =====
  /**
   * UIScriptManager - 统一的脚本管理器
   * 专门处理对象的脚本功能，与UIComponent分离
   */

  (() => {
  window.UIScriptManager = {

          /**
           * 为对象添加脚本功能
           */
          applyToObject(obj, properties = {}) {
              console.log(`🔧 UIScriptManager: 为${obj.constructor.name}添加脚本功能`);

              // 获取组件类型
              const componentType = obj.uiComponentType || obj.constructor.name;

              // 添加脚本数组
              obj.componentScripts = properties.componentScripts || this.createDefaultScripts(componentType);

              // 添加脚本管理方法
              this.addScriptMethods(obj);

              console.log(`✅ UIScriptManager: ${obj.constructor.name}脚本功能已添加`);

              return obj;
          },

          /**
           * 创建默认脚本数组
           */
          createDefaultScripts(componentType = 'default') {
              return [
                  {
                      id: 'default_script',
                      name: '默认脚本',
                      enabled: true,
                      code: this.generateScriptTemplate(componentType),
                      description: '默认脚本，包含常用方法模板'
                  }
              ];
          },

          /**
           * 根据组件类型生成脚本模板
           */
          generateScriptTemplate(componentType) {
              // 基础生命周期模板
              let template = `/**
   * 🚀 onStart - 对象启动生命周期
   * 触发时机: 对象创建并添加到父容器时自动触发
   * 作用: 初始化对象状态、设置默认值、绑定数据等
   * 配合方法: 无需手动调用，系统自动触发
   */
  function onStart() {
    console.log("对象启动:", self.name || "unnamed");
    // 在这里添加初始化逻辑
    // 例如: 设置初始文本、绑定数据源、初始化变量等
  }

  /**
   * 🔄 onUpdate - 每帧更新生命周期
   * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用
   * 作用: 实现动画、实时数据更新、状态检查等
   * 配合方法: 无需手动调用，系统自动触发
   * 注意: 避免在此方法中执行耗时操作
   */
  // function onUpdate() {
  //   console.log("每帧更新:", self.name);
  //   // 在这里添加每帧更新逻辑
  //   // 例如: 动画更新、实时数据显示、状态检查等
  // }

  /**
   * 📝 onFieldUpdate - 字段更新生命周期
   * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发
   * 作用: 响应数据变化、更新UI显示、同步状态等
   * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)
   * 参数: ctx - 包含字段更新上下文信息的对象
   */
  // function onFieldUpdate(ctx) {
  //   console.log("字段更新:", self.name, ctx);
  //   // 在这里添加字段更新时的处理逻辑
  //   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }
  //   // 例如: 根据 ctx.field 判断更新哪个属性
  // }

  /**
   * 🗑️ onDestroy - 对象销毁生命周期
   * 触发时机: 对象从父容器移除时自动触发
   * 作用: 清理资源、保存数据、解除绑定等
   * 配合方法: 无需手动调用，系统自动触发
   */
  // function onDestroy() {
  //   console.log("对象销毁:", self.name);
  //   // 在这里添加清理逻辑
  //   // 例如: 清理定时器、保存状态、解除事件绑定等
  // }`;

              // 根据组件类型添加特定事件
              if (componentType === 'Button') {
                  template += this.getButtonEvents();
              } else if (componentType === 'Switch' || componentType === 'Slider') {
                  template += this.getChangeEvent(componentType);
              } else if (componentType === 'UIInput') {
                  template += this.getInputEvents();
              }

              return template;
          },

          /**
           * 获取Button组件的交互事件模板
           */
          getButtonEvents() {
              return `

  /**
   * 👆 onClick - 单击交互事件
   * 触发时机: 用户单击对象时触发
   * 作用: 处理点击逻辑、切换状态、执行操作等
   * 配合方法: 无需手动调用，用户交互自动触发
   */
  // function onClick() {
  //   console.log("对象被点击:", self.name);
  //   // 在这里添加点击逻辑
  //   // 例如: 切换状态、打开菜单、执行命令等
  // }

  /**
   * 👆👆 onDoubleClick - 双击交互事件
   * 触发时机: 用户双击对象时触发
   * 作用: 处理双击特殊逻辑、快捷操作等
   * 配合方法: 无需手动调用，用户交互自动触发
   */
  // function onDoubleClick() {
  //   console.log("对象被双击:", self.name);
  //   // 在这里添加双击逻辑
  //   // 例如: 快速编辑、全屏显示、快捷操作等
  // }

  /**
   * 🖱️ onHover - 鼠标悬停事件
   * 触发时机: 鼠标进入对象区域时触发
   * 作用: 显示提示信息、改变外观、预览效果等
   * 配合方法: 通常与 onHoverOut 配对使用
   */
  // function onHover() {
  //   console.log("鼠标悬停:", self.name);
  //   // 在这里添加悬停逻辑
  //   // 例如: 显示工具提示、改变颜色、显示预览等
  // }

  /**
   * 🖱️ onHoverOut - 鼠标离开事件
   * 触发时机: 鼠标离开对象区域时触发
   * 作用: 隐藏提示信息、恢复外观、清理预览等
   * 配合方法: 通常与 onHover 配对使用
   */
  // function onHoverOut() {
  //   console.log("鼠标离开:", self.name);
  //   // 在这里添加鼠标离开逻辑
  //   // 例如: 隐藏工具提示、恢复颜色、清理预览等
  // }

  /**
   * ⬇️ onPress - 按下事件
   * 触发时机: 鼠标按下（但未释放）时触发
   * 作用: 开始拖拽、显示按下效果、记录按下状态等
   * 配合方法: 通常与 onRelease 配对使用
   */
  // function onPress() {
  //   console.log("对象按下:", self.name);
  //   // 在这里添加按下逻辑
  //   // 例如: 开始拖拽、改变外观、记录状态等
  // }

  /**
   * ⬆️ onRelease - 释放事件
   * 触发时机: 鼠标释放时触发
   * 作用: 结束拖拽、恢复外观、完成操作等
   * 配合方法: 通常与 onRelease 配对使用
   */
  // function onRelease() {
  //   console.log("对象释放:", self.name);
  //   // 在这里添加释放逻辑
  //   // 例如: 结束拖拽、恢复外观、完成操作等
  // }`;
          },

          /**
           * 获取Switch/Slider组件的onChange事件模板
           */
          getChangeEvent(componentType) {
              const isSwitch = componentType === 'Switch';
              return `

  /**
   * 🔄 onChange - 值变化事件
   * 触发时机: ${isSwitch ? '开关状态改变时触发' : '滑动条值改变时触发'}
   * 作用: 响应${isSwitch ? '开关状态' : '数值'}变化、更新相关UI、执行相应逻辑等
   * 配合方法: 无需手动调用，${isSwitch ? '状态变化' : '值变化'}时自动触发
   * 参数: ${isSwitch ? 'newValue - 新的开关状态 (true/false), oldValue - 旧的开关状态' : 'newValue - 新的数值, oldValue - 旧的数值'}
   */
  // function onChange(newValue, oldValue) {
  //   console.log("${isSwitch ? '开关状态变化' : '数值变化'}:", self.name, "从", oldValue, "到", newValue);
  //   // 在这里添加${isSwitch ? '状态变化' : '值变化'}处理逻辑
  //   // 例如: 更新其他UI组件、保存设置、触发相关操作等
  // }`;
          },

          /**
           * 获取UIInput组件的输入事件模板
           */
          getInputEvents() {
              return `

  /**
   * 🔄 onChange - 文本内容改变事件
   * 触发时机: 用户输入文本导致值改变时触发
   * 作用: 处理文本变化、实时验证、数据同步等
   * 参数: newValue - 新的文本值, oldValue - 旧的文本值
   */
  function onChange(newValue, oldValue) {
    console.log("文本改变:", oldValue, "→", newValue);
    // 在这里添加文本变化处理逻辑
    // 例如: 实时验证、数据同步、格式化等
  }

  /**
   * 🎯 onFocus - 获得焦点事件
   * 触发时机: 输入框获得焦点时触发
   * 作用: 处理焦点获得时的逻辑
   */
  // function onFocus() {
  //   console.log("输入框获得焦点:", self.name || "unnamed");
  //   // 在这里添加获得焦点时的处理逻辑
  //   // 例如: 显示提示信息、改变样式等
  // }

  /**
   * 🎯 onBlur - 失去焦点事件
   * 触发时机: 输入框失去焦点时触发
   * 作用: 处理焦点失去时的逻辑、数据验证等
   */
  // function onBlur() {
  //   console.log("输入框失去焦点:", self.name || "unnamed");
  //   // 在这里添加失去焦点时的处理逻辑
  //   // 例如: 数据验证、保存数据等
  // }

  /**
   * ⌨️ onEnterPressed - 回车键按下事件
   * 触发时机: 用户在输入框中按下回车键时触发
   * 作用: 处理回车键确认操作
   */
  // function onEnterPressed() {
  //   console.log("回车键按下:", self.value);
  //   // 在这里添加回车键处理逻辑
  //   // 例如: 提交表单、确认输入等
  // }

  /**
   * ❌ onValidationFailed - 输入验证失败事件
   * 触发时机: 输入内容不符合验证规则时触发
   * 作用: 处理验证失败的情况
   * 参数: errorMessage - 错误信息
   */
  // function onValidationFailed(errorMessage) {
  //   console.log("输入验证失败:", errorMessage);
  //   // 在这里添加验证失败处理逻辑
  //   // 例如: 显示错误提示、重置输入等
  // }`;
          },

          /**
           * 为对象添加脚本管理方法
           */
          addScriptMethods(obj) {
              // 添加脚本
              obj.addScript = function(script) {
                  if (!this.componentScripts) {
                      this.componentScripts = [];
                  }
                  this.componentScripts.push(script);
                  console.log(`📝 添加脚本: ${script.name}`);
              };

              // 删除脚本
              obj.removeScript = function(scriptId) {
                  if (!this.componentScripts) return false;
                  const index = this.componentScripts.findIndex(s => s.id === scriptId);
                  if (index !== -1) {
                      const removed = this.componentScripts.splice(index, 1)[0];
                      console.log(`🗑️ 删除脚本: ${removed.name}`);
                      return true;
                  }
                  return false;
              };

              // 🔑 更新脚本（关键方法）
              obj.updateScript = function(scriptId, updates) {
                  if (!this.componentScripts) return false;
                  const script = this.componentScripts.find(s => s.id === scriptId);
                  if (script) {
                      // 更新脚本属性
                      Object.assign(script, updates);
                      // console.log(`📝 更新脚本: ${script.name}`, updates);
                      return true;
                  }
                  console.warn(`⚠️ 未找到脚本: ${scriptId}`);
                  return false;
              };

              // 启用/禁用脚本
              obj.setScriptEnabled = function(scriptId, enabled) {
                  if (!this.componentScripts) return false;
                  const script = this.componentScripts.find(s => s.id === scriptId);
                  if (script) {
                      script.enabled = enabled;
                      console.log(`${enabled ? '✅' : '❌'} ${enabled ? '启用' : '禁用'}脚本: ${script.name}`);
                      return true;
                  }
                  return false;
              };

              // 🔑 切换脚本启用状态（ScriptPanel需要的方法）
              obj.toggleScript = function(scriptId, enabled) {
                  return this.setScriptEnabled(scriptId, enabled);
              };

              // 获取脚本
              obj.getScript = function(scriptId) {
                  if (!this.componentScripts) return null;
                  return this.componentScripts.find(s => s.id === scriptId) || null;
              };

              // 获取所有启用的脚本
              obj.getEnabledScripts = function() {
                  if (!this.componentScripts) return [];
                  return this.componentScripts.filter(s => s.enabled);
              };

              // 执行脚本中的方法
              obj.executeScriptMethod = function(methodName, ...args) {
                  const enabledScripts = this.getEnabledScripts();
                  const results = [];

                  // console.log(`🔧 UIScriptManager: 执行脚本方法 ${methodName}`, {
                  //     componentType: this.constructor.name,
                  //     enabledScriptsCount: enabledScripts.length,
                  //     args: args
                  // });

                  for (const script of enabledScripts) {
                      try {
                          // 🔑 修复：创建正确的脚本执行环境
                          // 使用with语句和eval来确保函数定义能够正确添加到上下文
                          const scriptContext = { self: this };

                          // 创建一个包装函数，在其中执行脚本代码
                          const wrappedCode = `
                              (function() {
                                  ${script.code}

                                  // 将所有函数添加到上下文对象
                                  // 🔑 通用生命周期方法
                                  if (typeof onStart === 'function') this.onStart = onStart;
                                  if (typeof onUpdate === 'function') this.onUpdate = onUpdate;
                                  if (typeof onFieldUpdate === 'function') this.onFieldUpdate = onFieldUpdate;
                                  if (typeof onDestroy === 'function') this.onDestroy = onDestroy;

                                  // 🔑 按钮相关方法
                                  if (typeof onClick === 'function') this.onClick = onClick;
                                  if (typeof onDoubleClick === 'function') this.onDoubleClick = onDoubleClick;
                                  if (typeof onHover === 'function') this.onHover = onHover;
                                  if (typeof onHoverOut === 'function') this.onHoverOut = onHoverOut;
                                  if (typeof onPress === 'function') this.onPress = onPress;
                                  if (typeof onRelease === 'function') this.onRelease = onRelease;

                                  // 🔑 滑动条/开关相关方法
                                  if (typeof onChange === 'function') this.onChange = onChange;

                                  // 🔑 UIInput特有方法
                                  if (typeof onFocus === 'function') this.onFocus = onFocus;
                                  if (typeof onBlur === 'function') this.onBlur = onBlur;
                                  if (typeof onEnterPressed === 'function') this.onEnterPressed = onEnterPressed;
                                  if (typeof onValidationFailed === 'function') this.onValidationFailed = onValidationFailed;
                              }).call(scriptContext);
                          `;

                          // 执行包装后的代码
                          eval(wrappedCode);

                          // 执行指定方法
                          if (typeof scriptContext[methodName] === 'function') {
                              // console.log(`✅ UIScriptManager: 执行脚本 [${script.name}::${methodName}]`);
                              const result = scriptContext[methodName].apply(this, args);
                              results.push(result);
                          } else {
                              //  console.log(`⚠️ UIScriptManager: 脚本 [${script.name}] 中未找到方法 ${methodName}`);
                          }
                      } catch (error) {
                          console.error(`❌ 脚本执行错误 [${script.name}::${methodName}]:`, error);
                      }
                  }

                  return results;
              };

              // 🔑 添加executeScript方法作为executeScriptMethod的别名（兼容性）
              obj.executeScript = function(methodName, ...args) {
                  return this.executeScriptMethod(methodName, ...args);
              };
          }
      };

      console.log('✅ UIScriptManager: 脚本管理器已加载');

  })();

  // ===== uiComponent.js =====
  /**
       * UI更新管理器
       * 使用 RPG Maker MZ 内置的更新循环来管理所有UI组件的更新
       */
      window.UIUpdateManager = {
          _components: [],
          _isHooked: false,

          /**
           * 注册组件到更新循环
           */
          register(component) {
              if (this._components.indexOf(component) === -1) {
                  this._components.push(component);
                  console.log(`🔧 UIUpdateManager: 注册组件 ${component.constructor.name}，总数: ${this._components.length}`);

                  // 确保已挂钩到更新循环
                  this._ensureUpdateHook();
              }
          },

          /**
           * 从更新循环注销组件
           */
          unregister(component) {
              const index = this._components.indexOf(component);
              if (index !== -1) {
                  this._components.splice(index, 1);
                  console.log(`🔧 UIUpdateManager: 注销组件 ${component.constructor.name}，总数: ${this._components.length}`);
              }
          },

          /**
           * 确保已挂钩到 RPG Maker MZ 的更新循环
           */
          _ensureUpdateHook() {
              if (this._isHooked) return;

              // 挂钩到 SceneManager 的更新循环
              if (window.SceneManager && window.SceneManager.updateMain) {
                  const originalUpdate = window.SceneManager.updateMain;
                  window.SceneManager.updateMain = function() {
                      // 调用原始更新
                      originalUpdate.call(this);

                      // 更新所有注册的UI组件
                      window.UIUpdateManager._updateComponents();
                  };

                  this._isHooked = true;
                  console.log('✅ UIUpdateManager: 已挂钩到 SceneManager.updateMain');
              }
          },

          /**
           * 更新所有注册的组件
           */
          _updateComponents() {
              for (let i = this._components.length - 1; i >= 0; i--) {
                  const component = this._components[i];
                  try {
                      if (component && component.update && typeof component.update === 'function') {
                          component.update();
                      }
                  } catch (error) {
                      console.error(`❌ UIUpdateManager: 组件更新失败`, {
                          component: component.constructor.name,
                          error: error.message
                      });

                      // 移除有问题的组件
                      this._components.splice(i, 1);
                  }
              }
          }
      };

      /**
       * UIComponent工具类
       * 提供静态方法为Sprite添加UIComponent功能
       */
      window.UIComponentUtils = {
          /**
           * 为Sprite添加UIComponent功能
           */
          applyToSprite(sprite, properties = {}) {
              console.log(`🔧 UIComponent: 为${sprite.constructor.name}添加UIComponent功能`, properties);

              // 🔑 组件标识
              sprite.isUIComponent = true;
              sprite.uiComponentType = sprite.constructor.name;
              sprite.componentId = properties.id || this.generateComponentId(sprite);

              // 🔑 基础属性
              sprite.name = properties.name || '';
              sprite.enabled = properties.enabled !== false;

              // 🔑 数据绑定系统（简化）
              sprite.dataBinding = properties.dataBinding || '';
              sprite._dataWatcher = null;
              sprite._isDataBound = false;

              // 🔑 脚本执行控制
              sprite._scriptExecutionEnabled = properties.scriptExecutionEnabled !== false;

              // 🔑 更新循环控制
              sprite._updateEnabled = properties.updateEnabled !== false;
              sprite._lastUpdateTime = Date.now();
              sprite._isRegisteredForUpdate = false;

              // 🔑 生命周期状态
              sprite._isCreated = false;
              sprite._isStarted = false;
              sprite._isDestroyed = false;

              // 🔑 拖拽重排序状态标记
              sprite._isBeingReordered = false;



              // 🔑 初始化数据监听器数组
              sprite._dataWatchers = [];

              // 添加方法
              this.addMethods(sprite);

              return sprite;
          },

          /**
           * 生成组件ID
           */
          generateComponentId(sprite) {
              return `${sprite.constructor.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          },







          /**
           * 为sprite添加Scene_Base相关方法
           */
          addSceneBaseMethods(sprite) {
              // 🔑 淡出速度方法
              sprite.fadeSpeed = function() {
                  return 24; // Scene_Base默认淡出速度
              };

              sprite.slowFadeSpeed = function() {
                  return this.fadeSpeed() * 2; // 慢速淡出 = 普通速度 * 2
              };

              // 🔑 只淡出音频（不需要Scene_Base实例）
              sprite.fadeOutAllAudio = function() {
                  const time = this.slowFadeSpeed() / 60;
                  console.log('🎵 UIComponent: 淡出所有音频', { time });

                  try {
                      if (window.AudioManager) {
                          window.AudioManager.fadeOutBgm(time);
                          window.AudioManager.fadeOutBgs(time);
                          window.AudioManager.fadeOutMe(time);
                          console.log('✅ UIComponent: 音频淡出成功');
                      } else {
                          console.warn('⚠️ UIComponent: AudioManager 不可用');
                      }
                  } catch (error) {
                      console.error('❌ UIComponent: 音频淡出失败', error);
                  }
              };

              // 🔑 开始画面淡出
              sprite.startFadeOut = function(duration, white) {
                  duration = duration || this.slowFadeSpeed();
                  white = white || false;

                  console.log('🎬 UIComponent: 开始画面淡出', { duration, white });

                  try {
                      const currentScene = window.SceneManager && window.SceneManager._scene;

                      if (currentScene && typeof currentScene.startFadeOut === 'function') {
                          currentScene.startFadeOut(duration, white);
                          console.log('✅ UIComponent: 画面淡出成功');
                      } else {
                          console.warn('⚠️ UIComponent: 当前场景不支持画面淡出');
                      }
                  } catch (error) {
                      console.error('❌ UIComponent: 画面淡出失败', error);
                  }
              };

              // 🔑 完整的fadeOutAll功能（音频 + 画面）
              sprite.fadeOutAll = function() {
                  console.log('🎭 UIComponent: 执行完整淡出 (音频 + 画面)');

                  // 先淡出音频
                  this.fadeOutAllAudio();

                  // 再淡出画面
                  this.startFadeOut(this.slowFadeSpeed());

                  console.log('✅ UIComponent: 完整淡出执行完成');
              };

              // 🔑 其他Scene_Base常用方法
              sprite.checkGameover = function() {
                  console.log('💀 UIComponent: 检查游戏结束状态');

                  try {
                      if (window.$gameParty && window.$gameParty.isAllDead()) {
                          console.log('💀 UIComponent: 队伍全灭，切换到游戏结束场景');
                          if (window.SceneManager && window.Scene_Gameover) {
                              window.SceneManager.goto(window.Scene_Gameover);
                          }
                      }
                  } catch (error) {
                      console.error('❌ UIComponent: 检查游戏结束失败', error);
                  }
              };

              sprite.popScene = function() {
                  console.log('🔙 UIComponent: 返回上一个场景');

                  try {
                      if (window.SceneManager) {
                          window.SceneManager.pop();
                          console.log('✅ UIComponent: 场景返回成功');
                      } else {
                          console.warn('⚠️ UIComponent: SceneManager 不可用');
                      }
                  } catch (error) {
                      console.error('❌ UIComponent: 场景返回失败', error);
                  }
              };
          },

          /**
           * 为sprite添加方法
           */
          addMethods(sprite) {
              // 🔑 智能数据路径解析器
              sprite.parseDataPath = function(dataPath) {
                  // 支持简化语法
                  const pathMappings = {
                      // 简化语法 -> 实际路径
                      '$gameParty._gold': { object: () => window.$gameParty, field: '_gold' },
                      '$gameVariables[': {
                          pattern: /^\$gameVariables\[(\d+)\]$/,
                          resolver: (match) => ({
                              object: () => window.$gameVariables._dataClass,
                              field: match[1]
                          })
                      },
                      '$gameSwitches[': {
                          pattern: /^\$gameSwitches\[(\d+)\]$/,
                          resolver: (match) => ({
                              object: () => window.$gameSwitches._dataClass,
                              field: match[1]
                          })
                      },
                      '$gameActors[': {
                          pattern: /^\$gameActors\[(\d+)\]\._exp$/,
                          resolver: (match) => ({
                              object: () => window.$gameActors._data[match[1]],
                              field: '_exp'
                          })
                      }
                  };

                  // 直接匹配
                  if (pathMappings[dataPath]) {
                      return pathMappings[dataPath];
                  }

                  // 模式匹配
                  for (const [key, mapping] of Object.entries(pathMappings)) {
                      if (mapping.pattern && mapping.resolver) {
                          const match = dataPath.match(mapping.pattern);
                          if (match) {
                              return mapping.resolver(match);
                          }
                      }
                  }

                  // 兜底：简单的点分割解析
                  const lastDotIndex = dataPath.lastIndexOf('.');
                  if (lastDotIndex > 0) {
                      const objectPath = dataPath.substring(0, lastDotIndex);
                      const fieldName = dataPath.substring(lastDotIndex + 1);

                      try {
                          return {
                              object: () => eval(objectPath),
                              field: fieldName
                          };
                      } catch (error) {
                          console.warn('🚨 UIComponent: 无法解析数据路径:', dataPath, error);
                          return null;
                      }
                  }

                  return null;
              };

              // 🔑 新的数据监听API - watchData (支持自定义对象)
              sprite.watchData = function(target, callback) {
                  console.log('🔧 UIComponent: 设置数据监听', { target, hasCallback: !!callback });

                  if (typeof callback !== 'function') {
                      console.warn('🚨 UIComponent: watchData 回调函数无效', { target, callback });
                      return null;
                  }

                  try {
                      // 🔑 支持多种监听方式
                      if (typeof target === 'string') {
                          // 字符串路径：监听全局对象属性
                          return this.watchObjectProperty(target, callback);
                      } else if (typeof target === 'object' && target !== null) {
                          // 对象：监听对象的所有属性变化
                          return this.watchObjectChanges(target, callback);
                      } else {
                          console.warn('🚨 UIComponent: 不支持的监听目标类型:', typeof target);
                          return null;
                      }

                  } catch (error) {
                      console.error('❌ UIComponent: 设置数据监听失败', error);
                      return null;
                  }
              };

              // 🔑 监听对象属性（字符串路径）
              sprite.watchObjectProperty = function(dataPath, callback) {
                  // 使用智能解析器
                  const parsed = this.parseDataPath(dataPath);
                  if (!parsed) {
                      console.warn('🚨 UIComponent: 无法解析数据路径:', dataPath);
                      return null;
                  }

                  const targetObject = parsed.object();
                  const fieldName = parsed.field;

                  if (!targetObject) {
                      console.warn('🚨 UIComponent: 目标对象不存在:', dataPath);
                      return null;
                  }

                  console.log('🎯 UIComponent: 设置对象属性监听', {
                      path: dataPath,
                      object: targetObject.constructor?.name || 'Unknown',
                      field: fieldName,
                      currentValue: targetObject[fieldName]
                  });

                  // 使用ProxyFieldWatcher监听字段变化
                  if (window.ProxyFieldWatcher) {
                      const watcher = window.ProxyFieldWatcher.watch(
                          targetObject,
                          fieldName,
                          (field, newValue, oldValue) => {
                              console.log(`🔄 UIComponent: 对象属性变化 ${dataPath}`, {
                                  field,
                                  newValue,
                                  oldValue
                              });

                              // 调用用户回调
                              try {
                                  callback(newValue, oldValue);
                              } catch (error) {
                                  console.error('❌ UIComponent: 数据监听回调执行失败', error);
                              }
                          }
                      );

                      // 添加到监听器数组
                      this._dataWatchers = this._dataWatchers || [];
                      this._dataWatchers.push({
                          path: dataPath,
                          watcher: watcher,
                          callback: callback
                      });

                      console.log('✅ UIComponent: 对象属性监听设置成功', {
                          path: dataPath,
                          totalWatchers: this._dataWatchers.length
                      });

                      return watcher;
                  } else {
                      console.warn('🚨 UIComponent: ProxyFieldWatcher未加载');
                      return null;
                  }
              };

              // 🔑 监听对象变化（对象引用）
              sprite.watchObjectChanges = function(targetObject, callback) {
                  console.log('🎯 UIComponent: 设置对象变化监听', {
                      object: targetObject.constructor?.name || 'Unknown',
                      keys: Object.keys(targetObject)
                  });

                  if (!window.ProxyFieldWatcher) {
                      console.warn('🚨 UIComponent: ProxyFieldWatcher未加载');
                      return null;
                  }

                  // 🔑 创建一个代理对象来监听所有属性变化
                  const watchers = [];

                  // 监听对象的所有可枚举属性
                  for (const key of Object.keys(targetObject)) {
                      console.log(`🔧 UIComponent: 为属性 ${key} 设置监听器，当前值:`, targetObject[key]);

                      const watcher = window.ProxyFieldWatcher.watch(
                          targetObject,
                          key,
                          (field, newValue, oldValue) => {
                              console.log(`🔄 UIComponent: 对象属性变化 ${key}`, {
                                  field,
                                  newValue,
                                  oldValue
                              });

                              // 调用用户回调，传递属性名
                              try {
                                  callback(newValue, oldValue, key);
                              } catch (error) {
                                  console.error('❌ UIComponent: 对象监听回调执行失败', error);
                              }
                          }
                      );

                      if (watcher) {
                          watchers.push(watcher);
                          console.log(`✅ UIComponent: 属性 ${key} 监听器设置成功`);
                      } else {
                          console.warn(`🚨 UIComponent: 属性 ${key} 监听器设置失败`);
                      }
                  }

                  // 添加到监听器数组
                  this._dataWatchers = this._dataWatchers || [];
                  this._dataWatchers.push({
                      path: `[Object:${targetObject.constructor?.name || 'Unknown'}]`,
                      watcher: {
                          unwatch: () => watchers.forEach(w => w.unwatch && w.unwatch())
                      },
                      callback: callback
                  });

                  console.log('✅ UIComponent: 对象变化监听设置成功', {
                      watchedProperties: Object.keys(targetObject),
                      successfulWatchers: watchers.length,
                      totalWatchers: this._dataWatchers.length
                  });

                  return {
                      unwatch: () => watchers.forEach(w => w.unwatch && w.unwatch())
                  };
              };

              // 🔑 重排序状态管理方法
              sprite.markAsBeingReordered = function() {
                  this._isBeingReordered = true;
                  console.log('🔄 UIComponent: 标记为重排序状态', this.constructor.name);
              };

              sprite.unmarkAsBeingReordered = function() {
                  this._isBeingReordered = false;
                  console.log('🔄 UIComponent: 取消重排序状态', this.constructor.name);
              };

              sprite.isBeingReordered = function() {
                  return this._isBeingReordered === true;
              };

              // 🔑 统一的字段更新方法 - 只更新自身
              sprite.onFieldUpdate = function(ctx) {
                  console.log('� UIComponent: 字段更新触发', {
                      componentId: this.componentId,
                      ctx: ctx
                  });

                  // 执行脚本中的 onFieldUpdate 方法
                  if (this.executeScript && typeof this.executeScript === 'function') {
                      try {
                          console.log(`🔧 UIComponent: 执行 onFieldUpdate 脚本`, this.componentId);
                          this.executeScript('onFieldUpdate', ctx);
                          console.log(`✅ UIComponent: onFieldUpdate 脚本执行完成`, this.componentId);
                      } catch (error) {
                          console.error(`❌ UIComponent: onFieldUpdate 脚本执行失败`, {
                              componentId: this.componentId,
                              error: error.message,
                              stack: error.stack
                          });
                      }
                  }
              };

              // 🔑 级联更新方法 - 更新自身和所有子对象
              sprite.updateFieldsToChildren = function(ctx) {
                  console.log('� UIComponent: 级联字段更新开始', {
                      componentId: this.componentId,
                      ctx: ctx
                  });

                  // 1. 先触发自身的 onFieldUpdate
                  if (typeof this.onFieldUpdate === 'function') {
                      this.onFieldUpdate(ctx);
                  }

                  // 2. 然后触发所有子对象的 onFieldUpdate
                  if (this.children && Array.isArray(this.children)) {
                      this.children.forEach((child, index) => {
                          if (child && typeof child.onFieldUpdate === 'function') {
                              try {
                                  console.log(`🔧 UIComponent: 通知子组件 ${index} 字段更新`);
                                  child.onFieldUpdate(ctx);
                              } catch (error) {
                                  console.error(`❌ UIComponent: 子组件 ${index} 字段更新失败`, error);
                              }
                          }
                      });
                  }

                  console.log('✅ UIComponent: 级联字段更新完成', this.componentId);
              };


              // 生命周期方法
              sprite.onAddedToParent = function() {
                  const self = this;
                  console.log('🔧 UIComponent: 组件被添加到父容器', self.constructor.name);

                  if (!self._isStarted && self._isCreated) {
                      self._isStarted = true;
                      if (self.executeScript && typeof self.executeScript === 'function') {
                          self.executeScript('onStart');
                      }

                      // 🔑 启动更新循环
                      if (self.startUpdateLoop && typeof self.startUpdateLoop === 'function') {
                          self.startUpdateLoop();
                      }

                      // 启动子组件
                      if (self.children) {
                          self.children.forEach(child => {
                              if (child.isUIComponent && !child._isStarted) {
                                  child.onAddedToParent();
                              }
                          });
                      }
                  }




              };

              // 🔑 清理所有数据监听器
              sprite.clearAllDataWatchers = function() {
                  console.log('🔧 UIComponent: 清理所有数据监听器');

                  if (this._dataWatchers && this._dataWatchers.length > 0) {
                      this._dataWatchers.forEach((watcherInfo, index) => {
                          try {
                              if (watcherInfo.watcher && watcherInfo.watcher.unwatch) {
                                  watcherInfo.watcher.unwatch();
                                  console.log(`✅ UIComponent: 数据监听器已清理 ${watcherInfo.path}`);
                              }
                          } catch (error) {
                              console.error(`❌ UIComponent: 清理数据监听器失败 ${watcherInfo.path}`, error);
                          }
                      });

                      this._dataWatchers = [];
                      console.log('✅ UIComponent: 所有数据监听器已清理');
                  } else {
                      console.log('ℹ️ UIComponent: 没有需要清理的数据监听器');
                  }
              };

              sprite.onRemovedFromParent = function() {
                  const self = this;
                  console.log('🔧 UIComponent: 组件从父容器移除', self.constructor.name);

                  // 🔑 检查是否在重排序过程中
                  if (self._isBeingReordered) {
                      console.log('🔄 UIComponent: 跳过销毁逻辑 - 正在重排序', self.constructor.name);
                      return; // 重排序时不执行销毁逻辑
                  }

                  // 🔑 执行销毁脚本
                  if (self.executeScript && typeof self.executeScript === 'function') {
                      self.executeScript('onDestroy');
                  }

                  // 🔑 停止更新循环
                  if (self.stopUpdateLoop && typeof self.stopUpdateLoop === 'function') {
                      self.stopUpdateLoop();
                  }

                  // 🔑 清理所有数据监听器
                  if (self.clearAllDataWatchers) {
                      self.clearAllDataWatchers();
                  }

                  // 🔑 检查是否需要调用 destroy 方法
                  // 注意：不直接调用 destroy，因为 BaseObjectModel 会负责调用
                  // 这里只标记为已销毁，避免重复调用
                  if (typeof self.destroy === 'function' && !self._isDestroyed) {
                      self._isDestroyed = true; // 标记为已销毁，防止重复调用
                      console.log('� UIComponent: 标记对象为已销毁，由 BaseObjectModel 负责实际销毁', self.constructor.name);

                      // 不再直接调用 destroy，避免与 BaseObjectModel 的销毁逻辑冲突
                      // BaseObjectModel.removeFromParent() 会负责安全地调用 destroy
                  }
              };

              // 🔑 更新方法
              sprite.update = function() {
                  if (!this._updateEnabled || !this._isStarted) return;

                  const currentTime = Date.now();
                  const deltaTime = this._lastUpdateTime ? currentTime - this._lastUpdateTime : 16;
                  this._lastUpdateTime = currentTime;

                  // 执行 onUpdate 脚本
                  if (this.executeScript && typeof this.executeScript === 'function') {
                      this.executeScript('onUpdate', { deltaTime, currentTime });
                  }
              };

              // 🔑 注册到全局更新管理器
              sprite.startUpdateLoop = function() {
                  if (this._isRegisteredForUpdate) return; // 避免重复注册

                  // 注册到全局更新管理器
                  if (window.UIUpdateManager) {
                      window.UIUpdateManager.register(this);
                      this._isRegisteredForUpdate = true;
                  }
              };

              // 🔑 从全局更新管理器注销
              sprite.stopUpdateLoop = function() {
                  if (!this._isRegisteredForUpdate) return;

                  // 从全局更新管理器注销
                  if (window.UIUpdateManager) {
                      window.UIUpdateManager.unregister(this);
                      this._isRegisteredForUpdate = false;
                  }
              };

              // 🔑 添加Scene_Base相关方法
              this.addSceneBaseMethods(sprite);

              // 事件绑定 - 使用更准确的事件名称
              sprite.on('added', sprite.onAddedToParent.bind(sprite));
              sprite.on('removed', sprite.onRemovedFromParent.bind(sprite));
          }
      };

  //     // 🔑 为 PIXI.Container 原型添加 UIComponent 方法，确保嵌套容器也能参与调用链
  //     if (window.PIXI && window.PIXI.Container && window.PIXI.Container.prototype) {
  //         console.log('🔧 UIComponent: 为 PIXI.Container 原型添加 UIComponent 方法');

  //         // 添加 onFieldUpdate 方法（如果不存在）
  //         if (!window.PIXI.Container.prototype.onFieldUpdate) {
  //             window.PIXI.Container.prototype.onFieldUpdate = function(ctx) {
  //                 console.log('🔄 PIXI.Container: onFieldUpdate 被调用', {
  //                     containerName: this.name || 'unnamed',
  //                     hasChildren: !!(this.children && this.children.length > 0),
  //                     childrenCount: this.children ? this.children.length : 0,
  //                     ctx: ctx
  //                 });

  //                 // 不执行脚本（因为普通 Container 没有脚本系统）
  //                 // 但是会传递给子对象
  //                 if (this.children && Array.isArray(this.children)) {
  //                     this.children.forEach((child, index) => {
  //                         if (child && typeof child.onFieldUpdate === 'function') {
  //                             try {
  //                                 console.log(`🔧 PIXI.Container: 通知子组件 ${index} 字段更新`);
  //                                 child.onFieldUpdate(ctx);
  //                             } catch (error) {
  //                                 console.error(`❌ PIXI.Container: 子组件 ${index} 字段更新失败`, error);
  //                             }
  //                         }
  //                     });
  //                 }
  //             };
  //         }

  //         // 添加 updateFieldsToChildren 方法（如果不存在）
  //         if (!window.PIXI.Container.prototype.updateFieldsToChildren) {
  //             window.PIXI.Container.prototype.updateFieldsToChildren = function(ctx) {
  //                 console.log('🔄 PIXI.Container: updateFieldsToChildren 被调用', {
  //                     containerName: this.name || 'unnamed',
  //                     hasChildren: !!(this.children && this.children.length > 0),
  //                     childrenCount: this.children ? this.children.length : 0,
  //                     ctx: ctx
  //                 });

  //                 // 1. 先触发自身的 onFieldUpdate（如果有的话）
  //                 if (typeof this.onFieldUpdate === 'function') {
  //                     this.onFieldUpdate(ctx);
  //                 }

  //                 // 2. 然后触发所有子对象的 onFieldUpdate
  //                 if (this.children && Array.isArray(this.children)) {
  //                     this.children.forEach((child, index) => {
  //                         if (child && typeof child.onFieldUpdate === 'function') {
  //                             try {
  //                                 console.log(`🔧 PIXI.Container: 通知子组件 ${index} 字段更新`);
  //                                 child.onFieldUpdate(ctx);
  //                             } catch (error) {
  //                                 console.error(`❌ PIXI.Container: 子组件 ${index} 字段更新失败`, error);
  //                             }
  //                         }
  //                     });
  //                 }
  //             };
  //         }
  //  // 🔑 添加 clone 方法（如果不存在）- 为 UILayout 模板功能提供支持
  //         if (!window.PIXI.Container.prototype.clone) {
  //             window.PIXI.Container.prototype.clone = function(options = {}) {
  //                 console.log('🔄 PIXI.Container: clone 被调用', {
  //                     containerName: this.name || 'unnamed',
  //                     hasChildren: !!(this.children && this.children.length > 0),
  //                     childrenCount: this.children ? this.children.length : 0,
  //                     options: options
  //                 });

  //                 const {
  //                     offsetPosition = true,
  //                     offsetX = 20,
  //                     offsetY = 20
  //                 } = options;

  //                 // 创建新的 Container
  //                 const cloned = new window.PIXI.Container();

  //                 // 复制基本属性
  //                 cloned.name = this.name ? `${this.name}_cloned` : 'cloned_container';
  //                 cloned.visible = this.visible;
  //                 cloned.alpha = this.alpha;
  //                 cloned.rotation = this.rotation;
  //                 cloned.scale.set(this.scale.x, this.scale.y);
  //                 cloned.skew.set(this.skew.x, this.skew.y);
  //                 cloned.pivot.set(this.pivot.x, this.pivot.y);

  //                 // 设置位置
  //                 if (offsetPosition) {
  //                     cloned.x = this.x + offsetX;
  //                     cloned.y = this.y + offsetY;
  //                 } else {
  //                     cloned.x = this.x;
  //                     cloned.y = this.y;
  //                 }

  //                 // 递归克隆所有子对象
  //                 if (this.children && this.children.length > 0) {
  //                     this.children.forEach(child => {
  //                         if (child && typeof child.clone === 'function') {
  //                             try {
  //                                 const clonedChild = child.clone({ offsetPosition: false });
  //                                 cloned.addChild(clonedChild);
  //                             } catch (error) {
  //                                 console.error('❌ PIXI.Container: 克隆子对象失败', error);
  //                             }
  //                         } else {
  //                             console.warn('⚠️ PIXI.Container: 子对象没有 clone 方法，跳过', child.constructor.name);
  //                         }
  //                     });
  //                 }

  //                 console.log('✅ PIXI.Container: 克隆完成', {
  //                     originalName: this.name,
  //                     clonedName: cloned.name,
  //                     originalChildren: this.children ? this.children.length : 0,
  //                     clonedChildren: cloned.children ? cloned.children.length : 0
  //                 });

  //                 return cloned;
  //             };
  //         }
  //         console.log('✅ UIComponent: PIXI.Container 原型方法添加完成');
  //     } else {
  //         console.warn('⚠️ UIComponent: PIXI.Container 不可用，跳过原型方法添加');
  //     }

      console.log('✅ UIComponent Plugin loaded - UIComponent functionality available');



  // ===== proxyFieldWatcher.js =====

  class ProxyFieldWatcher {
    static proxies = new WeakMap();
    static watchers = new WeakMap();
  
    /**
     * 创建代理对象并监听字段变化
     * @param {Object} target - 目标对象
     * @param {string|Array} fields - 要监听的字段名或字段数组
     * @param {Function} callback - 回调函数
     * @returns {Object} 代理对象和取消监听函数
     */
    static watch(target, fields, callback) {
      console.log('🔧 ProxyFieldWatcher.watch called:', { target: target.constructor?.name, fields });

      // 获取或创建监听器映射
      let watchers = this.watchers.get(target);
      if (!watchers) {
        watchers = new Map();
        this.watchers.set(target, watchers);
      }

      // 处理字段列表
      const fieldList = Array.isArray(fields) ? fields : [fields];

      // 为每个字段添加监听器
      fieldList.forEach(field => {
        if (!watchers.has(field)) {
          watchers.set(field, new Set());
        }
        watchers.get(field).add(callback);
        console.log(`🔧 ProxyFieldWatcher: 添加监听器 ${field}`);
      });

      // 🔑 关键修复：直接在原始对象上设置拦截器
      this.setupDirectInterception(target, fieldList);

      // 返回取消监听函数
      return {
        unwatch: () => {
          console.log('🔧 ProxyFieldWatcher: 取消监听');
          fieldList.forEach(field => {
            const fieldWatchers = watchers.get(field);
            if (fieldWatchers) {
              fieldWatchers.delete(callback);
              if (fieldWatchers.size === 0) {
                watchers.delete(field);
              }
            }
          });

          if (watchers.size === 0) {
            this.watchers.delete(target);
          }
        }
      };
    }

    /**
     * 直接在原始对象上设置属性拦截器
     */
    static setupDirectInterception(target, fieldList) {
      fieldList.forEach(field => {
        // 检查是否已经设置过拦截器
        const descriptor = Object.getOwnPropertyDescriptor(target, field);
        if (descriptor && descriptor.get && descriptor.get._isProxyFieldWatcher) {
          console.log(`🔧 ProxyFieldWatcher: ${field} 已经有拦截器，监听器已添加到现有拦截器`);
          return; // 拦截器已存在，但监听器已经在上面的代码中添加到 Set 中了
        }

        // 保存原始值
        let originalValue = target[field];

        // 🔑 如果是数组，设置数组方法拦截
        if (Array.isArray(originalValue)) {
          this.setupArrayInterception(originalValue, target, field);
        }

        // 重新定义属性
        Object.defineProperty(target, field, {
          get() {
            return originalValue;
          },
          set(newValue) {
            const oldValue = originalValue;
            originalValue = newValue;

            // 🔑 如果新值是数组，也要设置数组方法拦截
            if (Array.isArray(newValue)) {
              this.setupArrayInterception(newValue, target, field);
            }

            if (oldValue !== newValue) {
              console.log(`🔄 ProxyFieldWatcher: ${field} 变化 ${oldValue} -> ${newValue}`);

              // 通知所有监听器
              const watchers = ProxyFieldWatcher.watchers.get(target);
              if (watchers) {
                const fieldWatchers = watchers.get(field);
                if (fieldWatchers) {
                  console.log(`🔔 ProxyFieldWatcher: 通知 ${fieldWatchers.size} 个监听器 ${field} 变化`);
                  fieldWatchers.forEach((callback, index) => {
                    try {
                      console.log(`🔔 ProxyFieldWatcher: 调用监听器 ${index + 1}/${fieldWatchers.size}`);
                      callback(field, newValue, oldValue);
                    } catch (error) {
                      console.error('ProxyFieldWatcher callback error:', error);
                    }
                  });
                } else {
                  console.warn(`🚨 ProxyFieldWatcher: 没有找到 ${field} 的监听器`);
                }
              } else {
                console.warn(`🚨 ProxyFieldWatcher: 没有找到目标对象的监听器映射`);
              }
            }
          },
          enumerable: true,
          configurable: true
        });

        // 标记这个 getter 是由 ProxyFieldWatcher 创建的
        const newDescriptor = Object.getOwnPropertyDescriptor(target, field);
        if (newDescriptor && newDescriptor.get) {
          newDescriptor.get._isProxyFieldWatcher = true;
        }

        console.log(`🔧 ProxyFieldWatcher: 为 ${field} 设置了直接拦截器`);
      });
    }

    /**
     * 为数组设置方法拦截
     */
    static setupArrayInterception(array, parentObject, fieldName) {
      // 避免重复设置
      if (array._proxyFieldWatcherSetup) {
        return;
      }

      console.log(`🔧 ProxyFieldWatcher: 为数组 ${fieldName} 设置方法拦截`);

      // 需要拦截的数组方法
      const methodsToIntercept = [
        'push', 'pop', 'shift', 'unshift', 'splice',
        'sort', 'reverse', 'fill', 'copyWithin'
      ];

      methodsToIntercept.forEach(methodName => {
        const originalMethod = array[methodName];
        if (typeof originalMethod === 'function') {
          array[methodName] = function(...args) {
            console.log(`🔄 ProxyFieldWatcher: 数组方法 ${fieldName}.${methodName} 被调用`);

            // 保存旧的数组状态（浅拷贝）
            const oldArray = [...this];

            // 调用原始方法
            const result = originalMethod.apply(this, args);

            // 通知监听器数组发生了变化
            const watchers = ProxyFieldWatcher.watchers.get(parentObject);
            if (watchers) {
              const fieldWatchers = watchers.get(fieldName);
              if (fieldWatchers) {
                fieldWatchers.forEach(callback => {
                  try {
                    callback(fieldName, this, oldArray);
                  } catch (error) {
                    console.error('ProxyFieldWatcher array callback error:', error);
                  }
                });
              }
            }

            return result;
          };
        }
      });

      // 拦截数组元素的直接赋值 array[index] = value
      const originalLength = array.length;
      for (let i = 0; i < originalLength + 10; i++) { // 预设一些索引
        this.setupArrayIndexInterception(array, i, parentObject, fieldName);
      }

      // 标记已设置
      array._proxyFieldWatcherSetup = true;
    }

    /**
     * 为数组索引设置拦截
     */
    static setupArrayIndexInterception(array, index, parentObject, fieldName) {
      const descriptor = Object.getOwnPropertyDescriptor(array, index);
      if (descriptor && descriptor.get && descriptor.get._isProxyFieldWatcher) {
        return; // 已经设置过
      }

      let currentValue = array[index];

      Object.defineProperty(array, index, {
        get() {
          return currentValue;
        },
        set(newValue) {
          if (currentValue !== newValue) {
            console.log(`🔄 ProxyFieldWatcher: 数组元素 ${fieldName}[${index}] 变化`);

            const oldValue = currentValue;
            currentValue = newValue;

            // 通知监听器
            const watchers = ProxyFieldWatcher.watchers.get(parentObject);
            if (watchers) {
              const fieldWatchers = watchers.get(fieldName);
              if (fieldWatchers) {
                fieldWatchers.forEach(callback => {
                  try {
                    callback(fieldName, array, array); // 传递整个数组
                  } catch (error) {
                    console.error('ProxyFieldWatcher array index callback error:', error);
                  }
                });
              }
            }
          }
        },
        enumerable: true,
        configurable: true
      });

      if (Object.getOwnPropertyDescriptor(array, index).get) {
        Object.getOwnPropertyDescriptor(array, index).get._isProxyFieldWatcher = true;
      }
    }

    static createProxy(target) {
      const watchers = this.watchers;
    
      return new Proxy(target, {
        set(obj, property, value) {
          const oldValue = obj[property];
          const result = Reflect.set(obj, property, value);
        
          if (oldValue !== value) {
            const objectWatchers = watchers.get(target);
            if (objectWatchers) {
              const fieldWatchers = objectWatchers.get(property);
              if (fieldWatchers) {
                fieldWatchers.forEach(callback => {
                  try {
                    callback(property, value, oldValue);
                  } catch (error) {
                    console.error('ProxyFieldWatcher callback error:', error);
                  }
                });
              }
            }
          }
        
          return result;
        },
      
        get(obj, property) {
          return Reflect.get(obj, property);
        }
      });
    }

    /**
     * 🔧 调试方法：显示当前所有监听器状态
     */
    static debugWatchers() {
      console.log('🔍 ProxyFieldWatcher 调试信息:');

      let totalObjects = 0;
      let totalFields = 0;
      let totalWatchers = 0;

      this.watchers.forEach((objectWatchers, target) => {
        totalObjects++;
        console.log(`📦 对象: ${target.constructor?.name || 'Unknown'}`);

        objectWatchers.forEach((fieldWatchers, fieldName) => {
          totalFields++;
          totalWatchers += fieldWatchers.size;
          console.log(`  📝 字段 ${fieldName}: ${fieldWatchers.size} 个监听器`);

          // 显示每个监听器的信息
          let index = 0;
          fieldWatchers.forEach((callback) => {
            index++;
            console.log(`    🔔 监听器 ${index}: ${callback.name || 'anonymous'}`);
          });
        });
      });

      console.log(`📊 总计: ${totalObjects} 个对象, ${totalFields} 个字段, ${totalWatchers} 个监听器`);
    }

    /**
     * 🔧 调试方法：检查特定字段的监听器
     */
    static debugField(target, fieldName) {
      console.log(`🔍 检查字段 ${fieldName} 的监听器:`);

      const watchers = this.watchers.get(target);
      if (!watchers) {
        console.log('❌ 没有找到目标对象的监听器映射');
        return;
      }

      const fieldWatchers = watchers.get(fieldName);
      if (!fieldWatchers) {
        console.log(`❌ 没有找到字段 ${fieldName} 的监听器`);
        return;
      }

      console.log(`✅ 找到 ${fieldWatchers.size} 个监听器:`);
      let index = 0;
      fieldWatchers.forEach((callback) => {
        index++;
        console.log(`  🔔 监听器 ${index}: ${callback.name || 'anonymous'}`);
      });
    }
  }
      // 将类添加到全局
      window.ProxyFieldWatcher = ProxyFieldWatcher;


  // ===== UIAtlas.js =====
  // 确保PIXI可用
      if (typeof PIXI === 'undefined') {
          console.error('🎨 UIAtlas: PIXI未定义，插件加载失败');
          return;
      }

      /**
       * UIAtlas类 - 智能图集容器
       * 继承自Container，可以在编辑模式（显示子对象）和合并模式（单一纹理）之间切换
       */
      class UIAtlas extends PIXI.Container {
          constructor() {
              // 初始化为Container
              super();

              // 设置默认尺寸（使用自定义属性，避免影响子对象缩放）
              this._atlasWidth = 512;
              this._atlasHeight = 512;

              // 合并模式下的Sprite（用于显示合并纹理）
              this._atlasSprite = null;

              // 模式控制
              this._isAtlasMode = false;  // false: 编辑模式, true: 合并模式

              // 子对象管理（内部数组，不使用PIXI的children）
              this._children = [];        // 内部子对象数组
              this._boundingBox = null;   // 编辑模式的边界框

              // 渲染相关
              this._canvas = null;
              this._context = null;

              // 创建边界框（用于编辑时的视觉提示）
               this._createBoundingBox();

              console.log('🎨 UIAtlas: 创建智能图集容器', {
                  width: this._atlasWidth,
                  height: this._atlasHeight,
                  mode: 'edit'
              });
          }

          /**
           * 获取图集宽度
           */
          get width() {
              return this._atlasWidth;
          }

          /**
           * 设置图集宽度
           */
          set width(value) {
              this._atlasWidth = value;
              this._updateBoundingBox();
          }

          /**
           * 获取图集高度
           */
          get height() {
              return this._atlasHeight;
          }

          /**
           * 设置图集高度
           */
          set height(value) {
              this._atlasHeight = value;
              this._updateBoundingBox();
          }

          /**
           * 更新边界框尺寸
           * @private
           */
          _updateBoundingBox() {
              if (this._boundingBox) {
                  this._boundingBox.clear();
                  this._boundingBox.lineStyle(2, 0x00ff00, 0.8);
                  this._boundingBox.drawRect(0, 0, this._atlasWidth, this._atlasHeight);
                  this._boundingBox.alpha = 0.6;
              }
          }

          /**
           * 创建边界框（编辑模式下的视觉提示）
           * @private
           */
          _createBoundingBox() {
              this._boundingBox = new PIXI.Graphics();
              this._boundingBox.lineStyle(2, 0x00ff00, 0.8); // 绿色边框
              // 使用实际的图集尺寸绘制边界框
              this._boundingBox.drawRect(0, 0, this._atlasWidth, this._atlasHeight);
              this._boundingBox.alpha = 0.6;
              super.addChild(this._boundingBox);
          }

          /**
           * 检查子对象类型是否被允许
           * @param {object} child 子对象
           * @returns {boolean} 是否允许添加
           */
          _isAllowedChildType(child) {
              // 只允许UIImage和UILabel
              const allowedTypes = ['UIImage', 'UILabel'];
              const childType = child.constructor.name;
              const isAllowed = allowedTypes.includes(childType) ||
                               (child.className && allowedTypes.includes(child.className));

              if (!isAllowed) {
                  console.warn('🎨 UIAtlas: 不支持的子对象类型', childType, '只支持 UIImage 和 UILabel');
              }

              return isAllowed;
          }

          /**
           * 检查子对象是否有嵌套子对象（UIAtlas只支持一层嵌套）
           * @param {object} child 子对象
           * @returns {boolean} 是否有嵌套子对象
           */
          _hasNestedChildren(child) {
              // 检查子对象是否有children属性且不为空
              if (child.children && child.children.length > 0) {
                  console.warn('🎨 UIAtlas: 子对象包含嵌套子对象，UIAtlas只支持一层嵌套', {
                      childType: child.constructor.name,
                      nestedChildrenCount: child.children.length
                  });
                  return true;
              }
              return false;
          }

          /**
           * 禁用子对象的addChild方法，防止嵌套
           * @param {object} child 子对象
           * @private
           */
          _preventNesting(child) {
              // 保存原始的addChild方法
              if (!child._originalAddChild) {
                  child._originalAddChild = child.addChild;

                  // 重写addChild方法，禁止添加子对象
                  child.addChild = function(nestedChild) {
                      console.warn('🎨 UIAtlas: 禁止在UIAtlas的子对象中添加嵌套子对象', {
                          parentType: this.constructor.name,
                          childType: nestedChild.constructor.name,
                          message: 'UIAtlas只支持一层嵌套，请将子对象直接添加到UIAtlas'
                      });

                      // 可选：抛出错误以强制阻止
                      throw new Error('UIAtlas的子对象不允许添加嵌套子对象，请直接添加到UIAtlas');

                      // 或者返回null表示添加失败
                      // return null;
                  };

                  console.log('🎨 UIAtlas: 已禁用子对象的嵌套功能', child.constructor.name);
              }
          }

          /**
           * 恢复子对象的addChild方法
           * @param {object} child 子对象
           * @private
           */
          _restoreNesting(child) {
              if (child._originalAddChild) {
                  child.addChild = child._originalAddChild;
                  delete child._originalAddChild;
                  console.log('🎨 UIAtlas: 已恢复子对象的嵌套功能', child.constructor.name);
              }
          }

          /**
           * 自定义addChild方法，管理内部子对象数组
           * @param {object} child 要添加的子对象
           * @returns {object} 添加的子对象
           */
          addChild(child) {
              // 内部对象（边界框）直接添加到显示列表
              if (child === this._boundingBox) {
                  return super.addChild(child);
              }

              // 检查子对象类型
              if (!this._isAllowedChildType(child)) {
                  throw new Error(`UIAtlas只支持UIImage和UILabel类型的子对象，不支持: ${child.constructor.name}`);
              }

              // 检查子对象是否已有嵌套子对象
              if (this._hasNestedChildren(child)) {
                  throw new Error(`UIAtlas不支持嵌套子对象，请将子对象直接添加到UIAtlas。当前子对象 ${child.constructor.name} 包含 ${child.children ? child.children.length : 0} 个嵌套子对象。`);
              }

              // 检查是否已经在内部数组中
              if (this._children.includes(child)) {
                  console.warn('🎨 UIAtlas: 子对象已存在，跳过添加', child.name || 'unnamed');
                  return child;
              }

              // 禁用子对象的嵌套功能
              this._preventNesting(child);

              // 添加到内部子对象数组
              this._children.push(child);

              // 如果是编辑模式，添加到显示列表
              if (!this._isAtlasMode) {
                  super.addChild(child);
                  // 确保子对象可见
                  if (child.visible !== undefined) {
                      child.visible = true;
                  }
              }

              console.log('🎨 UIAtlas: 添加子对象', {
                  type: child.constructor.name,
                  name: child.name || 'unnamed',
                  mode: this._isAtlasMode ? 'atlas' : 'edit',
                  totalChildren: this._children.length,
                  nestingPrevented: true
              });

              return child;
          }

          /**
           * 自定义removeChild方法
           * @param {object} child 要移除的子对象
           * @returns {object} 移除的子对象
           */
          removeChild(child) {
              // 内部对象直接从显示列表移除
              if (child === this._boundingBox) {
                  return super.removeChild(child);
              }

              // 从内部数组移除
              const index = this._children.indexOf(child);
              if (index !== -1) {
                  this._children.splice(index, 1);

                  // 恢复子对象的嵌套功能
                  this._restoreNesting(child);
              }

              // 尝试从显示列表移除
              try {
                  super.removeChild(child);
              } catch (e) {
                  // 忽略移除失败的错误
              }

              console.log('🎨 UIAtlas: 移除子对象', {
                  type: child.constructor.name,
                  totalChildren: this._children.length,
                  nestingRestored: index !== -1
              });

              return child;
          }



          /**
           * 检查是否处于合并模式
           * @returns {boolean} 是否为合并模式
           */
          isAtlasMode() {
              return this._isAtlasMode;
          }



          /**
           * 切换到编辑模式
           * 恢复空纹理，重新显示所有子对象
           * @returns {UIAtlas} 返回自身，支持链式调用
           */
          switchToEditMode() {
              if (!this._isAtlasMode) {
                  console.log('🎨 UIAtlas: 已经处于编辑模式');
                  return this;
              }

              console.log('🎨 UIAtlas: 解开合并，切换到编辑模式');

              // 移除合并纹理Sprite
              if (this._atlasSprite) {
                  super.removeChild(this._atlasSprite);
                  this._atlasSprite.destroy();
                  this._atlasSprite = null;
              }

              // 重新添加所有子对象到显示列表
              this._children.forEach(child => {
                  try {
                      super.addChild(child);
                  } catch (e) {
                      // 忽略添加失败的错误（可能已经在列表中）
                  }
                  child.visible = true;
              });

              // 显示边界框
              if (this._boundingBox) {
                  this._boundingBox.visible = true;
              }

              this._isAtlasMode = false;

              console.log('🎨 UIAtlas: 编辑模式已激活，可以编辑子对象', {
                  visibleChildren: this._children.length
              });

              return this;
          }



          /**
           * 切换到合并模式
           * 将所有子对象渲染到单一纹理，隐藏原始子对象，显示合并纹理
           * @returns {Promise<UIAtlas>} 返回Promise，支持异步操作
           */
          async switchToAtlasMode() {
              if (this._isAtlasMode) {
                  console.log('🎨 UIAtlas: 已经处于合并模式');
                  return this;
              }

              console.log('🎨 UIAtlas: 切换到合并模式', {
                  childrenCount: this._children.length,
                  size: `${this._atlasWidth}x${this._atlasHeight}`
              });

              try {
                  // 等待所有 UIImage 加载完成
                  await this._waitForImagesLoaded();

                  // 创建离屏Canvas
                  this._canvas = document.createElement('canvas');
                  this._canvas.width = this._atlasWidth;
                  this._canvas.height = this._atlasHeight;
                  this._context = this._canvas.getContext('2d');

                  // 渲染所有子对象到Canvas
                  await this._renderChildrenToCanvas();

                  // 创建合并纹理并创建Sprite显示
                  const dataURL = this._canvas.toDataURL('image/png');
                  const texture = PIXI.Texture.from(dataURL);

                  // 创建显示合并纹理的Sprite
                  this._atlasSprite = new PIXI.Sprite(texture);
                  super.addChild(this._atlasSprite);

                  // 从显示列表移除所有子对象（但保留在内部数组中）
                  this._children.forEach(child => {
                      // 直接尝试移除，如果不在列表中removeChild会忽略
                      try {
                          super.removeChild(child);
                      } catch (e) {
                          // 忽略移除失败的错误
                      }
                      // 标记为不可见，但不销毁
                      child.visible = false;
                  });

                  // 隐藏边界框
                  if (this._boundingBox) {
                      this._boundingBox.visible = false;
                  }

                  this._isAtlasMode = true;

                  console.log('🎨 UIAtlas: 合并模式切换完成，当前Sprite已包含合并纹理');
                  return this;

              } catch (error) {
                  console.error('🎨 UIAtlas: 切换到合并模式失败', error);
                  throw error;
              }
          }

          /**
           * 等待所有 UIImage 加载完成
           * @returns {Promise<void>}
           * @private
           */
          async _waitForImagesLoaded() {
              const imagePromises = [];

              this._children.forEach(child => {
                  if (child.constructor.name === 'UIImage' || child.uiComponentType === 'UIImage') {
                      // 检查图片是否已经加载完成
                      if (child.bitmap && child.bitmap.isReady && child.bitmap.isReady()) {
                          console.log('🎨 UIAtlas: UIImage 已加载完成', child.imagePath);
                          return;
                      }

                      // 创建等待图片加载的 Promise
                      const imagePromise = new Promise((resolve) => {
                          if (child.bitmap && typeof child.bitmap.addLoadListener === 'function') {
                              console.log('🎨 UIAtlas: 等待 UIImage 加载', child.imagePath);
                              child.bitmap.addLoadListener(() => {
                                  console.log('🎨 UIAtlas: UIImage 加载完成', child.imagePath);
                                  resolve();
                              });
                          } else {
                              // 如果没有 bitmap 或 addLoadListener，直接 resolve
                              console.log('🎨 UIAtlas: UIImage 无需等待', child.imagePath);
                              resolve();
                          }
                      });

                      imagePromises.push(imagePromise);
                  }
              });

              if (imagePromises.length > 0) {
                  console.log(`🎨 UIAtlas: 等待 ${imagePromises.length} 个图片加载完成`);
                  await Promise.all(imagePromises);
                  console.log('🎨 UIAtlas: 所有图片加载完成');
              } else {
                  console.log('🎨 UIAtlas: 没有需要等待的图片');
              }
          }

          /**
           * 渲染所有子对象到Canvas（内部方法）
           * @private
           */
          async _renderChildrenToCanvas() {
              for (const child of this._children) {
                  if (child.visible) {
                      await this._renderChildToCanvas(child);
                  }
              }
          }

          /**
           * 渲染单个子对象到Canvas（内部方法）
           * @private
           */
          async _renderChildToCanvas(child) {
              if (!child.visible) return;

              try {
                  // 根据子对象类型进行不同的渲染处理
                  if (child.texture && child.texture.baseTexture) {
                      // 处理有纹理的对象（如Sprite、UIImage等）
                      await this._renderTextureToCanvas(child);
                  } else if (child.text !== undefined) {
                      // 处理文字对象（如UILabel等）
                      this._renderTextToCanvas(child);
                  } else if (child.children && child.children.length > 0) {
                      // 处理容器对象，递归渲染子对象
                      for (const grandChild of child.children) {
                          await this._renderChildToCanvas(grandChild);
                      }
                  }
              } catch (error) {
                  console.warn('🎨 UIAtlas: 渲染子对象失败，跳过', child, error);
              }
          }

          /**
           * 渲染纹理对象到Canvas（内部方法）
           * @private
           */
          async _renderTextureToCanvas(child) {
              return new Promise((resolve) => {
                  try {
                      // 检查纹理是否有效
                      if (!child.texture || !child.texture.baseTexture) {
                          console.warn('🎨 UIAtlas: 子对象纹理无效', child.constructor.name);
                          resolve();
                          return;
                      }

                      const baseTexture = child.texture.baseTexture;

                      // 如果纹理还没有加载完成，等待加载
                      if (!baseTexture.valid) {
                          console.log('🎨 UIAtlas: 等待纹理加载完成', child.constructor.name);
                          baseTexture.once('loaded', () => {
                              this._renderTextureToCanvas(child).then(resolve);
                          });
                          return;
                      }

                      // 获取纹理源
                      const source = baseTexture.resource?.source;
                      if (!source) {
                          console.warn('🎨 UIAtlas: 纹理源为空', child.constructor.name);
                          resolve();
                          return;
                      }

                      // 检查是否是 UIImage 且有裁切信息
                      if (child.constructor.name === 'UIImage' && child.regions && child.regions.length > 0) {
                          // UIImage 有裁切信息，使用裁切渲染
                          const currentRegion = child.regions[child.currentRegionIndex || 0];
                          if (currentRegion) {
                              // 使用 9 参数的 drawImage 进行裁切渲染
                              this._context.drawImage(
                                  source,
                                  currentRegion.sx, currentRegion.sy, currentRegion.sw, currentRegion.sh, // 源图片裁切区域
                                  child.x, child.y, child.width || currentRegion.sw, child.height || currentRegion.sh // 目标位置和尺寸
                              );

                              console.log('🎨 UIAtlas: UIImage 裁切渲染成功', {
                                  type: child.constructor.name,
                                  region: currentRegion,
                                  sourceRect: `${currentRegion.sx},${currentRegion.sy},${currentRegion.sw},${currentRegion.sh}`,
                                  destRect: `${child.x},${child.y},${child.width || currentRegion.sw},${child.height || currentRegion.sh}`
                              });
                          } else {
                              console.warn('🎨 UIAtlas: UIImage 当前区域无效，使用完整图片');
                              // 回退到完整图片渲染
                              this._context.drawImage(
                                  source,
                                  child.x,
                                  child.y,
                                  child.width || source.width,
                                  child.height || source.height
                              );
                          }
                      } else {
                          // 普通纹理渲染（非 UIImage 或无裁切信息）
                          this._context.drawImage(
                              source,
                              child.x,
                              child.y,
                              child.width || source.width,
                              child.height || source.height
                          );

                          console.log('🎨 UIAtlas: 普通纹理渲染成功', {
                              type: child.constructor.name,
                              x: child.x,
                              y: child.y,
                              width: child.width || source.width,
                              height: child.height || source.height
                          });
                      }

                  } catch (error) {
                      console.warn('🎨 UIAtlas: 纹理渲染失败', error);
                  }
                  resolve();
              });
          }

          /**
           * 渲染文字对象到Canvas（内部方法）
           * @private
           */
          _renderTextToCanvas(child) {
              try {
                  // 设置字体样式
                  const fontSize = child.fontSize || child.style?.fontSize || 16;
                  const fontFamily = child.fontFamily || child.style?.fontFamily || 'GameFont';
                  const fontWeight = child.fontWeight || child.style?.fontWeight || 'normal';
                  const fillColor = child.textColor || child.style?.fill || '#000000';

                  this._context.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
                  this._context.fillStyle = fillColor;
                  this._context.textAlign = 'left';
                  this._context.textBaseline = 'top';

                  // 绘制文字
                  this._context.fillText(child.text, child.x, child.y);

                  console.log('🎨 UIAtlas: 文字对象渲染完成', {
                      text: child.text,
                      position: `${child.x},${child.y}`,
                      fontSize: fontSize
                  });

              } catch (error) {
                  console.error('🎨 UIAtlas: 文字对象渲染失败', error);
              }
          }

          /**
           * 获取有效子对象数量
           * @returns {number} 有效子对象数量
           */
          getChildrenCount() {
              return this._children.length;
          }



          /**
           * 检查是否可以合并（至少有一个有效子对象）
           * @returns {boolean} 是否可以合并
           */
          canMerge() {
              return this.getChildrenCount() > 0;
          }

          /**
           * 切换模式（编辑模式 ↔ 合并模式）
           * @returns {Promise<UIAtlas>} 返回Promise
           */
          async toggleMode() {
              if (this._isAtlasMode) {
                  return this.switchToEditMode();
              } else {
                  if (this.canMerge()) {
                      return await this.switchToAtlasMode();
                  } else {
                      console.warn('🎨 UIAtlas: 没有子对象可以合并');
                      return this;
                  }
              }
          }

          /**
           * 获取当前状态信息
           * @returns {object} 状态信息
           */
          getStatus() {
              return {
                  mode: this._isAtlasMode ? 'atlas' : 'edit',
                  childrenCount: this.getChildrenCount(),
                  canMerge: this.canMerge(),
                  hasTexture: this._isAtlasMode && this._atlasSprite !== null,
                  size: {
                      width: this._atlasWidth,
                      height: this._atlasHeight
                  }
              };
          }

          /**
           * 克隆当前 UIAtlas 对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UIAtlas} 克隆的 UIAtlas 对象
           */
          clone(options = {}) {
              console.log('🔄 UIAtlas: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = {
                  // 基础属性
                  width: this.atlasWidth,
                  height: this.atlasHeight,
                  visible: this.visible,

                  // UIAtlas 特有属性
                  backgroundColor: this.backgroundColor,
                  backgroundOpacity: this.backgroundOpacity
              };

              // 2. 创建克隆对象
              const clonedAtlas = new UIAtlas(cloneProperties);

              // 3. 设置位置和变换属性
              clonedAtlas.x = this.x + (offsetPosition ? offsetX : 0);
              clonedAtlas.y = this.y + (offsetPosition ? offsetY : 0);
              clonedAtlas.scale.x = this.scale.x;
              clonedAtlas.scale.y = this.scale.y;
              clonedAtlas.rotation = this.rotation;
              clonedAtlas.alpha = this.alpha;
              clonedAtlas.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedAtlas.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              // 5. 如果原始对象处于图集模式，克隆对象也进入图集模式
              if (this._isAtlasMode) {
                  clonedAtlas.mergeToAtlas().then(() => {
                      console.log('✅ UIAtlas: 克隆对象已自动合并到图集模式');
                  });
              }

              console.log('✅ UIAtlas: 克隆完成，包含', clonedChildren.length, '个子对象');
              return clonedAtlas;
          }

          /**
           * 销毁图集对象
           */
          destroy() {
              console.log('🎨 UIAtlas: 销毁智能图集Sprite');

              // 清理Canvas
              if (this._canvas) {
                  this._canvas = null;
                  this._context = null;
              }

              // 清理子对象数组
              this._children = [];

              // 重置状态
              this._isAtlasMode = false;

              // 调用父类销毁方法
              super.destroy();
          }
      }

      // 将UIAtlas类添加到全局作用域
      window.UIAtlas = UIAtlas;

      console.log('🎨 UIAtlas插件已加载');



  // ===== uiLabel.js =====
  //=============================================================================
      // UILabel Class - 专门的文本组件
      //=============================================================================

      /**
       * 文本组件类 - 继承自 Sprite
       * 专门用于显示文本，不混合其他功能
       */
      class UILabel extends Sprite {
          constructor(properties = {}) {
              super();

              console.log('🏷️ UILabel: 创建文本组件', properties);

              // 标识为 UI 组件
              this.isUIComponent = true;
              this.uiComponentType = 'UILabel';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 使用UIScriptManager添加脚本功能
              if (window.UIScriptManager) {
                  window.UIScriptManager.applyToObject(this, properties);
                  console.log('🔧 UILabel: 脚本系统初始化完成', {
                      hasComponentScripts: !!this.componentScripts,
                      scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                      scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                  });
              }

              this.initializeLabel(properties);

              // 🔑 标记为已创建，onStart会在添加到父容器时执行
              this._isCreated = true;
          }


          /**
           * 初始化文本组件
           * @param {Object} properties 文本属性
           */
          initializeLabel(properties) {
              // � 设置为非交互式，避免拦截父容器的事件
              this.interactive = false;
              this.interactiveChildren = false;

              // 设置默认属性
              this.setupDefaultProperties(properties);

              // 设置文本位图
              this.setupTextBitmap();

              // 绘制文本
              this.redrawText();

              console.log('UILabel created:', this.labelWidth, 'x', this.labelHeight);
          }

          /**
           * 🔑 获取默认字体 - 优先使用 rmmz-mainfont
           */
          getDefaultFont() {
              // 检查 FontManager 中是否有 rmmz-mainfont
              if (typeof window !== 'undefined' && window.FontManager) {
                  const FontManager = window.FontManager;
                  if (FontManager._states && FontManager._states['rmmz-mainfont'] === 'loaded') {
                      console.log('🎨 UILabel: 使用 rmmz-mainfont 作为默认字体');
                      return 'rmmz-mainfont';
                  }
              }

              // 如果 rmmz-mainfont 不存在，使用 GameFont
              console.log('🎨 UILabel: 使用 GameFont 作为默认字体');
              return 'GameFont';
          }

          /**
           * 设置默认属性
           */
          setupDefaultProperties(properties) {
              // 基础属性 - 如果没有传入宽高，使用默认值
              this.labelWidth = properties.width || 200;
              this.labelHeight = properties.height || 40;

              // 文本属性 - 使用内部属性存储
              this._text = properties.text || 'Label Text';
              this.prefix = properties.prefix || '';  // 前缀
              this.suffix = properties.suffix || '';  // 后缀
              this.fontSize = properties.fontSize || 16;

              // 🔑 智能字体选择：优先使用 rmmz-mainfont，如果不存在则使用 GameFont
              if (!properties.fontFace) {
                  this.fontFace = this.getDefaultFont();
              } else {
                  this.fontFace = properties.fontFace;
              }
              this.fontBold = properties.fontBold || false;
              this.fontItalic = properties.fontItalic || false;

              // 颜色属性
              this.textColor = properties.textColor || '#ffffff';
              this.outlineColor = properties.outlineColor || '#000000';
              this.outlineWidth = properties.outlineWidth || 4;

              // 对齐属性
              this.textAlign = properties.textAlign || 'center'; // left, center, right
              this.verticalAlign = properties.verticalAlign || 'middle'; // top, middle, bottom
              // 🔧 新增：垂直偏移调节参数（可以为负数向上偏移，正数向下偏移）
              this.verticalOffset = properties.verticalOffset || 0;

              // 🔑 间距属性
              this.letterSpacing = properties.letterSpacing || 0;
              this.wordSpacing = properties.wordSpacing || 0;
              this.lineHeight = properties.lineHeight || 1.2;

              // 🔑 渲染模式
              this.spacingMode = properties.spacingMode || 'auto'; // 'auto', 'bitmap', 'canvas'

              // 🔑 自动换行属性
              this.wordWrap = properties.wordWrap !== false; // 默认开启自动换行
              this.maxLines = properties.maxLines || 0; // 0表示不限制行数
              this.ellipsis = properties.ellipsis || '...'; // 省略号

              // 🔑 打字效果属性
              this.typewriterSpeed = properties.typewriterSpeed || 50; // 打字速度（毫秒/字符）
              this.typewriterDelay = properties.typewriterDelay || 0; // 开始前延迟（毫秒）
              this.typewriterCursor = properties.typewriterCursor !== false; // 是否显示光标
              this.typewriterCursorChar = properties.typewriterCursorChar || '|'; // 光标字符
              this.typewriterCursorBlink = properties.typewriterCursorBlink || 500; // 光标闪烁间隔

              // 🔑 打字效果状态
              this._typewriterState = {
                  isActive: false,        // 是否正在打字
                  isPaused: false,        // 是否暂停
                  currentIndex: 0,        // 当前显示到第几个字符
                  targetText: '',         // 目标文本
                  displayText: '',        // 当前显示的文本
                  timer: null,            // 定时器
                  cursorTimer: null,      // 光标闪烁定时器
                  showCursor: true,       // 光标显示状态
                  onComplete: null,       // 完成回调
                  onProgress: null        // 进度回调
              };

              // 背景属性
              this.backgroundColor = properties.backgroundColor || 'transparent';
              this.backgroundOpacity = properties.backgroundOpacity || 1;

              // 🔑 内边距属性（防止文本裁切）
              this.padding = properties.padding || 3; // 默认3像素内边距
              this.paddingTop = properties.paddingTop !== undefined ? properties.paddingTop : this.padding;
              this.paddingRight = properties.paddingRight !== undefined ? properties.paddingRight : this.padding;
              this.paddingBottom = properties.paddingBottom !== undefined ? properties.paddingBottom : this.padding;
              this.paddingLeft = properties.paddingLeft !== undefined ? properties.paddingLeft : this.padding;
          }

          /**
           * 获取标签宽度
           */
          get width() {
              return this.labelWidth;
          }

          /**
           * 设置标签宽度
           */
          set width(value) {
              this.labelWidth = value;
              this.setupTextBitmap();
              this.redrawText();
          }

          /**
           * 获取标签高度
           */
          get height() {
              return this.labelHeight;
          }

          /**
           * 设置标签高度
           */
          set height(value) {
              this.labelHeight = value;
              this.setupTextBitmap();
              this.redrawText();
          }

          /**
           * 设置文本位图
           */
          setupTextBitmap() {
              this.bitmap = new window.Bitmap(this.labelWidth, this.labelHeight);

              // 设置字体属性
              this.bitmap.fontSize = this.fontSize;
              this.bitmap.fontFace = this.fontFace;
              this.bitmap.fontBold = this.fontBold;
              this.bitmap.fontItalic = this.fontItalic;
              this.bitmap.textColor = this.textColor;
              this.bitmap.outlineColor = this.outlineColor;
              this.bitmap.outlineWidth = this.outlineWidth;
          }

          /**
           * 重绘文本
           */
          redrawText() {
              if (!this.bitmap) return;

              // 清除画布
              this.bitmap.clear();

              // 重新设置bitmap的所有属性（确保颜色正确）
              this.bitmap.fontSize = this.fontSize;
              this.bitmap.fontFace = this.fontFace;
              this.bitmap.fontBold = this.fontBold;
              this.bitmap.fontItalic = this.fontItalic;
              this.bitmap.textColor = this.textColor;
              this.bitmap.outlineColor = this.outlineColor;
              this.bitmap.outlineWidth = this.outlineWidth;

              console.log('🔧 UILabel: redrawText - textColor:', this.textColor, 'outlineColor:', this.outlineColor);

              // 绘制背景（如果需要）
              this.drawBackground();

              // 获取最终文本
              const finalText = this.getFinalText();

              // 根据是否需要间距选择渲染方法
              if (this.needsCustomSpacing()) {
                  this.drawTextWithSpacing(finalText);
              } else {
                  this.drawTextNormal(finalText);
              }
          }

          /**
           * 绘制背景
           */
          drawBackground() {
              if (this.backgroundColor !== 'transparent') {
                  this.bitmap.fillRect(0, 0, this.labelWidth, this.labelHeight, this.backgroundColor);
              }
          }

          /**
           * 获取最终显示文本
           */
          getFinalText() {
              // 处理文本内容（检查是否为表达式）
              let displayText = this._text;
              if (window.EDITOR_MODE && this._text && this._text.startsWith('{{') && this._text.endsWith('}}')) {
                  // 编辑器中用eval()预览表达式
                  try {
                      const expression = this._text.slice(2, -2); // 去掉{{}}
                      displayText = String(eval(expression)) || this._text;
                  } catch (error) {
                      console.warn('表达式预览失败:', this._text, error);
                      displayText = this._text; // 出错就显示原字符串
                  }
              }

              // 添加前缀和后缀
              return this.prefix + displayText + this.suffix;
          }

          /**
           * 检查是否需要自定义间距
           */
          needsCustomSpacing() {
              return this.letterSpacing > 0 ||
                     this.wordSpacing > 0 ||
                     this.lineHeight !== 1.2;
          }

          /**
           * 使用标准方法绘制文本
           */
          drawTextNormal(finalText) {
              if (this.wordWrap) {
                  // 🔑 多行文本绘制
                  this.drawMultiLineText(finalText);
              } else {
                  // 单行文本绘制
                  const textY = this.calculateTextY();
                  this.bitmap.drawText(
                      finalText,
                      0, textY,
                      this.labelWidth, this.fontSize,
                      this.textAlign
                  );
              }
          }

          /**
           * 🔑 绘制多行文本
           */
          drawMultiLineText(text) {
              const lines = this.wrapText(text);
              const lineHeight = this.fontSize * this.lineHeight;
              const totalHeight = lines.length * lineHeight;
              // 🔑 考虑内边距的可用高度
              const availableHeight = this.labelHeight - this.paddingTop - this.paddingBottom;
              const maxVisibleLines = Math.floor(availableHeight / lineHeight);

              let displayLines = lines;
              let startY = 0;

              // 🔑 如果文本超出高度，始终保持最新行可见
              if (totalHeight > availableHeight && lines.length > maxVisibleLines) {
                  // 显示最后几行，确保最新内容可见
                  displayLines = lines.slice(-maxVisibleLines);

                  // 根据垂直对齐方式调整起始位置
                  switch (this.verticalAlign) {
                      case 'top':
                          startY = this.paddingTop;
                          break;
                      case 'bottom':
                          startY = this.labelHeight - this.paddingBottom - (displayLines.length * lineHeight);
                          break;
                      case 'middle':
                      default:
                          startY = this.paddingTop + (availableHeight - (displayLines.length * lineHeight)) / 2;
                          break;
                  }
              } else {
                  // 文本没有超出高度，按正常的垂直对齐处理
                  switch (this.verticalAlign) {
                      case 'top':
                          startY = this.paddingTop;
                          break;
                      case 'bottom':
                          startY = this.labelHeight - this.paddingBottom - totalHeight;
                          break;
                      case 'middle':
                      default:
                          startY = this.paddingTop + (availableHeight - totalHeight) / 2;
                          break;
                  }
              }

              // 绘制每一行
              displayLines.forEach((line, index) => {
                  const y = startY + (index * lineHeight);
                  this.bitmap.drawText(
                      line,
                      0, y,
                      this.labelWidth, this.fontSize,
                      this.textAlign
                  );
              });
          }

          /**
           * 🔑 文本换行处理
           */
          wrapText(text) {
              if (!text) return [''];

              const words = text.split('');
              const lines = [];
              let currentLine = '';
              // 🔑 考虑内边距的可用宽度
              const availableWidth = this.labelWidth - this.paddingLeft - this.paddingRight;

              for (let i = 0; i < words.length; i++) {
                  const char = words[i];
                  const testLine = currentLine + char;
                  const testWidth = this.calculateTextWidth(testLine);

                  if (testWidth <= availableWidth) {
                      currentLine = testLine;
                  } else {
                      // 当前行已满，开始新行
                      if (currentLine) {
                          lines.push(currentLine);
                          currentLine = char;
                      } else {
                          // 单个字符就超宽，强制添加
                          lines.push(char);
                          currentLine = '';
                      }
                  }
              }

              // 添加最后一行
              if (currentLine) {
                  lines.push(currentLine);
              }

              // 处理最大行数限制
              if (this.maxLines > 0 && lines.length > this.maxLines) {
                  const truncatedLines = lines.slice(0, this.maxLines);

                  // 在最后一行添加省略号
                  if (truncatedLines.length > 0) {
                      const lastLine = truncatedLines[truncatedLines.length - 1];
                      let truncatedLine = lastLine;

                      // 逐步缩短最后一行直到能容纳省略号
                      while (truncatedLine.length > 0) {
                          const testLine = truncatedLine + this.ellipsis;
                          if (this.calculateTextWidth(testLine) <= this.labelWidth) {
                              truncatedLines[truncatedLines.length - 1] = testLine;
                              break;
                          }
                          truncatedLine = truncatedLine.slice(0, -1);
                      }

                      // 如果连省略号都放不下，就只显示省略号
                      if (truncatedLine.length === 0) {
                          truncatedLines[truncatedLines.length - 1] = this.ellipsis;
                      }
                  }

                  return truncatedLines;
              }

              return lines;
          }

          /**
           * 使用自定义间距绘制文本
           */
          drawTextWithSpacing(finalText) {
              if (this.wordWrap) {
                  // � 多行文本绘制（带字符间距）
                  this.drawMultiLineTextWithSpacing(finalText);
              } else {
                  // 单行文本绘制（带字符间距）
                  console.log('🔧 UILabel: 使用逐字符绘制，确保Y坐标一致');
                  this.drawCharByChar(finalText);
              }
          }

          /**
           * 🔑 绘制多行文本（带字符间距）
           */
          drawMultiLineTextWithSpacing(text) {
              const lines = this.wrapTextWithSpacing(text);
              const lineHeight = this.fontSize * this.lineHeight;
              const totalHeight = lines.length * lineHeight;
              // 🔑 考虑内边距的可用高度
              const availableHeight = this.labelHeight - this.paddingTop - this.paddingBottom;
              const maxVisibleLines = Math.floor(availableHeight / lineHeight);

              let displayLines = lines;
              let startY = 0;

              // 🔑 如果文本超出高度，始终保持最新行可见
              if (totalHeight > availableHeight && lines.length > maxVisibleLines) {
                  // 显示最后几行，确保最新内容可见
                  displayLines = lines.slice(-maxVisibleLines);

                  // 根据垂直对齐方式调整起始位置
                  switch (this.verticalAlign) {
                      case 'top':
                          startY = this.paddingTop;
                          break;
                      case 'bottom':
                          startY = this.labelHeight - this.paddingBottom - (displayLines.length * lineHeight);
                          break;
                      case 'middle':
                      default:
                          startY = this.paddingTop + (availableHeight - (displayLines.length * lineHeight)) / 2;
                          break;
                  }
              } else {
                  // 文本没有超出高度，按正常的垂直对齐处理
                  switch (this.verticalAlign) {
                      case 'top':
                          startY = this.paddingTop;
                          break;
                      case 'bottom':
                          startY = this.labelHeight - this.paddingBottom - totalHeight;
                          break;
                      case 'middle':
                      default:
                          startY = this.paddingTop + (availableHeight - totalHeight) / 2;
                          break;
                  }
              }

              // 绘制每一行（带字符间距）
              displayLines.forEach((line, index) => {
                  const y = startY + (index * lineHeight);
                  this.drawLineWithSpacing(line, y);
              });
          }

          /**
           * 🔑 文本换行处理（带字符间距）
           */
          wrapTextWithSpacing(text) {
              if (!text) return [''];

              const words = text.split('');
              const lines = [];
              let currentLine = '';
              // 🔑 考虑内边距的可用宽度
              const availableWidth = this.labelWidth - this.paddingLeft - this.paddingRight;

              for (let i = 0; i < words.length; i++) {
                  const char = words[i];
                  const testLine = currentLine + char;
                  const testWidth = this.calculateTextWidthWithSpacing(testLine);

                  if (testWidth <= availableWidth) {
                      currentLine = testLine;
                  } else {
                      // 当前行已满，开始新行
                      if (currentLine) {
                          lines.push(currentLine);
                          currentLine = char;
                      } else {
                          // 单个字符就超宽，强制添加
                          lines.push(char);
                          currentLine = '';
                      }
                  }
              }

              // 添加最后一行
              if (currentLine) {
                  lines.push(currentLine);
              }

              // 处理最大行数限制
              if (this.maxLines > 0 && lines.length > this.maxLines) {
                  const truncatedLines = lines.slice(0, this.maxLines);

                  // 在最后一行添加省略号
                  if (truncatedLines.length > 0) {
                      const lastLine = truncatedLines[truncatedLines.length - 1];
                      let truncatedLine = lastLine;

                      // 逐步缩短最后一行直到能容纳省略号
                      while (truncatedLine.length > 0) {
                          const testLine = truncatedLine + this.ellipsis;
                          if (this.calculateTextWidthWithSpacing(testLine) <= this.labelWidth) {
                              truncatedLines[truncatedLines.length - 1] = testLine;
                              break;
                          }
                          truncatedLine = truncatedLine.slice(0, -1);
                      }

                      // 如果连省略号都放不下，就只显示省略号
                      if (truncatedLine.length === 0) {
                          truncatedLines[truncatedLines.length - 1] = this.ellipsis;
                      }
                  }

                  return truncatedLines;
              }

              return lines;
          }

          /**
           * 🔑 绘制单行文本（带字符间距）
           */
          drawLineWithSpacing(line, y) {
              const characters = line.split('');
              let currentX = this.calculateStartXWithSpacing(line);

              characters.forEach((char, index) => {
                  // 绘制单个字符
                  this.bitmap.drawText(
                      char,
                      currentX, y,
                      this.getCharWidth(char), this.fontSize,
                      'left'
                  );

                  // 计算下一个字符的位置
                  const charWidth = this.getCharWidth(char);
                  currentX += charWidth;

                  // 添加字符间距
                  if (index < characters.length - 1) {
                      if (char === ' ') {
                          currentX += this.wordSpacing;
                      } else {
                          currentX += this.letterSpacing;
                      }
                  }
              });
          }

          /**
           * 🔑 计算文本起始X位置（带字符间距）
           */
          calculateStartXWithSpacing(text) {
              const totalWidth = this.calculateTextWidthWithSpacing(text);
              // 🔧 修复：为描边和内边距预留空间，避免裁切
              const outlinePadding = this.outlineWidth > 0 ? Math.ceil(this.outlineWidth / 2) : 0;
              const leftPadding = Math.max(this.paddingLeft, outlinePadding);
              const rightPadding = Math.max(this.paddingRight, outlinePadding);
              const availableWidth = this.labelWidth - leftPadding - rightPadding;

              switch (this.textAlign) {
                  case 'center':
                      return leftPadding + Math.max(0, (availableWidth - totalWidth) / 2);
                  case 'right':
                      return leftPadding + Math.max(0, availableWidth - totalWidth);
                  case 'left':
                  default:
                      return leftPadding;
              }
          }

          /**
           * 🔑 计算文本总宽度（带字符间距）
           */
          calculateTextWidthWithSpacing(text) {
              const characters = text.split('');
              let totalWidth = 0;

              characters.forEach((char, index) => {
                  totalWidth += this.getCharWidth(char);

                  // 添加间距
                  if (index < characters.length - 1) {
                      if (char === ' ') {
                          totalWidth += this.wordSpacing;
                      } else {
                          totalWidth += this.letterSpacing;
                      }
                  }
              });

              return totalWidth;
          }

          /**
           * 尝试使用 Canvas 2D Context 的 letterSpacing
           */
          tryCanvasSpacing(text) {
              const context = this.bitmap.context;
              if (!context || !('letterSpacing' in context)) {
                  return false;
              }

              try {
                  // 设置字体属性
                  context.font = this.buildFontString();
                  context.fillStyle = this.textColor;
                  context.strokeStyle = this.outlineColor;
                  context.lineWidth = this.outlineWidth;

                  // 🔑 设置字符间距
                  context.letterSpacing = `${this.letterSpacing}px`;

                  // 🔧 修复：Canvas 2D Context 基线调整
                  // Canvas 2D Context 从基线绘制，Bitmap.drawText 从顶部绘制
                  const topY = this.calculateTextY(); // 获取顶部位置
                  const textY = topY + this.fontSize * 0.85; // 基线约在85%位置
                  const textX = this.calculateStartX(text);

                  // 绘制描边
                  if (this.outlineWidth > 0) {
                      context.strokeText(text, textX, textY);
                  }

                  // 绘制填充
                  context.fillText(text, textX, textY);

                  return true;
              } catch (error) {
                  console.warn('Canvas letterSpacing 失败:', error);
                  return false;
              }
          }

          /**
           * 构建字体字符串
           */
          buildFontString() {
              let font = '';
              if (this.fontItalic) font += 'italic ';
              if (this.fontBold) font += 'bold ';
              font += `${this.fontSize}px `;
              font += this.fontFace;
              return font;
          }

          /**
           * 逐字符绘制文本
           */
          drawCharByChar(text) {
              const characters = text.split('');
              let currentX = this.calculateStartX(text);
              const textY = this.calculateTextY();

              characters.forEach((char, index) => {
                  // 绘制单个字符
                  // 🔧 修复：lineHeight 应该是字体大小，不是容器高度
                  this.bitmap.drawText(
                      char,
                      currentX, textY,
                      this.getCharWidth(char), this.fontSize,  // 使用 fontSize 作为 lineHeight
                      'left'
                  );

                  // 计算下一个字符的位置
                  const charWidth = this.getCharWidth(char);
                  currentX += charWidth;

                  // 添加字符间距
                  if (index < characters.length - 1) {
                      if (char === ' ') {
                          currentX += this.wordSpacing;
                      } else {
                          currentX += this.letterSpacing;
                      }
                  }
              });
          }

          /**
           * 获取字符宽度（带缓存优化）
           */
          getCharWidth(char) {
              // 创建缓存键
              const cacheKey = `${char}_${this.fontSize}_${this.fontFace}_${this.fontBold}_${this.fontItalic}`;

              // 检查缓存
              if (!this._charWidthCache) {
                  this._charWidthCache = {};
              }

              if (this._charWidthCache[cacheKey] !== undefined) {
                  return this._charWidthCache[cacheKey];
              }

              // 创建临时bitmap测量字符宽度
              const tempBitmap = new window.Bitmap(100, 100);
              tempBitmap.fontSize = this.fontSize;
              tempBitmap.fontFace = this.fontFace;
              tempBitmap.fontBold = this.fontBold;
              tempBitmap.fontItalic = this.fontItalic;

              const width = tempBitmap.measureTextWidth(char);

              // 缓存结果
              this._charWidthCache[cacheKey] = width;

              // 销毁临时bitmap
              tempBitmap.destroy();

              return width;
          }

          /**
           * 计算文本起始X位置
           */
          calculateStartX(text) {
              const totalWidth = this.calculateTextWidth(text);
              // 🔧 修复：为描边和内边距预留空间，避免裁切
              const outlinePadding = this.outlineWidth > 0 ? Math.ceil(this.outlineWidth / 2) : 0;
              const leftPadding = Math.max(this.paddingLeft, outlinePadding);
              const rightPadding = Math.max(this.paddingRight, outlinePadding);
              const availableWidth = this.labelWidth - leftPadding - rightPadding;

              switch (this.textAlign) {
                  case 'left':
                      return leftPadding;
                  case 'right':
                      return leftPadding + Math.max(0, availableWidth - totalWidth);
                  case 'center':
                  default:
                      return leftPadding + Math.max(0, (availableWidth - totalWidth) / 2);
              }
          }

          /**
           * 计算文本总宽度（包含间距）
           */
          calculateTextWidth(text) {
              if (!this.needsCustomSpacing()) {
                  // 使用标准测量
                  const tempBitmap = new window.Bitmap(1000, 100);
                  tempBitmap.fontSize = this.fontSize;
                  tempBitmap.fontFace = this.fontFace;
                  tempBitmap.fontBold = this.fontBold;
                  tempBitmap.fontItalic = this.fontItalic;
                  return tempBitmap.measureTextWidth(text);
              }

              // 计算带间距的宽度
              const characters = text.split('');
              let totalWidth = 0;

              characters.forEach((char, index) => {
                  totalWidth += this.getCharWidth(char);

                  // 添加间距
                  if (index < characters.length - 1) {
                      if (char === ' ') {
                          totalWidth += this.wordSpacing;
                      } else {
                          totalWidth += this.letterSpacing;
                      }
                  }
              });

              return totalWidth;
          }



          /**
           * 计算文本Y位置（垂直对齐）
           * 🔧 修复：考虑内边距，防止文本裁切
           */
          calculateTextY() {
              // 🔑 考虑内边距的可用高度
              const availableHeight = this.labelHeight - this.paddingTop - this.paddingBottom;

              switch (this.verticalAlign) {
                  case 'top':
                      // 🔧 顶部对齐：从顶部内边距开始
                      return this.paddingTop + (this.verticalOffset || 0);
                  case 'bottom':
                      // 🔧 底部对齐：从底部内边距减去字体大小
                      return this.labelHeight - this.paddingBottom - this.fontSize + (this.verticalOffset || 0);
                  case 'middle':
                  default:
                      // 🔧 居中对齐：在可用空间内居中
                      return this.paddingTop + (availableHeight - this.fontSize) / 2 + (this.verticalOffset || 0);
              }
          }

          /**
           * 🔑 text 属性的 getter
           */
          get text() {
              return this._text;
          }

          /**
           * 🔑 text 属性的 setter - 统一的文本设置方式，自动转换类型
           */
          set text(value) {
              // 🔑 自动类型转换
              let stringValue;
              if (value === null || value === undefined) {
                  stringValue = '';
              } else if (typeof value === 'string') {
                  stringValue = value;
              } else if (typeof value === 'number') {
                  stringValue = value.toString();
              } else if (typeof value === 'boolean') {
                  stringValue = value ? 'true' : 'false';
              } else if (typeof value === 'object') {
                  try {
                      // 对于对象，尝试 JSON 序列化
                      stringValue = JSON.stringify(value);
                  } catch (error) {
                      // 如果序列化失败，使用 toString
                      stringValue = value.toString();
                  }
              } else {
                  // 其他类型直接转换
                  stringValue = String(value);
              }

              if (this._text !== stringValue) {
                  this._text = stringValue;
                  this.redrawText();
              }
          }

          /**
           * 设置文本内容（保持向后兼容）
           * @deprecated 推荐直接使用 this.text = value
           */
          setText(text) {
              this.text = text;
          }

          /**
           * 设置前缀
           */
          setPrefix(prefix) {
              this.prefix = prefix;
              this.redrawText();
          }

          /**
           * 设置后缀
           */
          setSuffix(suffix) {
              this.suffix = suffix;
              this.redrawText();
          }

          /**
           * 设置字符间距
           */
          setLetterSpacing(spacing) {
              this.letterSpacing = spacing;
              this.redrawText();
          }

          /**
           * 设置单词间距
           */
          setWordSpacing(spacing) {
              this.wordSpacing = spacing;
              this.redrawText();
          }

          /**
           * 设置行高
           */
          setLineHeight(height) {
              this.lineHeight = height;
              this.redrawText();
          }

          /**
           * 批量设置间距
           */
          setSpacing(options) {
              if (options.letter !== undefined) {
                  this.letterSpacing = options.letter;
              }
              if (options.word !== undefined) {
                  this.wordSpacing = options.word;
              }
              if (options.line !== undefined) {
                  this.lineHeight = options.line;
              }
              this.redrawText();
          }

          /**
           * 设置字体大小
           */
          setFontSize(size) {
              this.fontSize = size;
              this.bitmap.fontSize = size;
              this._clearCharWidthCache(); // 清除字符宽度缓存
              this.redrawText();
          }

          /**
           * 清除字符宽度缓存
           */
          _clearCharWidthCache() {
              this._charWidthCache = {};
          }

          /**
           * 设置文本颜色
           */
          setTextColor(color) {
              console.log('🔧 UILabel: setTextColor调用', color);
              this.textColor = color;
              this.bitmap.textColor = color;
              console.log('🔧 UILabel: bitmap.textColor设置为', this.bitmap.textColor);
              this.redrawText();
          }

          /**
           * 设置描边颜色
           */
          setOutlineColor(color) {
              console.log('🔧 UILabel: setOutlineColor调用', color);
              this.outlineColor = color;
              this.bitmap.outlineColor = color;
              console.log('🔧 UILabel: bitmap.outlineColor设置为', this.bitmap.outlineColor);
              this.redrawText();
          }

          /**
           * 设置描边宽度
           */
          setOutlineWidth(width) {
              this.outlineWidth = width;
              this.bitmap.outlineWidth = width;
              this.redrawText();
          }

          /**
           * 设置文本对齐
           */
          setTextAlign(align) {
              this.textAlign = align;
              this.redrawText();
          }

          /**
           * 设置垂直对齐
           */
          setVerticalAlign(align) {
              this.verticalAlign = align;
              this.redrawText();
          }

          /**
           * 🔧 设置垂直偏移（用于微调文本位置）
           * @param {number} offset 偏移量，负数向上，正数向下
           */
          setVerticalOffset(offset) {
              this.verticalOffset = offset;
              this.redrawText();
          }

          /**
           * 设置尺寸
           */
          setSize(width, height) {
              this.labelWidth = width;
              this.labelHeight = height;
              this.setupTextBitmap();
              this.redrawText();
          }

          /**
           * 设置背景颜色
           */
          setBackgroundColor(color) {
              this.backgroundColor = color;
              this.redrawText();
          }

          // 🔑 ========== 打字效果方法 ==========

          /**
           * 开始打字效果
           * @param {string} text 要显示的文本
           * @param {object} options 选项
           * @param {number} options.speed 打字速度（毫秒/字符）
           * @param {number} options.delay 开始前延迟（毫秒）
           * @param {function} options.onProgress 进度回调
           * @param {function} options.onComplete 完成回调
           */
          startTypewriter(text, options = {}) {
              console.log('🔤 UILabel: 开始打字效果', text);

              // 停止当前的打字效果
              this.stopTypewriter();

              // 设置状态
              this._typewriterState.targetText = text || '';
              this._typewriterState.currentIndex = 0;
              this._typewriterState.displayText = '';
              this._typewriterState.isActive = true;
              this._typewriterState.isPaused = false;
              this._typewriterState.onProgress = options.onProgress || null;
              this._typewriterState.onComplete = options.onComplete || null;

              // 获取速度设置
              const speed = options.speed !== undefined ? options.speed : this.typewriterSpeed;
              const delay = options.delay !== undefined ? options.delay : this.typewriterDelay;

              // 清空当前文本
              this._text = '';
              this.redrawText();

              // 启动光标闪烁
              this._startCursorBlink();

              // 延迟开始打字
              if (delay > 0) {
                  setTimeout(() => {
                      if (this._typewriterState.isActive) {
                          this._startTypewriterTimer(speed);
                      }
                  }, delay);
              } else {
                  this._startTypewriterTimer(speed);
              }
          }

          /**
           * 停止打字效果
           */
          stopTypewriter() {
              if (this._typewriterState.timer) {
                  clearTimeout(this._typewriterState.timer);
                  this._typewriterState.timer = null;
              }

              if (this._typewriterState.cursorTimer) {
                  clearInterval(this._typewriterState.cursorTimer);
                  this._typewriterState.cursorTimer = null;
              }

              this._typewriterState.isActive = false;
              this._typewriterState.isPaused = false;
          }

          /**
           * 暂停打字效果
           */
          pauseTypewriter() {
              if (this._typewriterState.isActive && !this._typewriterState.isPaused) {
                  this._typewriterState.isPaused = true;
                  if (this._typewriterState.timer) {
                      clearTimeout(this._typewriterState.timer);
                      this._typewriterState.timer = null;
                  }
              }
          }

          /**
           * 恢复打字效果
           */
          resumeTypewriter() {
              if (this._typewriterState.isActive && this._typewriterState.isPaused) {
                  this._typewriterState.isPaused = false;
                  this._startTypewriterTimer(this.typewriterSpeed);
              }
          }

          /**
           * 立即完成打字效果
           */
          completeTypewriter() {
              if (this._typewriterState.isActive) {
                  // 停止定时器
                  this.stopTypewriter();

                  // 立即显示全部文本
                  this._text = this._typewriterState.targetText;
                  this.redrawText();

                  // 触发完成回调
                  if (this._typewriterState.onComplete) {
                      this._typewriterState.onComplete();
                  }

                  console.log('🔤 UILabel: 打字效果立即完成');
              }
          }

          /**
           * 检查打字效果是否完成
           */
          isTypewriterComplete() {
              return !this._typewriterState.isActive ||
                     this._typewriterState.currentIndex >= this._typewriterState.targetText.length;
          }

          /**
           * 获取打字进度（0-100）
           */
          getTypewriterProgress() {
              if (!this._typewriterState.targetText) return 100;
              return Math.round((this._typewriterState.currentIndex / this._typewriterState.targetText.length) * 100);
          }

          // 🔑 ========== 打字效果内部方法 ==========

          /**
           * 启动打字定时器
           */
          _startTypewriterTimer(speed) {
              if (!this._typewriterState.isActive || this._typewriterState.isPaused) {
                  return;
              }

              this._typewriterState.timer = setTimeout(() => {
                  this._typewriterTick(speed);
              }, speed);
          }

          /**
           * 打字效果核心逻辑
           */
          _typewriterTick(speed) {
              if (!this._typewriterState.isActive || this._typewriterState.isPaused) {
                  return;
              }

              const targetText = this._typewriterState.targetText;
              const currentIndex = this._typewriterState.currentIndex;

              // 检查是否完成
              if (currentIndex >= targetText.length) {
                  this._completeTypewriter();
                  return;
              }

              // 添加下一个字符
              this._typewriterState.currentIndex++;
              this._updateTypewriterDisplay();

              // 触发进度回调
              if (this._typewriterState.onProgress) {
                  const progress = this.getTypewriterProgress();
                  const currentText = targetText.substring(0, this._typewriterState.currentIndex);
                  this._typewriterState.onProgress(progress, currentText);
              }

              // 继续下一个字符
              this._startTypewriterTimer(speed);
          }

          /**
           * 更新打字效果显示
           */
          _updateTypewriterDisplay() {
              const currentText = this._typewriterState.targetText.substring(0, this._typewriterState.currentIndex);

              // 添加光标
              if (this.typewriterCursor && this._typewriterState.showCursor && this._typewriterState.isActive) {
                  this._text = currentText + this.typewriterCursorChar;
              } else {
                  this._text = currentText;
              }

              // 重新绘制文本（利用已有的换行功能）
              this.redrawText();
          }

          /**
           * 启动光标闪烁
           */
          _startCursorBlink() {
              if (!this.typewriterCursor) return;

              // 清除现有的光标定时器
              if (this._typewriterState.cursorTimer) {
                  clearInterval(this._typewriterState.cursorTimer);
              }

              this._typewriterState.showCursor = true;
              this._typewriterState.cursorTimer = setInterval(() => {
                  if (this._typewriterState.isActive) {
                      this._typewriterState.showCursor = !this._typewriterState.showCursor;
                      this._updateTypewriterDisplay();
                  }
              }, this.typewriterCursorBlink);
          }

          /**
           * 完成打字效果（内部方法）
           */
          _completeTypewriter() {
              // 停止定时器
              this.stopTypewriter();

              // 显示完整文本（不带光标）
              this._text = this._typewriterState.targetText;
              this.redrawText();

              // 触发完成回调
              if (this._typewriterState.onComplete) {
                  this._typewriterState.onComplete();
              }

              console.log('🔤 UILabel: 打字效果自然完成');
          }

          /**
           * 获取所有属性（用于模型同步）
           */
          getProperties() {
              return {
                  name:this.name,
                  // 基础属性
                  x: this.x,
                  y: this.y,
                  width: this.labelWidth,
                  height: this.labelHeight,

                  // 文本属性
                  text: this._text,
                  fontSize: this.fontSize,
                  fontFace: this.fontFace,
                  fontBold: this.fontBold,
                  fontItalic: this.fontItalic,

                  // 颜色属性
                  textColor: this.textColor,
                  outlineColor: this.outlineColor,
                  outlineWidth: this.outlineWidth,

                  // 对齐属性
                  textAlign: this.textAlign,
                  verticalAlign: this.verticalAlign,

                  // 背景属性
                  backgroundColor: this.backgroundColor,
                  backgroundOpacity: this.backgroundOpacity
              };
          }

          /**
           * 克隆当前 UILabel 对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UILabel} 克隆的 UILabel 对象
           */
          clone(options = {}) {
              console.log('🔄 UILabel: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = {
                  // 基础属性
                  width: this.labelWidth,
                  height: this.labelHeight,
                  visible: this.visible,

                  // 🔑 UIComponent 属性（安全访问）
                  name: this.name || '',
                  enabled: this.enabled !== false,
                  dataBinding: this.dataBinding || '',

                  // 🔑 深度克隆组件脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : [],

                  // UILabel 特有属性
                  text: this._text,
                  prefix: this.prefix,
                  suffix: this.suffix,
                  fontSize: this.fontSize,
                  fontFace: this.fontFace,
                  fontBold: this.fontBold,
                  fontItalic: this.fontItalic,
                  textColor: this.textColor,
                  outlineColor: this.outlineColor,
                  outlineWidth: this.outlineWidth,
                  textAlign: this.textAlign,
                  verticalAlign: this.verticalAlign,
                  backgroundColor: this.backgroundColor,
                  backgroundOpacity: this.backgroundOpacity,

                  // 🔑 间距属性
                  letterSpacing: this.letterSpacing,
                  wordSpacing: this.wordSpacing,
                  lineHeight: this.lineHeight,
                  spacingMode: this.spacingMode
              };

              // 2. 创建克隆对象
              const clonedLabel = new UILabel(cloneProperties);

              // 🔧 调试：验证脚本是否被克隆
              console.log('🔄 UILabel: 克隆验证', {
                  原始组件名称: this.name,
                  克隆组件名称: clonedLabel.name,
                  原始脚本数量: this.componentScripts ? this.componentScripts.length : 0,
                  克隆脚本数量: clonedLabel.componentScripts ? clonedLabel.componentScripts.length : 0,
                  原始脚本详情: this.componentScripts ? this.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : [],
                  克隆脚本详情: clonedLabel.componentScripts ? clonedLabel.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : []
              });

              // 3. 设置位置和变换属性
              clonedLabel.x = this.x + (offsetPosition ? offsetX : 0);
              clonedLabel.y = this.y + (offsetPosition ? offsetY : 0);
              clonedLabel.scale.x = this.scale.x;
              clonedLabel.scale.y = this.scale.y;
              clonedLabel.rotation = this.rotation;
              clonedLabel.alpha = this.alpha;
              clonedLabel.anchor.x = this.anchor.x;
              clonedLabel.anchor.y = this.anchor.y;
              clonedLabel.pivot.x = this.pivot.x;
              clonedLabel.pivot.y = this.pivot.y;
              clonedLabel.skew.x = this.skew.x;
              clonedLabel.skew.y = this.skew.y;
              clonedLabel.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedLabel.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              console.log('✅ UILabel: 克隆完成，包含', clonedChildren.length, '个子对象');
              return clonedLabel;
          }
          /**
           * 🔑 获取克隆属性
           */
          getCloneProperties() {
              return {
                  // 基础属性
                  width: this.labelWidth,
                  height: this.labelHeight,

                  // 🔑 UIComponent 属性
                  name: this.name || '',
                  enabled: this.enabled !== false,
                  dataBinding: this.dataBinding || '',
                  componentScript: this.componentScript ? {
                      lifecycle: { ...(this.componentScript.lifecycle || {}) },
                      dataEvents: { ...(this.componentScript.dataEvents || {}) },
                      interactionEvents: { ...(this.componentScript.interactionEvents || {}) },
                      customFunctions: { ...(this.componentScript.customFunctions || {}) },
                      variables: { ...(this.componentScript.variables || {}) }
                  } : undefined,

                  // 文本属性
                  text: this._text,
                  prefix: this.prefix,
                  suffix: this.suffix,
                  fontSize: this.fontSize,
                  fontFace: this.fontFace,
                  fontBold: this.fontBold,
                  fontItalic: this.fontItalic,

                  // 颜色和样式
                  textColor: this.textColor,
                  outlineColor: this.outlineColor,
                  outlineWidth: this.outlineWidth,

                  // 对齐
                  textAlign: this.textAlign,
                  verticalAlign: this.verticalAlign,

                  // 背景
                  backgroundColor: this.backgroundColor,
                  backgroundOpacity: this.backgroundOpacity,

                  // 间距
                  letterSpacing: this.letterSpacing || 0,
                  wordSpacing: this.wordSpacing || 0,
                  lineHeight: this.lineHeight || 1.2,
                  spacingMode: this.spacingMode || 'auto'
              };
          }

          /**
           * 每帧更新 - 使用UIComponent的update方法
           */
          update() {
              // 🔑 调用UIComponent的update方法，它会处理脚本执行
              // 不需要调用super.update()，因为UIComponent的update方法已经包含了所有逻辑
              if (this._updateEnabled && this._isStarted && this.executeScript) {
                  const currentTime = Date.now();
                  const deltaTime = this._lastUpdateTime ? currentTime - this._lastUpdateTime : 16;
                  this._lastUpdateTime = currentTime;

                  // 执行 onUpdate 脚本
                  this.executeScript('onUpdate', { deltaTime, currentTime });
              }
          }

          /**
           * 销毁文本组件
           */
          destroy() {
              // 🔑 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true; // 防止重复销毁
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UILabel: 销毁脚本执行失败', error);
              }

              // 清理缓存
              this._clearCharWidthCache();

              if (this.bitmap) {
                  this.bitmap.destroy();
                  this.bitmap = null;
              }
                  try {
                 if (super.destroy){
                super.destroy();
                 } } catch (error) {
                    console.warn('⚠️ UILabel: 销毁脚本执行失败', error);
                }

          }
      }
      // 将类添加到全局
      window.UILabel = UILabel;

      console.log('UI Base Components Plugin loaded - UIImage and UILabel classes available');



  // ===== uiMask.js =====
  /**
   * UIMask 插件 - 遮罩组件
   *
   * 功能：
   * - 创建各种类型的遮罩（矩形、圆形、图片）
   * - 容器级遮罩，所有子对象自动应用遮罩效果
   * - Graphics大小始终等于Container大小
   * - 支持动态调整遮罩区域
   * - 完整的生命周期管理
   *
   * 使用方法：
   * const mask = new UIMask({
   *     maskType: 'rectangle',
   *     width: 200,
   *     height: 150
   * });
   *
   * <AUTHOR> Assistant
   * @version 3.0 - 容器级遮罩方案
   */

  (function() {
  console.log('🎭 开始加载 UIMask 插件 v3.0');

      /**
       * UIMask - 遮罩容器组件
       * 继承自 PIXI.Container，内部创建Graphics作为遮罩
       */
      class UIMask extends PIXI.Container {
          constructor(properties = {}) {
              super();

              console.log('🎭 UIMask: 创建遮罩容器', properties);

              // 标识为 UI 组件
              this.isUIComponent = true;
              this.uiComponentType = 'UIMask';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 使用UIScriptManager添加脚本功能
              if (window.UIScriptManager) {
                  window.UIScriptManager.applyToObject(this, properties);
                  console.log('🔧 UIMask: 脚本系统初始化完成', {
                      hasComponentScripts: !!this.componentScripts,
                      scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                      scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                  });
              }

              // 遮罩属性
              this.maskType = properties.maskType || 'rectangle'; // 'rectangle', 'circle', 'image'
              this.maskImage = properties.maskImage || null;

              // 🔑 容器尺寸（这就是遮罩的有效区域）
              this.maskWidth = properties.width || 200;
              this.maskHeight = properties.height || 200;

              // 设置基础属性
              this.x = properties.x || 0;
              this.y = properties.y || 0;
              this.name = properties.name || 'UIMask';

              // 🔑 创建内部Graphics对象作为遮罩
              this.maskGraphics = new PIXI.Graphics();

              // 🔑 设置容器级遮罩
              this.mask = this.maskGraphics;

              // 🔑 绘制遮罩图形
              this.drawMask();

              // 🔑 标记为已创建，onStart会在添加到父容器时执行
              this._isCreated = true;

              console.log('✅ UIMask: 遮罩容器创建完成', {
                  type: this.maskType,
                  size: `${this.maskWidth}x${this.maskHeight}`,
                  position: `(${this.x}, ${this.y})`
              });
          }

          /**
           * 🔑 添加width getter/setter来兼容外部代码
           */
          get width() {
              return this.maskWidth;
          }

          set width(value) {
              this.maskWidth = value;
              this.drawMask(); // 重新绘制遮罩
          }

          /**
           * 🔑 添加height getter/setter来兼容外部代码
           */
          get height() {
              return this.maskHeight;
          }

          set height(value) {
              this.maskHeight = value;
              this.drawMask(); // 重新绘制遮罩
          }

          /**
           * 🔑 绘制遮罩图形（Graphics大小始终等于Container）
           */
          drawMask() {
              this.maskGraphics.clear();

              switch (this.maskType) {
                  case 'rectangle':
                      this.drawRectangleMask();
                      break;
                  case 'circle':
                      this.drawCircleMask();
                      break;
                  case 'image':
                      this.drawImageMask();
                      break;
                  default:
                      console.warn('UIMask: 未知的遮罩类型', this.maskType);
                      this.drawRectangleMask();
              }
          }

          /**
           * 绘制矩形遮罩
           */
          drawRectangleMask() {
              console.log('🎨 UIMask: 绘制矩形遮罩', `${this.maskWidth}x${this.maskHeight}`);

              this.maskGraphics.beginFill(0xffffff, 1);
              this.maskGraphics.drawRect(0, 0, this.maskWidth, this.maskHeight);
              this.maskGraphics.endFill();

              console.log('✅ UIMask: 矩形遮罩绘制完成');
          }

          /**
           * 绘制圆形遮罩
           */
          drawCircleMask() {
              const radius = Math.min(this.maskWidth, this.maskHeight) / 2;
              const centerX = this.maskWidth / 2;
              const centerY = this.maskHeight / 2;

              console.log('🎨 UIMask: 绘制圆形遮罩', `半径: ${radius}`);

              this.maskGraphics.beginFill(0xffffff, 1);
              this.maskGraphics.drawCircle(centerX, centerY, radius);
              this.maskGraphics.endFill();

              console.log('✅ UIMask: 圆形遮罩绘制完成');
          }

          /**
           * 绘制图片遮罩
           */
          drawImageMask() {
              if (!this.maskImage) {
                  console.warn('UIMask: 图片遮罩需要指定图片路径，使用矩形遮罩代替');
                  this.drawRectangleMask();
                  return;
              }

              // 对于图片遮罩，我们先绘制一个矩形作为占位
              // 实际的图片遮罩需要加载图片后再处理
              this.maskGraphics.beginFill(0xffffff, 1);
              this.maskGraphics.drawRect(0, 0, this.maskWidth, this.maskHeight);
              this.maskGraphics.endFill();

              console.log('🎭 UIMask: 图片遮罩占位绘制完成', `${this.maskWidth}x${this.maskHeight}`);
              // TODO: 实现真正的图片遮罩加载
          }

          /**
           * 🔑 重写 addChild 方法 - 容器级遮罩，不需要特殊处理
           */
          addChild(child) {
              console.log('🎭 UIMask: 添加子对象，遮罩自动应用', {
                  childType: child.constructor.name,
                  childName: child.name,
                  maskSize: `${this.maskWidth}x${this.maskHeight}`
              });

              const result = super.addChild(child);

              console.log('✅ UIMask: 子对象添加完成，遮罩已自动生效');
              return result;
          }

          /**
           * 🔑 更新遮罩尺寸
           */
          updateSize(width, height) {
              if (width !== undefined) this.maskWidth = width;
              if (height !== undefined) this.maskHeight = height;

              console.log('🔧 UIMask: 更新遮罩尺寸', {
                  width: this.maskWidth,
                  height: this.maskHeight
              });

              // 重新绘制遮罩
              this.drawMask();
          }

          /**
           * 更新遮罩类型
           */
          updateMaskType(type) {
              this.maskType = type;
              console.log('🔧 UIMask: 更新遮罩类型', this.maskType);

              // 重新绘制遮罩
              this.drawMask();
          }

          /**
           * 获取遮罩信息
           */
          getMaskInfo() {
              return {
                  type: this.maskType,
                  size: { width: this.maskWidth, height: this.maskHeight },
                  maskImage: this.maskImage,
                  childrenCount: this.children.length,
                  hasChildren: this.children.length > 0
              };
          }

          /**
           * 🔑 更新方法 - 支持脚本执行
           */
          update() {
              // 调用父类更新方法（如果存在）
              if (super.update) {
                  super.update();
              }

              // 🔑 执行更新脚本
              if (this.executeScript) {
                  this.executeScript('onUpdate');
              }
          }

          /**
           * 🔑 克隆UIMask对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UIMask} 克隆的 UIMask 对象
           */
          clone(options = {}) {
              console.log('🔄 UIMask: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 创建新的UIMask对象
              const clonedMask = new UIMask({
                  // 基础属性
                  width: this.maskWidth,
                  height: this.maskHeight,
                  visible: this.visible,

                  // 🔑 UIComponent 属性（安全访问）
                  name: this.name || '',
                  enabled: this.enabled !== false,

                  // 🔑 深度克隆组件脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : [],

                  // UIMask 特有属性
                  maskType: this.maskType,
                  maskImage: this.maskImage
              });

              // 2. 设置位置和变换属性
              clonedMask.x = this.x + (offsetPosition ? offsetX : 0);
              clonedMask.y = this.y + (offsetPosition ? offsetY : 0);
              clonedMask.scale.x = this.scale.x;
              clonedMask.scale.y = this.scale.y;
              clonedMask.rotation = this.rotation;
              clonedMask.alpha = this.alpha;
              clonedMask.anchor.x = this.anchor.x;
              clonedMask.anchor.y = this.anchor.y;
              clonedMask.pivot.x = this.pivot.x;
              clonedMask.pivot.y = this.pivot.y;
              clonedMask.skew.x = this.skew.x;
              clonedMask.skew.y = this.skew.y;
              clonedMask.zIndex = this.zIndex;

              // 3. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedMask.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              console.log('✅ UIMask: 克隆完成', {
                  originalChildren: this.children.length,
                  clonedChildren: clonedChildren.length,
                  position: `(${clonedMask.x}, ${clonedMask.y})`
              });

              return clonedMask;
          }

          /**
           * 🔑 销毁时清理Graphics和遮罩
           */
          destroy(options) {
              console.log('🗑️ UIMask: 开始销毁遮罩容器');

              // 🔑 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true; // 防止重复销毁
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UIMask: 销毁脚本执行失败', error);
              }

              try {
                  // 清理遮罩Graphics
                  if (this.maskGraphics) {
                      this.maskGraphics.destroy();
                      this.maskGraphics = null;
                      console.log('✅ UIMask: maskGraphics已销毁');
                  }

                  // 清理遮罩引用
                  this.mask = null;

                  // 调用父类销毁方法
                  super.destroy(options);

                  console.log('✅ UIMask: 遮罩容器销毁完成');
              } catch (error) {
                  console.error('❌ UIMask: 销毁过程中出错', error);
              }
          }
      }

      // 导出到全局
      window.UIMask = UIMask;
      console.log('✅ UIMask 插件 v3.0 加载完成');

  })();

  // ===== uiLayout.js =====
  /**
   * UILayout - 通用布局管理器
   * 支持垂直、水平、网格等多种布局方式
   * 继承自PIXI.Container，专注于子元素的位置管理
   */

  (() => {
  // 确保 PIXI 可用
      if (typeof PIXI === 'undefined') {
          console.error('UILayout: PIXI 未找到');
          return;
      }

      /**
       * UILayout - 通用布局容器
       */
      class UILayout extends PIXI.Container {
          constructor(properties = {}) {
              super();

              // 标识为 UI 组件
              this.isUIComponent = true;
              this.uiComponentType = 'UILayout';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 使用UIScriptManager添加脚本功能
              if (window.UIScriptManager) {
                  window.UIScriptManager.applyToObject(this, properties);
                  console.log('🔧 UILayout: 脚本系统初始化完成', {
                      hasComponentScripts: !!this.componentScripts,
                      scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                      scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                  });
              }





              // 布局类型：'vertical', 'horizontal', 'grid'
              this.layoutType = properties.layoutType || 'vertical';

              // 基础布局参数
              this.spacing = properties.spacing || 5;              // 项目间距
              this.padding = properties.padding || 0;              // 内边距
              this.horizontalSpacing = properties.horizontalSpacing || this.spacing; // 水平间距
              this.verticalSpacing = properties.verticalSpacing || this.spacing;     // 垂直间距

              // 网格布局参数
              this.columns = properties.columns || 2;              // 网格列数
              this.rows = properties.rows || 0;                    // 网格行数（0表示自动）

              // 对齐方式
              this.mainAxisAlignment = properties.mainAxisAlignment || 'start';     // 主轴对齐：start, center, end, space-between, space-around
              this.crossAxisAlignment = properties.crossAxisAlignment || 'start';   // 交叉轴对齐：start, center, end, stretch

              // 🔑 容器尺寸直接使用Layout对象自身的宽高，确保不为负数
              this.containerWidth = Math.max(0, this.width || 0);
              this.containerHeight = Math.max(0, this.height || 0);

              // 自动更新布局
              this.autoUpdate = properties.autoUpdate !== false;

              // 布局状态
              this._needsLayout = true;
              this._isUpdatingLayout = false;

              // 🔑 模板绑定属性
              this.templateData = properties.templateData || [];      // 模板数据数组
              this.generatedItems = [];                               // 生成的列表项
              this.isTemplateMode = properties.isTemplateMode || false; // 是否启用模板模式
              this._cachedTemplate = null;                            // 缓存的模板对象

              // 🔑 滚动相关属性
              this.scrollEnabled = properties.scrollEnabled !== false; // 是否启用滚动
              this.scrollTop = 0;                                     // 当前滚动位置
              this.maxScrollTop = 0;                                  // 最大滚动位置
              this.scrollSpeed = properties.scrollSpeed || 40;        // 滚动速度
              this._contentContainer = null;                          // 内容容器


              // 🔑 初始化滚动功能
              this.initializeScrolling();

              // 标记为已创建并执行onStart
              this._isCreated = true;
              if (this.executeScript) {
                  this.executeScript('onStart');
              }
          }


          /**
           * 添加子元素并更新布局
           */
          addChild(child) {
              const result = super.addChild(child);
            
              if (this.autoUpdate) {
                  this.requestLayoutUpdate();
              }
              return result;
          }

          /**
           * 移除子元素并更新布局
           */
          removeChild(child) {
              try {
                  // 🔧 安全检查：确保子对象存在且有效
                  if (!child) {
                      console.warn('🚨 UILayout: 尝试移除null/undefined子对象');
                      return null;
                  }

                  // 🔧 检查子对象是否真的是这个容器的子对象
                  if (child.parent !== this) {
                      console.warn('🚨 UILayout: 子对象的父容器不是当前Layout', {
                          childParent: child.parent,
                          currentLayout: this
                      });
                      return null;
                  }

                  // 🔧 安全移除
                  const result = super.removeChild(child);

                  // 🔧 确保子对象的transform等属性仍然有效
                  if (child.transform) {
                      console.log('✅ UILayout: 子对象安全移除', child.constructor.name);
                  } else {
                      console.warn('🚨 UILayout: 子对象移除后transform为null', child.constructor.name);
                  }

                  if (this.autoUpdate) {
                      this.requestLayoutUpdate();
                  }
                  return result;
              } catch (error) {
                  console.error('🚨 UILayout: 移除子对象时发生错误', error, child);
                  return null;
              }
          }

          /**
           * 请求布局更新（异步）
           */
          requestLayoutUpdate() {
              this._needsLayout = true;
          }

          /**
           * 立即更新布局
           */
          updateLayout() {
              if (this._isUpdatingLayout) return;
              this._isUpdatingLayout = true;
              this._needsLayout = false;

              // 🔑 在布局更新前同步容器尺寸
              this.syncContainerSize();

              try {
                  switch (this.layoutType) {
                      case 'vertical':
                          this.updateVerticalLayout();
                          break;
                      case 'horizontal':
                          this.updateHorizontalLayout();
                          break;
                      case 'grid':
                          this.updateGridLayout();
                          break;
                      default:
                          console.warn('UILayout: 未知的布局类型', this.layoutType);
                          this.updateVerticalLayout(); // 默认使用垂直布局
                  }

                  // 🔑 布局更新完成后，更新滚动范围
                  this.updateScrollRange();

              } catch (error) {
                  console.error('UILayout: 布局更新失败', error);
              } finally {
                  this._isUpdatingLayout = false;
              }
          }

          /**
           * 垂直布局
           */
          updateVerticalLayout() {
              let currentY = this.padding;
              const containerCenterX = this.containerWidth / 2;
              this.children.forEach((child, index) => {
                  if (!child.visible) return;
                  // 设置Y位置
                  child.y = currentY;

                  // 设置X位置（根据对齐方式）
                  switch (this.crossAxisAlignment) {
                      case 'center':
                          child.x = containerCenterX - (child.width || 0) / 2;
                          break;
                      case 'end':
                          child.x = this.containerWidth - (child.width || 0) - this.padding;
                          break;
                      case 'start':
                      default:
                          child.x = this.padding;
                          break;
                  }

                  // 更新下一个元素的Y位置
                  const childHeight = child.height || 0;
                  currentY += childHeight + this.verticalSpacing;


              });

              // 🔑 计算内容的实际高度，但不修改容器高度
              this._contentHeight = currentY - this.verticalSpacing + this.padding;

              // 🔑 只有在容器高度为0且Layout对象高度也为0时，才自动设置高度
              if (this.containerHeight === 0 && (this.height === 0 || this.height === undefined)) {
                  this.height = this._contentHeight;
                  this.containerHeight = this._contentHeight;
                  console.log('🔄 UILayout: 自动设置容器高度', this.containerHeight);
              }
          }

          /**
           * 水平布局
           */
          updateHorizontalLayout() {
              let currentX = this.padding;
              const containerCenterY = this.containerHeight / 2;

              this.children.forEach((child, index) => {
                  if (!child.visible) return;

                  // 设置X位置
                  child.x = currentX;

                  // 设置Y位置（根据对齐方式）
                  switch (this.crossAxisAlignment) {
                      case 'center':
                          child.y = containerCenterY - (child.height || 0) / 2;
                          break;
                      case 'end':
                          child.y = this.containerHeight - (child.height || 0) - this.padding;
                          break;
                      case 'start':
                      default:
                          child.y = this.padding;
                          break;
                  }

                  // 更新下一个元素的X位置
                  currentX += (child.width || 0) + this.horizontalSpacing;
              });

              // 🔑 计算内容的实际宽度，但不修改容器宽度
              this._contentWidth = currentX - this.horizontalSpacing + this.padding;

              // 🔑 只有在容器宽度为0且Layout对象宽度也为0时，才自动设置宽度
              if (this.containerWidth === 0 && (this.width === 0 || this.width === undefined)) {
                  this.width = this._contentWidth;
                  this.containerWidth = this._contentWidth;
                  console.log('🔄 UILayout: 自动设置容器宽度', this.containerWidth);
              }
          }

          /**
           * 网格布局
           */
          updateGridLayout() {
              const visibleChildren = this.children.filter(child => child.visible);
            
              visibleChildren.forEach((child, index) => {
                  const row = Math.floor(index / this.columns);
                  const col = index % this.columns;

                  // 计算位置
                  child.x = this.padding + col * ((child.width || 0) + this.horizontalSpacing);
                  child.y = this.padding + row * ((child.height || 0) + this.verticalSpacing);
              });

              // 自动计算容器尺寸
              if (visibleChildren.length > 0) {
                  const totalRows = Math.ceil(visibleChildren.length / this.columns);
                  const maxChildWidth = Math.max(...visibleChildren.map(child => child.width || 0));
                  const maxChildHeight = Math.max(...visibleChildren.map(child => child.height || 0));

                  if (this.containerWidth === 0) {
                      this.containerWidth = this.padding * 2 + 
                          this.columns * maxChildWidth + 
                          (this.columns - 1) * this.horizontalSpacing;
                  }

                  if (this.containerHeight === 0) {
                      this.containerHeight = this.padding * 2 + 
                          totalRows * maxChildHeight + 
                          (totalRows - 1) * this.verticalSpacing;
                  }
              }
          }

          /**
           * 设置布局类型
           */
          setLayoutType(layoutType) {
              if (this.layoutType !== layoutType) {

                  this.layoutType = layoutType;
                  this.requestLayoutUpdate();
              }
          }

          /**
           * 设置间距
           */
          setSpacing(spacing) {
              this.spacing = spacing;
              this.horizontalSpacing = spacing;
              this.verticalSpacing = spacing;
              this.requestLayoutUpdate();
          }

          /**
           * 设置容器尺寸
           */
          setContainerSize(width, height) {
              // 🔑 同时更新Layout对象自身的尺寸和容器尺寸，确保不为负数
              if (width !== undefined) {
                  this.width = Math.max(0, width);
                  this.containerWidth = Math.max(0, width);
              }
              if (height !== undefined) {
                  this.height = Math.max(0, height);
                  this.containerHeight = Math.max(0, height);
              }
              this.requestLayoutUpdate();
          }

          /**
           * 获取计算后的容器尺寸
           */
          getCalculatedSize() {
              return {
                  width: this.containerWidth,
                  height: this.containerHeight
              };
          }

          /**
           * 🔑 同步容器尺寸（当Layout对象尺寸改变时调用）
           */
          syncContainerSize() {
              const oldWidth = this.containerWidth;
              const oldHeight = this.containerHeight;

              // 🔧 确保尺寸不为负数
              this.containerWidth = Math.max(0, this.width || 0);
              this.containerHeight = Math.max(0, this.height || 0);

              // 如果尺寸发生变化，请求布局更新
              if (oldWidth !== this.containerWidth || oldHeight !== this.containerHeight) {
                  console.log('🔄 UILayout: 容器尺寸同步', {
                      oldSize: `${oldWidth}x${oldHeight}`,
                      newSize: `${this.containerWidth}x${this.containerHeight}`,
                      originalSize: `${this.width}x${this.height}`
                  });
                  this.requestLayoutUpdate();
              }
          }











          /**
           * 🔑 获取或缓存模板对象（自动使用第一个子元素）
           */
          _getTemplate() {
              // 如果已经有缓存的模板，直接返回
              if (this._cachedTemplate) {
                  return this._cachedTemplate;
              }

              // 如果有子元素，使用第一个作为模板
              if (this.children && this.children.length > 0) {
                  const firstChild = this.children[0];

                  // 克隆第一个子元素作为模板
                  if (typeof firstChild.clone === 'function') {
                      this._cachedTemplate = firstChild.clone({ offsetPosition: false });
                      this._cachedTemplate._isTemplate = true;

                      console.log('🔗 UILayout: 缓存第一个子元素作为模板', {
                          templateType: this._cachedTemplate.uiComponentType,
                          templateId: this._cachedTemplate.componentId,
                          originalChildType: firstChild.uiComponentType
                      });

                      return this._cachedTemplate;
                  } else {
                      console.warn('⚠️ UILayout: 第一个子元素没有 clone 方法');
                  }
              }
              console.warn('⚠️ UILayout: 没有可用的子元素作为模板');
              return null;
          }

          /**
           * 🔑 初始化滚动功能
           */
          initializeScrolling() {
              if (!this.scrollEnabled) return;

              console.log('🖱️ UILayout: 初始化滚动功能');

              // 🔑 不创建额外容器，直接使用 UILayout 自身作为滚动容器
              this._contentContainer = this;
              this._originalY = this.y; // 保存原始Y位置

              // 设置滚动事件监听
              this.setupScrolling();

              console.log('✅ UILayout: 滚动功能初始化完成');
          }

          /**
           * 🔑 设置滚轮滚动（参考 UIList）
           */
          setupScrolling() {
              console.log('🖱️ UILayout: 设置滚轮滚动');

              // 使用 RPG Maker MZ 的循环事件系统
              this.setupRPGMakerEventLoop();

              console.log('✅ UILayout: 循环事件设置完成');
          }

          /**
           * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIList）
           */
          setupRPGMakerEventLoop() {
              // 注册到 Graphics.app.ticker（PIXI 渲染循环）
              if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  this._tickerCallback = () => this.processWheelScroll();
                  Graphics.app.ticker.add(this._tickerCallback);
                  console.log('✅ UILayout: 已注册到 PIXI ticker 循环');
                  return;
              }

              console.warn('⚠️ UILayout: 无法注册到 RPG Maker MZ 循环');
          }

          /**
           * 🔑 处理滚轮滚动（参考 UIList）
           */
          processWheelScroll() {
              if (!this.scrollEnabled || !this._contentContainer) return;
              if (typeof TouchInput === 'undefined') return;

              // 检查是否在 UILayout 区域内
              if (!this.isTouchedInsideFrame()) return;

              // 使用 RPG Maker MZ 的滚轮检测
              const threshold = 20;
              if (TouchInput.wheelY >= threshold) {
                  // 向下滚动
                  this.scrollDown();
              }
              if (TouchInput.wheelY <= -threshold) {
                  // 向上滚动
                  this.scrollUp();
              }
          }

          /**
           * 🔑 检查鼠标是否在 UILayout 区域内
           */
          isTouchedInsideFrame() {
              if (typeof TouchInput === 'undefined') return false;

              // 获取鼠标在世界坐标中的位置
              const globalPos = { x: TouchInput.x, y: TouchInput.y };

              // 转换为本地坐标
              const localPos = this.toLocal(globalPos);

              return localPos.x >= 0 && localPos.x <= this.containerWidth &&
                     localPos.y >= 0 && localPos.y <= this.containerHeight;
          }

          /**
           * 🔑 向下滚动
           */
          scrollDown() {
              this.scrollTop = Math.min(this.scrollTop + this.scrollSpeed, this.maxScrollTop);
              this.updateScrollPosition();
              console.log('🖱️ UILayout: 向下滚动', { scrollTop: this.scrollTop, maxScrollTop: this.maxScrollTop });
          }

          /**
           * 🔑 向上滚动
           */
          scrollUp() {
              this.scrollTop = Math.max(this.scrollTop - this.scrollSpeed, 0);
              this.updateScrollPosition();
              console.log('🖱️ UILayout: 向上滚动', { scrollTop: this.scrollTop, maxScrollTop: this.maxScrollTop });
          }

          /**
           * 🔑 更新滚动位置
           */
          updateScrollPosition() {
              if (!this.scrollEnabled) return;

              // 🔑 直接移动所有子对象，而不是移动容器
              if (this.generatedItems && this.generatedItems.length > 0) {
                  this.generatedItems.forEach(item => {
                      if (item._originalY === undefined) {
                          item._originalY = item.y; // 保存原始位置
                      }
                      item.y = item._originalY - this.scrollTop;
                  });
              }

              console.log('🖱️ UILayout: 滚动位置更新', {
                  scrollTop: this.scrollTop,
                  maxScrollTop: this.maxScrollTop,
                  itemsCount: this.generatedItems ? this.generatedItems.length : 0
              });
          }

          /**
           * 🔑 更新滚动范围
           */
          updateScrollRange() {
              if (!this.scrollEnabled || !this._contentContainer) return;

              // 🔑 使用布局计算时已经得到的内容高度
              let contentHeight = this._contentHeight || 0;

              // 如果没有预计算的内容高度，则重新计算
              if (contentHeight === 0) {
                  if (this.generatedItems.length > 0) {
                      // 使用生成的项目计算高度
                      this.generatedItems.forEach(item => {
                          const itemBottom = item.y + item.height;
                          contentHeight = Math.max(contentHeight, itemBottom);
                      });
                  } else if (this.children && this.children.length > 0) {
                      // 使用所有子对象计算高度
                      this.children.forEach(child => {
                          if (child !== this._contentContainer) {
                              const childBottom = child.y + (child.height || 0);
                              contentHeight = Math.max(contentHeight, childBottom);
                          }
                      });
                  }
              }

              // 🔑 计算最大滚动距离：内容高度 - 容器显示高度
              this.maxScrollTop = Math.max(0, contentHeight - this.containerHeight);

              console.log('📏 UILayout: 滚动范围更新', {
                  contentHeight: contentHeight,
                  containerHeight: this.containerHeight,
                  maxScrollTop: this.maxScrollTop,
                  scrollEnabled: this.maxScrollTop > 0
              });
          }

          /**
           * 🔑 滚动到指定位置
           */
          scrollTo(scrollTop) {
              this.scrollTop = Math.max(0, Math.min(scrollTop, this.maxScrollTop));
              this.updateScrollPosition();
          }

          /**
           * 🔑 设置模板数据并渲染
           */
          setTemplateData(dataArray) {
              console.log('🔄 UILayout: 设置模板数据', {
                  dataCount: dataArray ? dataArray.length : 0,
                  currentChildrenCount: this.children ? this.children.length : 0,
                  isTemplateMode: this.isTemplateMode
              });

              this.templateData = dataArray || [];

              // 🔑 自动启用模板模式（如果有数据的话）
              if (this.templateData.length > 0) {
                  this.isTemplateMode = true;
                  this.renderFromTemplate();
              }
          }

          /**
           * 🔑 从模板渲染列表项
           */
          renderFromTemplate() {
              if (!this.isTemplateMode) {
                  console.warn('⚠️ UILayout: 未启用模板模式');
                  return;
              }

              console.log('🔄 UILayout: 开始从模板渲染列表项', {
                  dataCount: this.templateData.length,
                  currentChildrenCount: this.children ? this.children.length : 0
              });

              // 🔑 第一步：保存第一个元素作为模板（如果还没有缓存）
              if (!this._cachedTemplate) {
                  this._getTemplate(); // 这会缓存第一个子元素
              }

              const template = this._cachedTemplate;
              if (!template) {
                  console.warn('⚠️ UILayout: 无法获取模板对象');
                  return;
              }

              console.log('🔄 UILayout: 使用模板渲染', {
                  templateType: template.uiComponentType,
                  templateId: template.componentId
              });

              // 🔑 第二步：清空所有子元素（包括原始的第一个元素）
              this.clearAllChildren();

              // 🔑 第三步：为每个数据项克隆模板并渲染
              this.templateData.forEach((itemData, index) => {
                  try {
                      const clonedItem = template.clone({ offsetPosition: false });

                      // 🔑 使用 updateFieldsToChildren 方法初始化数据（级联到所有子对象）
                      if (typeof clonedItem.updateFieldsToChildren === 'function') {
                          const context = {
                              index: index,
                              isTemplate: true,
                              templateMode: true,
                              parentLayout: this,
                              data: itemData  // 🔑 将数据放在 context 中
                          };
                          clonedItem.updateFieldsToChildren(context);
                      } else if (typeof clonedItem.onFieldUpdate === 'function') {
                          // 备用方案：如果没有级联方法，使用单层方法
                          const context = {
                              index: index,
                              isTemplate: true,
                              templateMode: true,
                              parentLayout: this,
                              data: itemData
                          };
                          clonedItem.onFieldUpdate(context);
                      }

                      // 显示克隆的项目
                      clonedItem.visible = true;
                      clonedItem._isTemplate = false;

                      // 🔑 直接添加到布局中
                      this.addChild(clonedItem);
                      this.generatedItems.push(clonedItem);

                      console.log(`✅ UILayout: 生成列表项 [${index}]`, {
                          itemType: clonedItem.uiComponentType,
                          itemId: clonedItem.componentId
                      });

                  } catch (error) {
                      console.error(`❌ UILayout: 生成列表项 [${index}] 失败`, error);
                  }
              });

              // 更新布局
              this.updateLayout();

              // 🔑 第五步：更新滚动范围（如果启用滚动）
              if (this.scrollEnabled) {
                  this.updateScrollRange();
              }

              console.log('✅ UILayout: 模板渲染完成', {
                  generatedCount: this.generatedItems.length,
                  totalChildren: this.children ? this.children.length : 0,
                  scrollEnabled: this.scrollEnabled,
                  maxScrollTop: this.maxScrollTop
              });
          }

          /**
           * 🔑 清除所有子元素
           */
          clearAllChildren() {
              console.log('🔄 UILayout: 清除所有子元素', {
                  currentCount: this.children ? this.children.length : 0
              });

              // 🔧 安全清除所有子元素
              const childrenToRemove = [...(this.children || [])]; // 创建副本避免迭代时修改
              childrenToRemove.forEach(child => {
                  try {
                      if (child && child.parent === this) {
                          this.removeChild(child);

                          // 🔧 安全清理资源
                          if (typeof child.destroy === 'function' && child.transform) {
                              child.destroy();
                          }
                      }
                  } catch (error) {
                      console.error('🚨 UILayout: 清理子对象时发生错误', error, child);
                  }
              });

              // 清空生成项目数组
              this.generatedItems = [];
              console.log('✅ UILayout: 所有子元素清除完成');
          }

          /**
           * 🔑 清除生成的列表项
           */
          clearGeneratedItems() {
              console.log('🔄 UILayout: 清除生成的列表项', {
                  currentCount: this.generatedItems.length
              });

              // 🔧 安全清理生成的项目
              this.generatedItems.forEach(item => {
                  try {
                      if (item && item.parent === this) {
                          this.removeChild(item);
                      }
                      // 🔧 安全清理资源
                      if (typeof item.destroy === 'function' && item.transform) {
                          item.destroy();
                      }
                  } catch (error) {
                      console.error('🚨 UILayout: 清理生成项目时发生错误', error, item);
                  }
              });

              this.generatedItems = [];
              console.log('✅ UILayout: 生成的列表项清除完成');
          }

          /**
           * 🔑 更新模板数据（不重新渲染，直接更新现有项目）
           */
          updateTemplateData(newDataArray) {
              console.log('🔄 UILayout: 更新模板数据', {
                  newDataCount: newDataArray ? newDataArray.length : 0,
                  currentGeneratedCount: this.generatedItems.length,
                  hasTemplate: !!this._cachedTemplate
              });

              this.templateData = newDataArray || [];

              // 如果已经有生成的项目，直接更新它们的数据
              if (this.generatedItems.length > 0 && this.templateData.length > 0) {
                  this.generatedItems.forEach((item, index) => {
                      if (index < this.templateData.length) {
                          const context = {
                              index: index,
                              isUpdate: true,
                              templateMode: true,
                              parentLayout: this,
                              data: this.templateData[index]  // 🔑 将数据放在 context 中
                          };

                          // 🔑 优先使用级联更新方法
                          if (typeof item.updateFieldsToChildren === 'function') {
                              item.updateFieldsToChildren(context);
                          } else if (typeof item.onFieldUpdate === 'function') {
                              item.onFieldUpdate(context);
                          }
                      }
                  });
                  console.log('✅ UILayout: 现有项目数据更新完成');
              } else {
                  // 否则重新渲染
                  this.renderFromTemplate();
              }
          }

          /**
           * 销毁时清理资源
           */
          destroy() {
              // 🔑 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true; // 防止重复销毁
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UILayout: 销毁脚本执行失败', error);
              }

              // 🔑 清理滚动相关资源
              if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  Graphics.app.ticker.remove(this._tickerCallback);
                  this._tickerCallback = null;
                  console.log('🗑️ UILayout: 已清理滚动事件监听');
              }

              // 清理生成的项目
              this.clearGeneratedItems();

              // 清理模板缓存
              if (this._cachedTemplate && typeof this._cachedTemplate.destroy === 'function') {
                  this._cachedTemplate.destroy();
              }
              this._cachedTemplate = null;
              this.templateData = [];

              // 调用父类的销毁方法
              if (super.destroy) {
                  super.destroy();
              }
          }

          /**
           * 🔑 获取克隆属性
           */
          getCloneProperties() {
              return {
                  x:this.x,
                  y:this.y,
                  // 基础属性
                  width: this.containerWidth,
                  height: this.containerHeight,

                  // 布局属性
                  layoutType: this.layoutType,
                  spacing: this.spacing,
                  padding: this.padding,
                  horizontalSpacing: this.horizontalSpacing,
                  verticalSpacing: this.verticalSpacing,
                  columns: this.columns,
                  rows: this.rows,
                  mainAxisAlignment: this.mainAxisAlignment,
                  crossAxisAlignment: this.crossAxisAlignment,
                  autoUpdate: this.autoUpdate,

                  // 高级设置
                  wrapContent: this.wrapContent || false,
                  reverseOrder: this.reverseOrder || false,

                  // 🔑 滚动相关设置
                  scrollEnabled: this.scrollEnabled,
                  scrollSpeed: this.scrollSpeed,

                  // 🔑 深度克隆组件脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : []
              };
          }

          /**
           * 克隆布局管理器
           */
          clone(options = {}) {


              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = this.getCloneProperties();

              // 2. 创建克隆对象
              const clonedLayout = new UILayout(cloneProperties);
  console.log('--------------------------------------------------------------------');
  console.log(this.x, this.y);

              // 3. 设置位置和变换属性
              clonedLayout.x = this.x + (offsetPosition ? offsetX : 0);
              clonedLayout.y = this.y + (offsetPosition ? offsetY : 0);
              clonedLayout.scale.x = this.scale.x;
              clonedLayout.scale.y = this.scale.y;
              clonedLayout.rotation = this.rotation;
              clonedLayout.alpha = this.alpha;
              clonedLayout.visible = this.visible;
              // clonedLayout.anchor.x = this.anchor.x;
              // clonedLayout.anchor.y = this.anchor.y;
              clonedLayout.pivot.x = this.pivot.x;
              clonedLayout.pivot.y = this.pivot.y;
              clonedLayout.skew.x = this.skew.x;
              clonedLayout.skew.y = this.skew.y;
              clonedLayout.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child) {
                      let clonedChild = null;

                      if (typeof child.clone === 'function') {
                          // UI组件有 clone 方法，直接调用
                          clonedChild = child.clone({ offsetPosition: false });
                      } else {
                          // 对于没有 clone 方法的对象（如 PIXI.Container），创建一个基础副本
                          console.log(`🔄 UILayout: 克隆基础对象 [${i}]: ${child.constructor.name}`);

                          // 创建同类型的新对象
                          const ChildClass = child.constructor;
                          clonedChild = new ChildClass();

                          // 复制基础属性
                          if (typeof child.x === 'number') clonedChild.x = child.x;
                          if (typeof child.y === 'number') clonedChild.y = child.y;
                          if (typeof child.visible === 'boolean') clonedChild.visible = child.visible;
                          if (typeof child.alpha === 'number') clonedChild.alpha = child.alpha;
                          if (typeof child.rotation === 'number') clonedChild.rotation = child.rotation;

                          // 复制 scale 属性
                          if (child.scale && clonedChild.scale) {
                              if (typeof child.scale.x === 'number') clonedChild.scale.x = child.scale.x;
                              if (typeof child.scale.y === 'number') clonedChild.scale.y = child.scale.y;
                          }

                          // 递归克隆子对象
                          if (child.children && child.children.length > 0) {
                              for (let j = 0; j < child.children.length; j++) {
                                  const grandChild = child.children[j];
                                  if (grandChild && typeof grandChild.clone === 'function') {
                                      const clonedGrandChild = grandChild.clone({ offsetPosition: false });
                                      clonedChild.addChild(clonedGrandChild);
                                  }
                              }
                          }
                      }

                      if (clonedChild) {
                          clonedLayout.addChild(clonedChild);
                          clonedChildren.push(clonedChild);
                          console.log(`✅ UILayout: 子对象 [${i}] 克隆成功: ${child.constructor.name}`);
                      }
                  }
              }


              return clonedLayout;
          }
      }

      // 导出到全局
      window.UILayout = UILayout;


  })();

  // ===== uiButton.js =====
  /**
       * UIButton - 可交互的按钮UI组件，继承自PIXI.Container
       */
      class UIButton extends PIXI.Container {
          constructor(properties = {}) {
              super();

              console.log('🔘 UIButton: 创建按钮（继承自PIXI.Container）', properties);

              // 标识为UI组件
              this.isUIComponent = true;
              this.uiComponentType = 'UIButton';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 使用UIScriptManager添加脚本功能
              if (window.UIScriptManager) {
                  window.UIScriptManager.applyToObject(this, properties);
              }

              // 初始化按钮组件
              this.initializeButton(properties);

              // 🔑 标记为已创建，onStart会在添加到父容器时执行
              this._isCreated = true;
          }

          /**
           * 初始化按钮组件
           * @param {Object} properties 按钮属性
           */
          initializeButton(properties) {
              // 基础属性
              this.x = properties.x || 0;
              this.y = properties.y || 0;
              this.buttonWidth = properties.width || 120;
              this.buttonHeight = properties.height || 40;
              this.enabled = properties.enabled !== false;

              // 🔑 子组件绑定属性（通过属性面板设置）
              this.boundDefaultSprite = properties.boundDefaultSprite || null;   // 绑定的默认状态精灵 (UIImage)
              this.boundHoverSprite = properties.boundHoverSprite || null;       // 绑定的悬停状态精灵 (UIImage)
              this.boundPressedSprite = properties.boundPressedSprite || null;   // 绑定的按下状态精灵 (UIImage)

              // 🔑 事件执行控制（默认禁用，可在属性面板中启用用于调试）
              this.executeEventsInEditor = false;

              // 状态管理
              this.currentState = 'default';
              this._pressed = false;
              this._hovered = false;

              // 行为属性
              this.enabled = properties.enabled !== false;

              // 初始化容器
              this.initializeContainer();

              // 设置事件监听器（使用RPG Maker MZ的TouchInput系统）
              this.setupEventListeners();

              // 初始更新
              this.updateButton();

              console.log('✅ UIButton: 按钮创建完成');
          }



          /**
           * 初始化容器（Container 不需要默认精灵，所有状态由外部绑定）
           */
          initializeContainer() {
              // 设置容器的交互属性
              this.interactive = true;
              this.interactiveChildren = true;

              // 设置容器的基础尺寸（用于碰撞检测等）
              this.containerWidth = this.buttonWidth;
              this.containerHeight = this.buttonHeight;

              console.log('✅ UIButton 容器初始化完成:', this.containerWidth, 'x', this.containerHeight);
          }

          /**
           * 🔑 核心方法：更新按钮
           */
          updateButton() {
              this.updateButtonDisplay();
          }

          /**
           * 更新按钮显示
           */
          updateButtonDisplay() {
              // 隐藏所有状态精灵
              this.hideAllStateSprites();

              // 显示当前状态的精灵
              const currentSprite = this.getCurrentStateSprite();
              if (currentSprite) {
                  currentSprite.visible = true;
                  console.log('🎨 UIButton: 显示状态精灵', this.currentState);
              }
          }

          /**
           * 隐藏所有状态精灵
           */
          hideAllStateSprites() {
              if (this.boundDefaultSprite) this.boundDefaultSprite.visible = false;
              if (this.boundHoverSprite) this.boundHoverSprite.visible = false;
              if (this.boundPressedSprite) this.boundPressedSprite.visible = false;
          }

          /**
           * 获取当前状态的精灵
           */
          getCurrentStateSprite() {
              switch (this.currentState) {
                  case 'hover':
                      return this.boundHoverSprite || this.boundDefaultSprite;
                  case 'pressed':
                      return this.boundPressedSprite || this.boundDefaultSprite;
                  default:
                      return this.boundDefaultSprite;
              }
          }



          /**
           * 切换状态
           */
          switchToState(newState) {
              if (this.currentState === newState) return;

              console.log('🔄 UIButton: 状态切换', this.currentState, '->', newState);
              this.currentState = newState;
              this.updateButton();
          }

          /**
           * 设置事件监听器 - 使用 RPG Maker MZ 的 TouchInput 系统
           */
          setupEventListeners() {
              // 初始化按钮状态
              this._wasPressed = false;
              this._wasHovered = false;

              // 双击检测相关
              this._doubleClickCount = 0;
              this._doubleClickTimer = null;

              // 保存原始的 update 方法
              // if (!this._originalUpdate) {
              //     this._originalUpdate = this.update;
              // }

              // 🔑 使用 RPG Maker MZ 的循环事件系统（参考 UIItem）
              this.setupRPGMakerEventLoop();

              // 自动注册到全局注册表
              if (typeof window.registerUIButton === 'function') {
                  window.registerUIButton(this);
              }

              console.log('✅ UIButton: 事件监听器设置完成');
          }

          /**
           * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIItem）
           */
          setupRPGMakerEventLoop() {
              // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
              if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  this._tickerCallback = () => this.processButtonTouch();
                  Graphics.app.ticker.add(this._tickerCallback);
                  console.log('✅ UIButton: 已注册到 PIXI ticker 循环');
                  return;
              }

              console.warn('⚠️ UIButton: 无法注册到 RPG Maker MZ 循环');
          }



          /**
           * 处理按钮触摸事件 - 使用 RPG Maker MZ 的 TouchInput 系统（通过循环事件调用）
           */
          processButtonTouch() {
              if (!this.enabled || !this.visible) {
                  // console.log('🚫 UIButton: 按钮被禁用或不可见', {
                  //     enabled: this.enabled,
                  //     visible: this.visible,
                  //     alpha: this.alpha,
                  //     interactive: this.interactive
                  // });
                  return;
              }

              // 检查TouchInput是否可用 - 通过window访问
              if (typeof window.TouchInput === 'undefined') {
                  console.log('🚫 UIButton: TouchInput未定义');
                  return;
              }

              const isBeingTouched = this.isBeingTouched();
              const isTriggered = window.TouchInput.isTriggered();
              const isReleased = window.TouchInput.isReleased();

              // 添加调试日志
              // if (isBeingTouched || isTriggered || isReleased) {
              //     console.log('🎯 UIButton: 触摸状态', {
              //         isBeingTouched,
              //         isTriggered,
              //         isReleased,
              //         _wasHovered: this._wasHovered,
              //         _wasPressed: this._wasPressed
              //     });
              // }

              // 参考UISlider的逻辑进行状态管理
              if (isBeingTouched) {
                  // 鼠标在按钮上

                  // 处理悬停状态
                  if (!this._wasHovered) {
                      this.onButtonHover();
                      this._wasHovered = true;
                  }

                  // 处理按下状态
                  if (isTriggered) {
                      this._pressed = true;
                      this._wasPressed = true;
                      this.onButtonPress();
                  }

                  // 保持按下状态 - 只要TouchInput.isPressed()且鼠标在按钮上
                  if (window.TouchInput.isPressed()) {
                      this._pressed = true;
                  }
              } else {
                  // 鼠标不在按钮上
                  if (this._wasHovered) {
                      this.onButtonHoverOut();
                      this._wasHovered = false;
                  }

                  // � 关键修复：只有在没有按下时才清除_pressed状态
                  // 如果正在按下，保持状态直到释放
                  if (!window.TouchInput.isPressed()) {
                      this._pressed = false;
                  } else {
                      console.log('🔄 UIButton: 鼠标离开按钮，但仍在按下，保持_pressed状态');
                  }
              }

              // 处理释放状态 - 无论鼠标在哪里，只要释放就触发
              if (this._wasPressed && isReleased) {
                  console.log('🎯 UIButton: 检测到释放事件，_wasPressed =', this._wasPressed);
                  // 🔑 先调用 onButtonRelease()，再清除状态
                  this.onButtonRelease();
                  this._pressed = false;
                  this._wasPressed = false;
              }
          }

          /**
           * 检查是否被触摸
           */
          isBeingTouched() {
              if (typeof window.TouchInput === 'undefined') return false;

              // 获取按钮在屏幕上的位置
              const bounds = this.getBounds();
              const touchX = window.TouchInput.x;
              const touchY = window.TouchInput.y;

              const isHit = touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                           touchY >= bounds.y && touchY <= bounds.y + bounds.height;

              // 🔍 调试：检查是否在容器内且容器可能阻挡事件
              if (this.parent && this.parent.uiComponentType === 'UIList') {
                  const parentBounds = this.parent.getBounds();
                  const insideParent = touchX >= parentBounds.x && touchX <= parentBounds.x + parentBounds.width &&
                                     touchY >= parentBounds.y && touchY <= parentBounds.y + parentBounds.height;

                  console.log('🔍 UIButton在UIList内的碰撞检测:', {
                      buttonName: this.name || 'unnamed',
                      buttonBounds: bounds,
                      parentBounds: parentBounds,
                      touchPoint: { x: touchX, y: touchY },
                      hitButton: isHit,
                      insideParent: insideParent,
                      parentInteractive: this.parent.interactive,
                      buttonVisible: this.visible,
                      buttonWorldVisible: this.worldVisible
                  });
              }

              return isHit;
          }

          /**
           * 检查是否有启用的指定生命周期方法
           */
          hasEnabledLifecycleMethod(methodName) {
              if (!this.componentScripts || !Array.isArray(this.componentScripts)) {
                  return false;
              }

              return this.componentScripts.some(script => {
                  if (!script.enabled || !script.code) return false;
                  // 检查脚本代码中是否包含未注释的指定函数定义
                  const lines = script.code.split('\n');
                  return lines.some(line => {
                      const trimmed = line.trim();
                      // 排除注释行
                      if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*')) {
                          return false;
                      }
                      // 检查是否有指定函数定义
                      const regex = new RegExp(`function\\s+${methodName}\\s*\\(`);
                      return regex.test(trimmed);
                  });
              });
          }

          /**
           * 按钮悬停事件
           */
          onButtonHover() {
              if (!this.enabled) return;
              // console.log('🔄 UIButton: 鼠标进入');
              this._hovered = true;
              if (!this._pressed) {
                  this.switchToState('hover');
              }
              // 🔑 只有启用了 onHover 方法时才执行脚本
              if (this.executeScript && this.hasEnabledLifecycleMethod('onHover')) {
                  this.executeScript('onHover');
              }
          }

          /**
           * 按钮离开事件
           */
          onButtonHoverOut() {
              // console.log('🔄 UIButton: 鼠标离开');
              this._hovered = false;
              if (!this._pressed) {
                  this.switchToState('default');
              }
              // 🔑 只有启用了 onHoverOut 方法时才执行脚本
              if (this.executeScript && this.hasEnabledLifecycleMethod('onHoverOut')) {
                  this.executeScript('onHoverOut');
              }
          }

          /**
           * 按钮按下事件
           */
          onButtonPress() {
              if (!this.enabled) return;
              console.log('👇 UIButton: 按钮按下');
              this._pressed = true;
              this.switchToState('pressed');
              // 🔑 只有启用了 onPress 方法时才执行脚本
              if (this.executeScript && this.hasEnabledLifecycleMethod('onPress')) {
                  this.executeScript('onPress');
              }
          }

          /**
           * 按钮释放事件
           */
          onButtonRelease() {
              if (!this.enabled) return;
              console.log('👆 UIButton: 按钮释放', { _pressed: this._pressed, _wasPressed: this._wasPressed });

              // 保存按下状态，因为后面会清除
              const wasPressed = this._pressed || this._wasPressed;

              if (wasPressed) {
                  console.log('🎯 UIButton: 检测到按钮被按下过，触发点击事件');
                  this.handleClick(); // 处理点击和双击
              } else {
                  console.log('⚠️ UIButton: 按钮未被按下，不触发点击事件');
              }

              this._pressed = false;
              this.switchToState(this._hovered ? 'hover' : 'default');
              // 🔑 只有启用了 onRelease 方法时才执行脚本
              if (this.executeScript && this.hasEnabledLifecycleMethod('onRelease')) {
                  this.executeScript('onRelease');
              }
          }

          /**
           * 处理点击事件（包括双击检测）
           */
          handleClick() {
              console.log('🎯 UIButton: handleClick 被调用');

              // 🔑 检查是否有启用的双击脚本
              const hasDoubleClickScript = this.hasEnabledLifecycleMethod('onDoubleClick');

              console.log('🔍 UIButton: 双击检测', {
                  hasDoubleClickScript,
                  _doubleClickCount: this._doubleClickCount
              });

              if (hasDoubleClickScript) {
                  // 有双击脚本，需要检测双击
                  this._doubleClickCount = (this._doubleClickCount || 0) + 1;

                  if (this._doubleClickCount === 1) {
                      // 第一次点击，设置定时器
                      console.log('⏰ UIButton: 设置单击延迟定时器');
                      this._doubleClickTimer = setTimeout(() => {
                          this._doubleClickCount = 0;
                          // 单击事件
                          console.log('⚡ UIButton: 延迟执行单击脚本');
                          if (this.executeScript && this.hasEnabledLifecycleMethod('onClick')) {
                              this.executeScript('onClick');
                          }
                      }, 300); // 300ms 内的第二次点击算作双击
                  } else if (this._doubleClickCount === 2) {
                      // 第二次点击，清除定时器并执行双击事件
                      console.log('⚡ UIButton: 执行双击脚本');
                      if (this._doubleClickTimer) {
                          clearTimeout(this._doubleClickTimer);
                      }
                      this._doubleClickCount = 0;
                      if (this.executeScript && this.hasEnabledLifecycleMethod('onDoubleClick')) {
                          this.executeScript('onDoubleClick');
                      }
                      return; // 不执行单击事件
                  }
              } else {
                  // 没有双击脚本，直接执行单击
                  console.log('⚡ UIButton: 直接执行单击脚本');
                  if (this.executeScript && this.hasEnabledLifecycleMethod('onClick')) {
                      this.executeScript('onClick');
                  }
              }
          }

          /**
           * 检查按钮是否被按下（用于阻止地图点击穿透）
           */
          isPressed() {
              const pressed = this._pressed;
              if (pressed) {
                  console.log('🔴 UIButton.isPressed() = true, 按钮被按下');
              }
              return pressed;
          }





          /**
           * 设置启用状态
           */
          setEnabled(enabled) {
              this.enabled = enabled;
              this.interactive = enabled;
              this.alpha = enabled ? 1.0 : 0.5;

              if (enabled) {
                  this.switchToState('default');
              } else {
                  this.switchToState('disabled');
              }
          }

          /**
           * 设置尺寸
           */
          setSize(width, height) {
              this.buttonWidth = width;
              this.buttonHeight = height;
              this.updateButton();
              console.log('📏 UIButton: 尺寸更新', width, height);
          }

          /**
           * 获取宽度
           */
          get width() {
              return this.buttonWidth;
          }

          /**
           * 设置宽度
           */
          set width(value) {
              this.buttonWidth = value;
              this.updateButton();
          }

          /**
           * 获取高度
           */
          get height() {
              return this.buttonHeight;
          }

          /**
           * 设置高度
           */
          set height(value) {
              this.buttonHeight = value;
              this.updateButton();
          }

          /**
           * 检查对象是否为 UIImage 类型
           */
          isUIImageType(obj) {
              if (!obj) return false;

              // 方法1: 检查 UI 组件标识
              if (obj.isUIComponent === true && obj.uiComponentType === 'UIImage') {
                  return true;
              }

              // 方法2: 检查构造函数名称
              if (obj.constructor && obj.constructor.name === 'UIImage') {
                  return true;
              }

              // 方法3: 检查特征属性（UIImage 特有的属性）
              if (obj.imagePath !== undefined || obj.regions !== undefined ||
                  obj.scaleMode !== undefined || obj.preserveAspectRatio !== undefined) {
                  return true;
              }

              return false;
          }

          /**
           * 🔑 检查对象是否是当前UIButton的子集
           * 只有UIButton的子对象才能被绑定
           */
          isChildOfThisButton(obj) {
              if (!obj) return false;

              // 检查对象是否在当前UIButton的children数组中
              return this.children.includes(obj);
          }

          /**
           * 🔑 检查对象是否可以绑定到当前UIButton
           * 必须同时满足：1. 是UIImage类型  2. 是当前UIButton的子对象
           */
          canBindToThisButton(obj) {
              if (!this.isUIImageType(obj)) {
                  console.warn('🚨 UIButton: 绑定对象必须是 UIImage 类型', {
                      provided: obj?.constructor?.name || 'unknown',
                      expected: 'UIImage'
                  });
                  return false;
              }

              if (!this.isChildOfThisButton(obj)) {
                  console.warn('🚨 UIButton: 绑定对象必须是当前UIButton的子对象', {
                      objectName: obj.name || 'unnamed',
                      buttonName: this.name || 'unnamed',
                      isChild: false
                  });
                  return false;
              }

              return true;
          }



          /**
           * 绑定默认状态精灵
           */
          bindDefaultSprite(sprite) {
              if (!this.canBindToThisButton(sprite)) {
                  console.error('🚨 UIButton: 无法绑定 DefaultSprite', {
                      reason: '对象必须是UIImage类型且为当前UIButton的子对象'
                  });
                  return;
              }

              this.boundDefaultSprite = sprite;
              this.updateButton();
              console.log('✅ UIButton: 成功绑定 DefaultSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          /**
           * 绑定悬停状态精灵
           */
          bindHoverSprite(sprite) {
              if (!this.canBindToThisButton(sprite)) {
                  console.error('🚨 UIButton: 无法绑定 HoverSprite', {
                      reason: '对象必须是UIImage类型且为当前UIButton的子对象'
                  });
                  return;
              }

              this.boundHoverSprite = sprite;
              this.updateButton();
              console.log('✅ UIButton: 成功绑定 HoverSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          /**
           * 绑定按下状态精灵
           */
          bindPressedSprite(sprite) {
              if (!this.canBindToThisButton(sprite)) {
                  console.error('🚨 UIButton: 无法绑定 PressedSprite', {
                      reason: '对象必须是UIImage类型且为当前UIButton的子对象'
                  });
                  return;
              }

              this.boundPressedSprite = sprite;
              this.updateButton();
              console.log('✅ UIButton: 成功绑定 PressedSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          /**
           * 克隆当前 UIButton 对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UIButton} 克隆的 UIButton 对象
           */
          clone(options = {}) {
              console.log('🔄 UIButton: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = {
                  // 基础属性
                  width: this.width,
                  height: this.height,
                  enabled: this.enabled,
                  visible: this.visible,

                  // 🔑 UIComponent 属性（安全访问）
                  name: this.name || '',
                  dataBinding: this.dataBinding || '',

                  // 🔑 深度克隆组件脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : [],

                  // 🔑 按钮事件系统
                  buttonEvents: this.buttonEvents ? { ...this.buttonEvents } : {}
              };

              // 2. 创建克隆对象
              const clonedButton = new UIButton(cloneProperties);

              // 3. 设置位置和变换属性
              clonedButton.x = this.x + (offsetPosition ? offsetX : 0);
              clonedButton.y = this.y + (offsetPosition ? offsetY : 0);
              clonedButton.scale.x = this.scale.x;
              clonedButton.scale.y = this.scale.y;
              clonedButton.rotation = this.rotation;
              clonedButton.alpha = this.alpha;
              clonedButton.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedButton.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              // 5. 重建绑定关系
              this.rebuildBindingsForClone(clonedButton, clonedChildren);

              console.log('✅ UIButton: 克隆完成，包含', clonedChildren.length, '个子对象');
              return clonedButton;
          }

          /**
           * 为克隆对象重建绑定关系
           * @param {UIButton} clonedButton 克隆的按钮对象
           * @param {Array} clonedChildren 克隆的子对象数组
           */
          rebuildBindingsForClone(clonedButton, clonedChildren) {
              console.log('🔗 UIButton: 重建克隆对象的绑定关系');

              // 找到原始绑定对象在子对象数组中的索引
              const findChildIndex = (boundObject) => {
                  if (!boundObject) return -1;
                  for (let i = 0; i < this.children.length; i++) {
                      if (this.children[i] === boundObject) {
                          return i;
                      }
                  }
                  return -1;
              };

              // 重新绑定默认状态精灵
              if (this.boundDefaultSprite) {
                  const index = findChildIndex(this.boundDefaultSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedButton.bindDefaultSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定默认状态精灵');
                  }
              }

              // 重新绑定悬停状态精灵
              if (this.boundHoverSprite) {
                  const index = findChildIndex(this.boundHoverSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedButton.bindHoverSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定悬停状态精灵');
                  }
              }

              // 重新绑定按下状态精灵
              if (this.boundPressedSprite) {
                  const index = findChildIndex(this.boundPressedSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedButton.bindPressedSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定按下状态精灵');
                  }
              }
          }

          /**
           * 销毁UIButton并从注册表中清理
           */
          destroy() {
              console.log('🗑️ UIButton.destroy() 被调用！', this.name || 'unnamed');

              // 🔑 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true; // 防止重复销毁
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UIButton: 销毁脚本执行失败', error);
              }

              // 🔑 立即禁用按钮
              this.enabled = false;
              this.visible = false;

              // 🔑 禁用交互
              this.interactive = false;
              this.interactiveChildren = false;

              // 🔑 清理 PIXI ticker 注册
              if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  Graphics.app.ticker.remove(this._tickerCallback);
                  this._tickerCallback = null;
              }

              // 从全局注册表中注销
              if (typeof window.unregisterUIButton === 'function') {
                  window.unregisterUIButton(this);
              }

              // 清理按钮事件
              this.buttonEvents = {};

              // 清理绑定的精灵引用
              this.boundDefaultSprite = null;
              this.boundHoverSprite = null;
              this.boundPressedSprite = null;

              // 清理定时器
              if (this._doubleClickTimer) {
                  clearTimeout(this._doubleClickTimer);
                  this._doubleClickTimer = null;
              }

              // 调用父类的destroy方法（如果存在）
              if (super.destroy) {
                  super.destroy();
              }

              console.log('🗑️ UIButton: 已销毁并清理注册表');
          }

      }

      // 导出到全局
      window.UIButton = UIButton;

      // 全局UIButton实例注册表
      window._uiButtonRegistry = window._uiButtonRegistry || [];

      // 注册UIButton实例
      window.registerUIButton = function(button) {
          if (window._uiButtonRegistry.indexOf(button) === -1) {
              window._uiButtonRegistry.push(button);
              console.log('📝 UIButton已注册，当前总数:', window._uiButtonRegistry.length);
          }
      };

      // 注销UIButton实例
      window.unregisterUIButton = function(button) {
          const index = window._uiButtonRegistry.indexOf(button);
          if (index > -1) {
              window._uiButtonRegistry.splice(index, 1);
              console.log('🗑️ UIButton已注销，当前总数:', window._uiButtonRegistry.length);
          }
      };

      // 检查是否有任何UIButton被按下
      window.isAnyUIButtonPressed = function() {
          // console.log('🔍 检查全局UIButton注册表:', {
          //     总数: window._uiButtonRegistry.length,
          //     按钮列表: window._uiButtonRegistry.map(btn => ({
          //         name: btn.name || 'unnamed',
          //         enabled: btn.enabled,
          //         visible: btn.visible,
          //         isPressed: btn.isPressed ? btn.isPressed() : 'no method'
          //     }))
          // });

          const result = window._uiButtonRegistry.some(button =>
              button.isPressed && button.isPressed()
          );

          // console.log('🔍 全局注册表检查结果:', result);
          return result;
      };

      // 扩展Scene_Map以支持UIButton的点击穿透阻止
      // 使用延迟加载确保Scene_Map已经定义
      setTimeout(() => {
          if (typeof window.Scene_Map !== 'undefined') {
              // console.log('🔧 开始扩展Scene_Map以支持UIButton点击穿透阻止');

              // 保存原始的isAnyButtonPressed方法
              const _Scene_Map_isAnyButtonPressed = window.Scene_Map.prototype.isAnyButtonPressed;

              window.Scene_Map.prototype.isAnyButtonPressed = function() {
                  // 调用原始方法检查菜单按钮等
                  const originalResult = _Scene_Map_isAnyButtonPressed.call(this);
                  if (originalResult) {
                      // console.log('🚫 原始按钮被按下，阻止地图点击');
                      return true;
                  }

                  // 检查是否有UIButton被按下
                  const uiButtonPressed = this.isAnyUIButtonPressed();

                  // 添加详细调试日志
                  // console.log('� Scene_Map.isAnyButtonPressed检查', {
                  //     originalResult,
                  //     uiButtonPressed,
                  //     TouchInputPressed: window.TouchInput ? window.TouchInput.isPressed() : 'undefined',
                  //     TouchInputTriggered: window.TouchInput ? window.TouchInput.isTriggered() : 'undefined'
                  // });

                  if (uiButtonPressed) {
                      // console.log('� UIButton被按下，阻止地图点击穿透');
                  }
                  return uiButtonPressed;
              };

              window.Scene_Map.prototype.isAnyUIButtonPressed = function() {
                  // 使用全局注册表检查UIButton，而不是搜索场景树
                  if (typeof window.isAnyUIButtonPressed === 'function') {
                      const result = window.isAnyUIButtonPressed();
                      // console.log('🔍 全局UIButton注册表检查结果:', result);
                      return result;
                  }
                  return false;
              };



              // console.log('✅ Scene_Map扩展完成，支持UIButton点击穿透阻止');
          } else {
              console.warn('⚠️ Scene_Map未找到，无法扩展点击穿透阻止功能');
          }
      }, 100); // 延迟100ms确保Scene_Map已加载

      console.log('✅ UIButton插件加载完成');



  // ===== uiSlider.js =====
  //=============================================================================
  // ui.js
  //=============================================================================

  (() => {
//=============================================================================
      // Slider Class
      //=============================================================================

      /**
       * 滑动条类 - 继承自 Container
       * Slider 作为容器管理轨道、进度条、滑块和标签等子组件
       * 通过属性面板绑定轨道精灵、滑块精灵、进度精灵、文本精灵
       * 所有子组件都是外部绑定，Slider 只负责逻辑控制和位置计算
       */
      class UISlider extends PIXI.Container {
          constructor(properties = {}) {
              super();

              // 标识为 UI 组件
              this.isUIComponent = true;
              this.uiComponentType = 'Slider';

              this.initializeSlider(properties);
          }

          /**
           * 初始化滑动条
           * @param {Object} properties 滑动条属性
           */
          initializeSlider(properties) {
              // 设置默认属性
              this.setupDefaultProperties(properties);

              // 初始化容器
              this.initializeContainer();

              // 绑定事件
              this.setupEventListeners();

              // 初始更新
              this.updateSlider();

              console.log('Slider created:', this.sliderWidth, 'x', this.sliderHeight);
          }

          /**
           * 🔑 初始化脚本管理器
           */
          initializeScriptManager() {
              if (typeof window.UIScriptManager !== 'undefined') {
                  // 使用 UIScriptManager 为 UISlider 添加脚本功能
                  window.UIScriptManager.applyToObject(this, { componentScripts: this.componentScripts });
                  console.log('✅ UISlider: 脚本管理器初始化完成');
              } else {
                  console.warn('⚠️ UISlider: UIScriptManager 不可用');
              }
          }

          /**
           * 设置默认属性
           */
          setupDefaultProperties(properties) {
              // 基础属性
              this.sliderWidth = properties.width || 200;
              this.sliderHeight = properties.height || 20;

              // 数值属性
              this.value = properties.value || 0;
              this.minValue = properties.minValue || 0;
              this.maxValue = properties.maxValue || 100;
              this.step = properties.step || 1;

              // 🔑 子组件绑定属性（通过属性面板设置）
              this.boundRangeSprite = properties.boundRangeSprite || null;       // 绑定的范围精灵 (UIImage) - 定义有效范围 ⭐
              this.boundProgressSprite = properties.boundProgressSprite || null; // 绑定的进度精灵 (UIImage) - 显示当前进度
              this.boundThumbSprite = properties.boundThumbSprite || null;       // 绑定的滑块精灵 (UIImage) - 可拖拽控制
              this.boundLabelSprite = properties.boundLabelSprite || null;       // 绑定的文本精灵 (UILabel) - 显示数值

              // 默认样式设置
              this.defaultProgressColor = properties.defaultProgressColor || '#00ff00';
              this.defaultTrackColor = properties.defaultTrackColor || '#404040';

              // 行为属性
              this.enabled = properties.enabled !== false;

              // 回调函数
              this.onChange = properties.onChange || null;
              this.onDragStart = properties.onDragStart || null;
              this.onDragEnd = properties.onDragEnd || null;

              // 事件代码属性（用于代码生成）
              this._eventCodes = properties._eventCodes || {};

              // 🔑 统一的脚本系统
              this.componentScripts = properties.componentScripts || [];

              // 🔑 事件执行控制（默认禁用，可在属性面板中启用用于调试）
              this.executeEventsInEditor = properties.executeEventsInEditor || false;

              // 🔑 初始化脚本管理器
              this.initializeScriptManager();

              // 内部状态
              this.isDragging = false;
              this.dragStartX = 0;
              this.dragStartValue = 0;
          }

          /**
           * 初始化容器（Container 不需要轨道位图，轨道由外部绑定）
           */
          initializeContainer() {
              // 设置容器的交互属性
              this.interactive = true;
              this.interactiveChildren = true;

              // 设置容器的基础尺寸（用于碰撞检测等）
              this.containerWidth = this.sliderWidth;
              this.containerHeight = this.sliderHeight;

              console.log('✅ Slider 容器初始化完成:', this.containerWidth, 'x', this.containerHeight);
          }

          /**
           * 🔑 核心方法：更新滑动条状态和动画
           */
          updateSlider() {
              // 🔑 执行动画（使用当前值计算位置）
              this.updateThumb();
              this.updateProgress();

              // 🔑 更新其他状态
              this.updateLabel();

              console.log('🔘 UISlider: 状态和动画更新完成');
          }

          /**
           * 🎯 纯动画方法：根据当前值更新进度显示（使用裁切）
           */
          updateProgress() {
              if (!this.boundProgressSprite) return;

              // 🔑 根据当前值计算进度
              const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

              // 🔑 如果有范围精灵，使用裁切方式显示进度
              if (this.boundRangeSprite) {
                  const rangeWidth = this.boundRangeSprite.width || 200;
                  const rangeHeight = this.boundRangeSprite.height || 20;

                  // 🔑 使用裁切区域来显示进度，而不是拉伸
                  this.applyProgressMask(this.boundProgressSprite, progress);

                  console.log('🔘 UISlider: 进度更新（裁切）', {
                      value: this.value,
                      progress: progress.toFixed(2),
                      maskWidth: (progress * rangeWidth).toFixed(1)
                  });
              }
          }

          /**
           * 更新绑定的进度精灵 - 使用遮罩实现
           */
          updateBoundProgressSprite(progress) {
              if (!this.boundProgressSprite) return;

              // 🎯 使用遮罩方式实现进度显示
              this.applyProgressMask(this.boundProgressSprite, progress);
          }



          /**
           * 🎯 纯动画方法：直接修改绑定对象坐标实现动画
           */
          updateThumb() {
              if (!this.boundThumbSprite) {
                  console.log('🔘 UISlider: 没有绑定滑块精灵，跳过动画');
                  return;
              }

              // 🔑 根据当前值计算滑块位置
              if (!this.boundRangeSprite) {
                  console.log('🔘 UISlider: 没有绑定范围精灵，跳过动画');
                  return;
              }

              // 获取范围精灵的尺寸（使用texture尺寸作为备选）
              const rangeWidth = this.boundRangeSprite.width ||
                                this.boundRangeSprite.texture?.width ||
                                this.sliderWidth || 200;
              const rangeHeight = this.boundRangeSprite.height ||
                                 this.boundRangeSprite.texture?.height ||
                                 this.sliderHeight || 20;

              // 获取滑块精灵的尺寸
              const thumbWidth = this.boundThumbSprite.width ||
                                this.boundThumbSprite.texture?.width || 20;
              const thumbHeight = this.boundThumbSprite.height ||
                                 this.boundThumbSprite.texture?.height || 20;

              // 计算进度比例
              const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

              // 计算目标位置（相对于范围精灵的位置）
              const baseX = this.boundRangeSprite.x || 0;
              const baseY = this.boundRangeSprite.y || 0;
              const targetX = baseX + progress * (rangeWidth - thumbWidth);
              const targetY = baseY + (rangeHeight - thumbHeight) / 2;

              // 🔑 直接设置滑块位置
              this.boundThumbSprite.x = targetX;
              this.boundThumbSprite.y = targetY;

              console.log('🔘 UISlider: 滑块位置更新', {
                  value: this.value,
                  progress: progress.toFixed(2),
                  rangeSize: `${rangeWidth}x${rangeHeight}`,
                  rangePos: `(${baseX}, ${baseY})`,
                  thumbSize: `${thumbWidth}x${thumbHeight}`,
                  targetPos: `(${targetX.toFixed(1)}, ${targetY.toFixed(1)})`
              });
          }

          /**
           * 更新文本显示
           */
          updateLabel() {
              if (!this.boundLabelSprite) return;

              // 🔑 更新 UILabel 的文本内容，让 UILabel 自己处理前缀、后缀和重绘
              this.boundLabelSprite.text = this.value.toString();

              // 🔑 调用 UILabel 的重绘方法，这样会正确处理前缀和后缀
              if (this.boundLabelSprite.redrawText && typeof this.boundLabelSprite.redrawText === 'function') {
                  this.boundLabelSprite.redrawText();
              } else if (this.boundLabelSprite.setText && typeof this.boundLabelSprite.setText === 'function') {
                  // 备用方法：调用 setText，它内部会调用 redrawText
                  this.boundLabelSprite.setText(this.value.toString());
              }
          }

          /**
           * 检查对象是否为 UIImage 类型
           */
          isUIImageType(obj) {
              if (!obj) return false;

              // 方法1: 检查 UI 组件标识
              if (obj.isUIComponent === true && obj.uiComponentType === 'UIImage') {
                  return true;
              }

              // 方法2: 检查构造函数名称
              if (obj.constructor && obj.constructor.name === 'UIImage') {
                  return true;
              }

              // 方法3: 检查特征属性（UIImage 特有的属性）
              if (obj.imagePath !== undefined || obj.regions !== undefined ||
                  obj.scaleMode !== undefined || obj.preserveAspectRatio !== undefined) {
                  return true;
              }

              return false;
          }

          /**
           * 检查对象是否为 UILabel 类型
           */
          isUILabelType(obj) {
              if (!obj) return false;

              // 方法1: 检查 UI 组件标识
              if (obj.isUIComponent === true && obj.uiComponentType === 'UILabel') {
                  return true;
              }

              // 方法2: 检查构造函数名称
              if (obj.constructor && obj.constructor.name === 'UILabel') {
                  return true;
              }

              // 方法3: 检查特征属性（UILabel 特有的属性）
              if (obj.text !== undefined || obj.fontSize !== undefined ||
                  obj.textColor !== undefined || obj.fontFace !== undefined) {
                  return true;
              }

              return false;
          }

          /**
           * 根据轨道精灵的有效长度更新 UISlider 的尺寸 - 已删除boundTrackSprite
           */
          updateSizeFromTrack() {
              console.log('🔧 UISlider: boundTrackSprite已删除，保持当前尺寸');
              return;
          }

          /**
           * 根据范围精灵的长度更新 UISlider 的总长度
           */
          updateSizeFromRange() {
              console.log('🔍 UISlider: updateSizeFromRange() 被调用');

              if (!this.boundRangeSprite) {
                  console.log('🔧 UISlider: 没有范围精灵，保持当前尺寸');
                  return;
              }

              console.log('🔍 UISlider: 范围精灵对象详情', {
                  sprite: this.boundRangeSprite,
                  spriteType: this.boundRangeSprite.constructor.name,
                  width: this.boundRangeSprite.width,
                  height: this.boundRangeSprite.height,
                  textureWidth: this.boundRangeSprite.texture?.width,
                  textureHeight: this.boundRangeSprite.texture?.height,
                  bitmap: this.boundRangeSprite.bitmap,
                  bitmapWidth: this.boundRangeSprite.bitmap?.width,
                  bitmapHeight: this.boundRangeSprite.bitmap?.height
              });

              // 🔑 获取范围精灵的尺寸作为滑动条的总长度
              const rangeWidth = this.boundRangeSprite.width ||
                               this.boundRangeSprite.texture?.width ||
                               this.boundRangeSprite.bitmap?.width || 0;
              const rangeHeight = this.boundRangeSprite.height ||
                                this.boundRangeSprite.texture?.height ||
                                this.boundRangeSprite.bitmap?.height || 0;

              console.log('🔍 UISlider: 计算出的范围精灵尺寸', {
                  rangeWidth: rangeWidth,
                  rangeHeight: rangeHeight,
                  currentSliderWidth: this.sliderWidth,
                  currentSliderHeight: this.sliderHeight
              });

              if (rangeWidth <= 0 || rangeHeight <= 0) {
                  console.warn('⚠️ UISlider: 范围精灵尺寸无效', {
                      width: rangeWidth,
                      height: rangeHeight
                  });
                  return;
              }

              // 🔑 检查尺寸是否真的发生了变化，避免不必要的更新
              const sizeChanged = this.sliderWidth !== rangeWidth || this.sliderHeight !== rangeHeight;

              if (!sizeChanged) {
                  console.log('🔘 UISlider: 尺寸未变化，跳过更新');
                  return;
              }

              // 🔑 范围精灵的长度就是滑动条的总长度
              const oldSliderWidth = this.sliderWidth;
              this.sliderWidth = rangeWidth;
              this.sliderHeight = rangeHeight;

              // 容器尺寸也使用范围精灵的尺寸
              this.containerWidth = rangeWidth;
              this.containerHeight = rangeHeight;

              console.log('🔧 UISlider: 根据范围精灵更新总长度', {
                  oldSliderWidth: oldSliderWidth,
                  newSliderWidth: this.sliderWidth,
                  rangeSpriteSize: `${rangeWidth}x${rangeHeight}`,
                  sliderTotalLength: rangeWidth,
                  containerSize: `${this.containerWidth}x${this.containerHeight}`
              });

              // 🔑 立刻进行布局设置
              this.layoutComponents();

              console.log('🔧 UISlider: updateSizeFromRange() 完成，当前 sliderWidth =', this.sliderWidth);
          }

          /**
           * 布局所有组件
           */
          layoutComponents() {
              console.log('🎯 UISlider: 开始布局组件');

              // 1. 布局轨道精灵（背景装饰，居中对齐）- boundTrackSprite已删除

              // 2. 布局范围精灵（定义有效范围）
              if (this.boundRangeSprite) {
                  this.boundRangeSprite.x = 0;
                  this.boundRangeSprite.y = 0;
                  console.log('📍 范围精灵布局: (0, 0) - 定义有效范围');
              }

              // 3. 布局进度精灵（显示当前进度）
              if (this.boundProgressSprite && this.boundRangeSprite) {
                  this.layoutProgressOnRange();
              }

              // 4. 布局滑块精灵（可拖拽控制）
              if (this.boundThumbSprite && this.boundRangeSprite) {
                  this.layoutThumbOnRange();
              }

              // 5. 文字除外，不参与自动布局
              console.log('📍 文字精灵不参与自动布局');
          }

          /**
           * 将进度精灵布局到范围精灵上 - 使用遮罩实现
           */
          layoutProgressOnRange() {
              if (!this.boundProgressSprite || !this.boundRangeSprite) return;

              // 进度精灵与范围精灵对齐
              this.boundProgressSprite.x = this.boundRangeSprite.x;
              this.boundProgressSprite.y = this.boundRangeSprite.y;

              // 根据当前值计算进度比例
              const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

              // 🎯 使用遮罩方式实现进度显示
              this.applyProgressMask(this.boundProgressSprite, progress);

              console.log('📍 进度精灵布局到范围精灵上（遮罩方式）:', {
                  progress: progress,
                  value: this.value
              });
          }

          /**
           * 应用进度显示 - 使用裁剪区域方法
           * @param {Sprite} targetSprite - 目标精灵
           * @param {number} progress - 进度比例 (0-1)
           */
          applyProgressMask(targetSprite, progress) {
              if (!targetSprite || !targetSprite.bitmap) return;

              // 获取原始图片尺寸
              const originalWidth = targetSprite.bitmap.width;
              const originalHeight = targetSprite.bitmap.height;

              // 计算显示宽度
              const displayWidth = Math.max(0, Math.round(progress * originalWidth));

              // 使用setFrame方法裁剪图片显示区域
              if (targetSprite.setFrame) {
                  // RPG Maker MZ的Sprite有setFrame方法
                  targetSprite.setFrame(0, 0, displayWidth, originalHeight);
              } else {
                  // 如果没有setFrame，直接设置texture的frame
                  if (targetSprite.texture && targetSprite.texture.frame) {
                      targetSprite.texture.frame.width = displayWidth;
                      targetSprite.texture.frame.height = originalHeight;
                      targetSprite.texture._updateUvs();
                  }
              }

              console.log('进度更新（裁剪区域）:', {
                  progress: progress,
                  displayWidth: displayWidth,
                  originalSize: `${originalWidth}x${originalHeight}`,
                  targetSprite: targetSprite.constructor.name
              });
          }

          /**
           * 将滑块精灵布局到范围精灵上
           */
          layoutThumbOnRange() {
              if (!this.boundThumbSprite || !this.boundRangeSprite) return;

              // 根据当前值计算滑块位置
              const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);
              const thumbX = this.boundRangeSprite.x + progress * this.sliderWidth;

              // 将滑块居中对齐到范围精灵上
              const thumbWidth = this.boundThumbSprite.width || this.boundThumbSprite.texture?.width || 20;
              const thumbHeight = this.boundThumbSprite.height || this.boundThumbSprite.texture?.height || 20;

              this.boundThumbSprite.x = thumbX - thumbWidth / 2;
              this.boundThumbSprite.y = this.boundRangeSprite.y + (this.sliderHeight - thumbHeight) / 2;

              console.log('📍 滑块精灵布局到范围精灵上:', {
                  progress: progress,
                  thumbX: this.boundThumbSprite.x,
                  thumbY: this.boundThumbSprite.y,
                  value: this.value
              });
          }

          /**
           * 🔑 检查对象是否是当前UISlider的子集
           */
          isChildOfThisSlider(obj) {
              if (!obj) return false;
              return this.children.includes(obj);
          }

          /**
           * 🔑 检查对象是否可以绑定到当前UISlider
           */
          canBindToThisSlider(obj) {
              if (!this.isUIImageType(obj)) {
                  console.warn('🚨 UISlider: 绑定对象必须是 UIImage 类型', {
                      provided: obj?.constructor?.name || 'unknown',
                      expected: 'UIImage'
                  });
                  return false;
              }

              if (!this.isChildOfThisSlider(obj)) {
                  console.warn('🚨 UISlider: 绑定对象必须是当前UISlider的子对象', {
                      objectName: obj.name || 'unnamed',
                      sliderName: this.name || 'unnamed',
                      isChild: false,
                      hint: '请先将UIImage拖拽到UISlider下作为子对象，然后再进行绑定'
                  });
                  return false;
              }

              return true;
          }

          /**
           * 🔑 绑定子组件的方法
           */
          // bindTrackSprite方法已删除 - boundTrackSprite属性不再需要

          bindRangeSprite(sprite) {
              console.log('🔍 UISlider: bindRangeSprite() 被调用', {
                  sprite: sprite,
                  spriteType: sprite?.constructor?.name
              });

              // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
              if (this.boundRangeSprite === sprite) {
                  console.log('🔍 UISlider: 范围精灵已绑定，跳过重复绑定');
                  return;
              }

              // 🔑 检查是否可以绑定
              if (!this.canBindToThisSlider(sprite)) {
                  console.error('❌ UISlider: 无法绑定 RangeSprite', {
                      reason: '对象必须是UIImage类型且为当前UISlider的子对象'
                  });
                  return;
              }

              console.log('🔍 UISlider: 设置 boundRangeSprite');
              this.boundRangeSprite = sprite;

              console.log('🔍 UISlider: 调用 updateSizeFromRange()');
              // 🔑 范围精灵的长度决定滑动条的总长度
              this.updateSizeFromRange();

              console.log('✅ UISlider: 成功绑定 RangeSprite (UIImage) - 定义有效范围');
          }

          bindThumbSprite(sprite) {
              // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
              if (this.boundThumbSprite === sprite) {
                  console.log('🔍 UISlider: 滑块精灵已绑定，跳过重复绑定');
                  return;
              }

              // 🔑 检查是否可以绑定
              if (!this.canBindToThisSlider(sprite)) {
                  console.error('❌ UISlider: 无法绑定 ThumbSprite', {
                      reason: '对象必须是UIImage类型且为当前UISlider的子对象'
                  });
                  return;
              }

              this.boundThumbSprite = sprite;

              // 🔑 绑定滑块后，重新计算轨道有效长度 - boundTrackSprite已删除

              this.updateThumb();
              console.log('✅ UISlider: 成功绑定 ThumbSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          bindProgressSprite(sprite) {
              // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
              if (this.boundProgressSprite === sprite) {
                  console.log('🔍 UISlider: 进度精灵已绑定，跳过重复绑定');
                  return;
              }

              // 🔑 检查是否可以绑定
              if (!this.canBindToThisSlider(sprite)) {
                  console.error('❌ UISlider: 无法绑定 ProgressSprite', {
                      reason: '对象必须是UIImage类型且为当前UISlider的子对象'
                  });
                  return;
              }

              this.boundProgressSprite = sprite;
              this.updateProgress();
              console.log('✅ UISlider: 成功绑定 ProgressSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          bindLabelSprite(sprite) {
              // 检查是否为 UILabel 类型
              if (!this.isUILabelType(sprite)) {
                  console.error('🚨 UISlider: boundLabelSprite 必须是 UILabel 类型', {
                      provided: sprite.constructor.name,
                      isUIComponent: sprite.isUIComponent,
                      uiComponentType: sprite.uiComponentType,
                      expected: 'UILabel'
                  });
                  return;
              }

              this.boundLabelSprite = sprite;
              this.updateLabel();
              console.log('✅ UISlider: 成功绑定 LabelSprite (UILabel)');
          }

          /**
           * 创建进度填充精灵 (sl_2)
           */
          createProgressSprite() {
              this.progressSprite = new Sprite();
              this.progressSprite.bitmap = ImageManager.loadBitmap('ui/slider/', 'sl_2');

              // 等待进度图片加载完成
              this.progressSprite.bitmap.addLoadListener(() => {
                  this.updateProgressDisplay();
                  console.log('✅ Slider 进度图片加载完成');
              });

              // 添加到轨道上方
              this.addChild(this.progressSprite);
          }

          /**
           * 重绘轨道 - 图片模式下不需要重绘
           */
          redrawTrack() {
              // 使用图片时不需要重绘轨道
              // 如果需要显示进度，可以考虑使用遮罩或者叠加图片
              console.log('Slider 使用图片模式，无需重绘轨道');
          }

          /**
           * 创建滑动按钮 - 使用图片 (sl_3)
           */
          createThumbSprite() {
              this.thumbSprite = new Sprite();

              // 加载滑动按钮图片 (sl_3)
              this.thumbSprite.bitmap = ImageManager.loadBitmap('ui/slider/', 'sl_3');

              // 等待图片加载完成后设置尺寸
              this.thumbSprite.bitmap.addLoadListener(() => {
                  this.thumbWidth = this.thumbSprite.bitmap.width;
                  this.thumbHeight = this.thumbSprite.bitmap.height;
                  this.updateThumbPosition();
                  console.log('✅ Slider 滑块图片加载完成:', this.thumbWidth, 'x', this.thumbHeight);
              });

              this.addChild(this.thumbSprite);
          }





          /**
           * 更新进度显示 - 使用统一的遮罩系统
           */
          updateProgressDisplay() {
              if (!this.progressSprite || !this.progressSprite.bitmap) {
                  return;
              }

              // 计算进度比例
              const progress = (this.value - this.minValue) / (this.maxValue - this.minValue);

              // 🎯 使用统一的遮罩方法
              this.applyProgressMask(this.progressSprite, progress);

              console.log('🎨 更新进度显示（统一遮罩）:', {
                  progress: progress,
                  value: this.value
              });
          }

          /**
           * 重绘滑动按钮 - 图片模式下不需要重绘
           */
          redrawThumb() {
              // 使用图片时不需要重绘按钮
              console.log('Slider 使用图片模式，无需重绘按钮');
          }

          /**
           * 设置事件监听器 - 使用 RPG Maker MZ 的 TouchInput 系统
           */
          setupEventListeners() {
              // 初始化拖拽状态
              this.isDragging = false;
              this.dragStartX = 0;
              this.dragStartValue = 0;

              // 🔑 使用 RPG Maker MZ 的循环事件系统（参考 UIButton）
              this.setupRPGMakerEventLoop();

              console.log('✅ Slider 事件监听器设置完成');
          }

          /**
           * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIItem）
           */
          setupRPGMakerEventLoop() {
              // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
              if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  this._tickerCallback = () => this.processSliderTouch();
                  Graphics.app.ticker.add(this._tickerCallback);
                  console.log('✅ UISlider: 已注册到 PIXI ticker 循环');
                  return;
              }

              console.warn('⚠️ UISlider: 无法注册到 RPG Maker MZ 循环');
          }

          /**
           * 🔑 清理循环事件注册和销毁生命周期
           */
          destroy() {
              console.log('🗑️ UISlider: 开始销毁滑块', this.name);

              // 🔑 执行 onDestroy 脚本
              if (this.executeScript && typeof this.executeScript === 'function') {
                  try {
                      this.executeScript('onDestroy');
                      console.log('🔄 UISlider: onDestroy 脚本执行完成');
                  } catch (error) {
                      console.error('❌ UISlider: onDestroy 脚本执行失败', error);
                  }
              }

              // 清理 PIXI ticker 注册
              if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  Graphics.app.ticker.remove(this._tickerCallback);
                  this._tickerCallback = null;
              }

              // 清理绑定的组件引用
              this.boundRangeSprite = null;
              this.boundThumbSprite = null;
              this.boundProgressSprite = null;
              this.boundLabelSprite = null;

              // 调用父类的 destroy
              if (super.destroy) {
                  super.destroy();
              }

              console.log('✅ UISlider: 销毁完成');
          }

          /**
           * 处理滑动条触摸事件 - 使用 RPG Maker MZ 的 TouchInput 系统（通过循环事件调用）
           */
          processSliderTouch() {
              if (!this.enabled || !this.visible) return;

              if (typeof TouchInput === 'undefined') return;

              // 🔑 在编辑器模式下且未启用事件执行时，不处理鼠标事件
              if (window.EDITOR_MODE && !this.executeEventsInEditor) {
                  return;
              }

              // 如果正在拖拽，优先处理拖拽逻辑（不管鼠标是否在滑块范围内）
              if (this.isDragging) {
                  if (TouchInput.isPressed()) {
                      // 继续拖拽
                      this.onSliderDrag();
                  } else if (TouchInput.isReleased()) {
                      // 释放拖拽
                      this.onSliderRelease();
                  }
                  return; // 拖拽中时不处理其他逻辑
              }

              // 只有在没有拖拽时才检查是否开始新的触摸
              if (this.isBeingTouched() && TouchInput.isTriggered()) {
                  this.onSliderPress();
              }
          }

          /**
           * 检查是否被触摸
           */
          isBeingTouched() {
              if (typeof TouchInput === 'undefined') return false;

              // 🔑 只检测 boundRangeSprite 的边界，而不是整个容器
              if (!this.boundRangeSprite) {
                  console.log('🔘 UISlider: 没有绑定范围精灵，无法检测触摸');
                  return false;
              }

              const bounds = this.boundRangeSprite.getBounds();
              const touchX = TouchInput.x;
              const touchY = TouchInput.y;

              const isHit = touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                           touchY >= bounds.y && touchY <= bounds.y + bounds.height;

              // console.log('🔍 UISlider: 触摸检测（仅范围精灵）', {
              //     touchPoint: `(${touchX}, ${touchY})`,
              //     rangeBounds: bounds,
              //     hitRange: isHit
              // });

              return isHit;
          }

          /**
           * 碰撞检测
           */
          hitTest(x, y) {
              return x >= 0 && x <= this.sliderWidth && y >= 0 && y <= this.sliderHeight;
          }

          /**
           * 滑动条按下事件
           */
          onSliderPress() {
              if (typeof TouchInput === 'undefined') return;

              // 🔑 使用与 isBeingTouched() 相同的边界计算方式
              if (!this.boundRangeSprite) {
                  console.log('🔘 UISlider: 没有绑定范围精灵，无法处理按下事件');
                  return;
              }

              const bounds = this.boundRangeSprite.getBounds();

              // 计算相对于范围精灵的本地坐标
              const localX = TouchInput.x - bounds.x;
              const localY = TouchInput.y - bounds.y;

              // 检查是否点击在滑动按钮上（如果有绑定的滑块精灵）
              if (this.boundThumbSprite) {
                  // 🔑 计算滑块相对于范围精灵的位置
                  const rangeBounds = this.boundRangeSprite.getBounds();
                  const thumbBounds = this.boundThumbSprite.getBounds();

                  const thumbX = thumbBounds.x - rangeBounds.x;
                  const thumbY = thumbBounds.y - rangeBounds.y;
                  const thumbWidth = thumbBounds.width || 16;
                  const thumbHeight = thumbBounds.height || 20;

                  console.log('🔍 UISlider: 滑块碰撞检测', {
                      localClick: `(${localX}, ${localY})`,
                      thumbArea: `(${thumbX}, ${thumbY}) ${thumbWidth}x${thumbHeight}`,
                      hitThumb: localX >= thumbX && localX <= thumbX + thumbWidth &&
                               localY >= thumbY && localY <= thumbY + thumbHeight
                  });

                  if (localX >= thumbX && localX <= thumbX + thumbWidth &&
                      localY >= thumbY && localY <= thumbY + thumbHeight) {
                      // 点击在滑动按钮上，开始拖拽
                      this.isDragging = true;
                      this.dragStartX = TouchInput.x;
                      this.dragStartValue = this.value;

                      // 执行回调函数
                      if (this.onDragStart) {
                          this.onDragStart(this.value);
                      }

                      // 🔑 执行 onSliderStart 脚本（新的脚本系统）
                      if (this.executeScript && typeof this.executeScript === 'function') {
                          try {
                              this.executeScript('onSliderStart', this.value);
                              console.log(`🔄 UISlider: onSliderStart 脚本执行完成`);
                          } catch (error) {
                              console.error('❌ UISlider: onSliderStart 脚本执行失败', error);
                          }
                      }

                      // 🔑 执行事件代码（保持兼容性）
                      this.executeEvent('onDragStart');

                      console.log('🎯 Slider: 开始拖拽');
                      return; // 退出，不执行轨道点击逻辑
                  }
              }

              // 点击在轨道上，跳转到该位置
              const rangeWidth = bounds.width || this.sliderWidth;
              const progress = Math.max(0, Math.min(1, localX / rangeWidth));
              const newValue = this.minValue + progress * (this.maxValue - this.minValue);
              this.setValue(newValue);

              console.log('🎯 Slider: 轨道点击', {
                  localX: localX,
                  rangeWidth: rangeWidth,
                  progress: progress.toFixed(2),
                  newValue: newValue
              });

              // 播放音效
              if (typeof SoundManager !== 'undefined') {
                  SoundManager.playCursor();
              }
          }

          /**
           * 滑动条拖拽事件
           */
          onSliderDrag() {
              if (!this.isDragging) return;

              const deltaX = TouchInput.x - this.dragStartX;
              const deltaValue = (deltaX / this.sliderWidth) * (this.maxValue - this.minValue);
              const newValue = this.dragStartValue + deltaValue;

              this.setValue(newValue);
          }

          /**
           * 滑动条释放事件
           */
          onSliderRelease() {
              if (!this.isDragging) return;

              this.isDragging = false;

              // 执行回调函数
              if (this.onDragEnd) {
                  this.onDragEnd(this.value);
              }

              // 🔑 执行 onSliderEnd 脚本（新的脚本系统）
              if (this.executeScript && typeof this.executeScript === 'function') {
                  try {
                      this.executeScript('onSliderEnd', this.value);
                      console.log(`🔄 UISlider: onSliderEnd 脚本执行完成`);
                  } catch (error) {
                      console.error('❌ UISlider: onSliderEnd 脚本执行失败', error);
                  }
              }

              // 🔑 执行事件代码（保持兼容性）
              this.executeEvent('onDragEnd');

              console.log('🎯 Slider: 拖拽结束');
          }

          /**
           * 设置值
           */
          setValue(value) {
              const oldValue = this.value;

              // 限制范围
              value = Math.max(this.minValue, Math.min(this.maxValue, value));

              // 应用步长
              if (this.step > 0) {
                  value = Math.round((value - this.minValue) / this.step) * this.step + this.minValue;
              }

              this.value = value;

              if (this.value !== oldValue) {
                  // 🔑 使用新的更新方法
                  this.updateSlider();

                  // 执行回调函数
                  if (this.onChange) {
                      this.onChange(this.value);
                  }

                  // 🔑 执行 onChange 脚本（新的脚本系统）
                  if (this.executeScript && typeof this.executeScript === 'function') {
                      try {
                          this.executeScript('onChange', this.value, oldValue);
                          console.log(`🔄 UISlider: onChange 脚本执行完成 ${oldValue} → ${this.value}`);
                      } catch (error) {
                          console.error('❌ UISlider: onChange 脚本执行失败', error);
                      }
                  }

                  // 🔑 执行事件代码（保持兼容性）
                  this.executeEvent('onChange');
              }
          }

          /**
           * 获取值
           */
          getValue() {
              return this.value;
          }

          /**
           * 更新按钮位置（旧方法，保持兼容性）
           */
          updateThumbPosition() {
              // 新设计中使用 updateThumb 方法
              this.updateThumb();
          }

          /**
           * 设置启用状态
           */
          setEnabled(enabled) {
              this.enabled = enabled;
              this.alpha = enabled ? 1.0 : 0.5;
              this.interactive = enabled;

              // 检查 thumbSprite 是否已经创建
              if (this.thumbSprite) {
                  this.thumbSprite.interactive = enabled;
              }
          }

          /**
           * 设置范围
           */
          setRange(minValue, maxValue) {
              this.minValue = minValue;
              this.maxValue = maxValue;
              this.setValue(this.value); // 重新验证当前值
          }

          /**
           * 设置图片路径
           */
          setTrackImage(imagePath) {
              this.trackImagePath = imagePath;
              if (this.bitmap && typeof ImageManager !== 'undefined') {
                  this.bitmap = ImageManager.loadBitmap('', imagePath);
              }
          }

          setProgressImage(imagePath) {
              this.progressImagePath = imagePath;
              if (this.progressSprite && typeof ImageManager !== 'undefined') {
                  this.progressSprite.bitmap = ImageManager.loadBitmap('', imagePath);
              }
          }

          setThumbImage(imagePath) {
              this.thumbImagePath = imagePath;
              if (this.thumbSprite && typeof ImageManager !== 'undefined') {
                  this.thumbSprite.bitmap = ImageManager.loadBitmap('', imagePath);
              }
          }



          /**
           * 获取所有属性（用于模型同步）
           */
          getProperties() {
              return {
                  // 基础属性
                  x: this.x,
                  y: this.y,
                  width: this.sliderWidth,
                  height: this.sliderHeight,

                  // 数值属性
                  value: this.value,
                  minValue: this.minValue,
                  maxValue: this.maxValue,
                  step: this.step,

                  // 图片路径
                  trackImagePath: this.trackImagePath,
                  progressImagePath: this.progressImagePath,
                  thumbImagePath: this.thumbImagePath,

                  // 外观属性
                  trackColor: this.trackColor,
                  fillColor: this.fillColor,
                  thumbColor: this.thumbColor,
                  thumbWidth: this.thumbWidth,
                  thumbHeight: this.thumbHeight,

                  // 🔑 文本功能已删除 - 文本由外部绑定对象提供

                  // 行为属性
                  enabled: this.enabled,

                  // 事件代码
                  _eventCodes: this._eventCodes
              };
          }

          /**
           * 执行指定类型的事件 - 参考 UIButton 实现
           */
          executeEvent(eventType) {
              // 从 _eventCodes 对象中获取事件代码
              const eventCode = this._eventCodes && this._eventCodes[eventType];
              if (!eventCode || typeof eventCode !== 'string') return;

              console.log('⚡ UISlider: 执行事件', eventType, eventCode);

              try {
                  // 创建函数并执行，this指向当前滑动条，同时传入当前值
                  const eventFunction = new Function('value', eventCode);
                  eventFunction.call(this, this.value);
              } catch (error) {
                  console.error(`UISlider ${eventType} 事件执行失败:`, error);
                  console.error('事件代码:', eventCode);
              }
          }

          /**
           * 克隆当前 UISlider 对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UISlider} 克隆的 UISlider 对象
           */
          clone(options = {}) {
              console.log('🔄 UISlider: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = {
                  // 基础属性
                  width: this.width,
                  height: this.height,
                  visible: this.visible,

                  // UISlider 特有属性
                  minValue: this.minValue,
                  maxValue: this.maxValue,
                  value: this.value,
                  step: this.step,
                  sliderWidth: this.sliderWidth,
                  sliderHeight: this.sliderHeight,

                  // 事件代码
                  _eventCodes: {
                      onChange: this._eventCodes?.onChange || ''
                  },

                  // 🔑 深度克隆脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : []
              };

              // 2. 创建克隆对象
              const clonedSlider = new UISlider(cloneProperties);

              // 3. 设置位置和变换属性
              clonedSlider.x = this.x + (offsetPosition ? offsetX : 0);
              clonedSlider.y = this.y + (offsetPosition ? offsetY : 0);
              clonedSlider.scale.x = this.scale.x;
              clonedSlider.scale.y = this.scale.y;
              clonedSlider.rotation = this.rotation;
              clonedSlider.alpha = this.alpha;
              clonedSlider.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedSlider.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              // 5. 重建绑定关系
              this.rebuildBindingsForClone(clonedSlider, clonedChildren);

              console.log('✅ UISlider: 克隆完成，包含', clonedChildren.length, '个子对象');
              return clonedSlider;
          }

          /**
           * 为克隆对象重建绑定关系
           * @param {UISlider} clonedSlider 克隆的滑块对象
           * @param {Array} clonedChildren 克隆的子对象数组
           */
          rebuildBindingsForClone(clonedSlider, clonedChildren) {
              console.log('🔗 UISlider: 重建克隆对象的绑定关系');

              // 找到原始绑定对象在子对象数组中的索引
              const findChildIndex = (boundObject) => {
                  if (!boundObject) return -1;
                  for (let i = 0; i < this.children.length; i++) {
                      if (this.children[i] === boundObject) {
                          return i;
                      }
                  }
                  return -1;
              };

              // 重新绑定范围精灵
              if (this.boundRangeSprite) {
                  const index = findChildIndex(this.boundRangeSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedSlider.bindRangeSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定范围精灵');
                  }
              }

              // 重新绑定滑块精灵
              if (this.boundThumbSprite) {
                  const index = findChildIndex(this.boundThumbSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedSlider.bindThumbSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定滑块精灵');
                  }
              }

              // 重新绑定文本精灵
              if (this.boundLabelSprite) {
                  const index = findChildIndex(this.boundLabelSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedSlider.bindLabelSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定文本精灵');
                  }
              }
          }

          /**
           * 销毁滑动条
           */
          destroy() {
              if (this.thumbSprite) {
                  this.thumbSprite.destroy();
              }
              if (this.progressSprite) {
                  this.progressSprite.destroy();
              }
              if (this.progressMask) {
                  this.progressMask.destroy();
              }
              super.destroy();
          }
      }

      // 将 UISlider 类添加到全局
      window.UISlider = UISlider;

      // 🔧 调试：检查方法是否正确定义
      console.log('🔧 调试：UISlider 类方法检查', {
          hasBindRangeSprite: typeof UISlider.prototype.bindRangeSprite,
          hasBindThumbSprite: typeof UISlider.prototype.bindThumbSprite,
          allMethods: Object.getOwnPropertyNames(UISlider.prototype).filter(name => typeof UISlider.prototype[name] === 'function')
      });

      console.log('UI Components Plugin loaded - UISlider class available');
       console.log('--------------------------------------UI Components Plugin loaded - Slider class available----------------------------------------');

  })();

  // ===== uiImage.js =====

      //=============================================================================
      // UIImage Class - 图片组件，参考UIButton实现
      //=============================================================================

      /**
       * 图片组件类 - 继承自 Sprite，参考UIButton的尺寸设置实现
       */
      class UIImage extends Sprite {
          constructor(properties = {}) {
              super();

              console.log('🖼️ UIImage: 创建图片组件', properties);

              // 标识为 UI 组件
              this.isUIComponent = true;
              this.uiComponentType = 'UIImage';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 使用UIScriptManager添加脚本功能
              if (window.UIScriptManager) {
                  window.UIScriptManager.applyToObject(this, properties);
                  console.log('🔧 UIImage: 脚本系统初始化完成', {
                      hasComponentScripts: !!this.componentScripts,
                      scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                      scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                  });
              }

              // 初始化图片组件
              this.initializeImage(properties);

              // 🔑 标记为已创建，onStart会在添加到父容器时执行
              this._isCreated = true;
          }

          /**
           * 初始化图片组件
           * @param {Object} properties 图片属性
           */
          initializeImage(properties) {
              // 基础属性
              this._width = properties.width || 100;
              this._height = properties.height || 100;
              // 🔑 统一使用 src 属性，兼容 imagePath
              this._src = properties.src || properties.imagePath || '';

              // 回调
              this.onLoadCallback = null;

              // 源图片bitmap
              this.sourceBitmap = null;

              // 🔑 缓存键，用于避免重复绘制
              this._lastRegionKey = null;

              // 裁切相关属性
              this.regions = properties.regions || [];
              this.currentRegionIndex = properties.currentRegionIndex || 0;
              this.gridRows = properties.gridRows || 1;
              this.gridCols = properties.gridCols || 1;

              // 智能裁切相关属性
              this.smartCropData = properties.smartCropData || null;

              // 🔑 九宫格相关属性
              this.enableNinePatch = properties.enableNinePatch || false;
              this.ninePatch = properties.ninePatch || { left: 10, top: 10, right: 10, bottom: 10 };

              // 创建bitmap并加载图片
              this.createBitmap();
              if (this._src) {
                  this.loadImage();
              }
          }

          /**
           * 获取宽度
           */
          get width() {
              return this._width;
          }

          /**
           * 设置宽度 - 触发重绘
           */
          set width(value) {
              if (this._width !== value) {
                  this._width = value;
                  // 🔑 只有在图片已加载且尺寸真正改变时才刷新，避免不必要的闪烁
                  if (this.sourceBitmap && this.sourceBitmap.isReady && this.sourceBitmap.isReady()) {
                      this.refresh();
                  }
                  console.log('📏 UIImage: 设置宽度', value);
              }
          }

          /**
           * 获取高度
           */
          get height() {
              return this._height;
          }

          /**
           * 设置高度 - 触发重绘
           */
          set height(value) {
              if (this._height !== value) {
                  this._height = value;
                  // 🔑 只有在图片已加载且尺寸真正改变时才刷新，避免不必要的闪烁
                  if (this.sourceBitmap && this.sourceBitmap.isReady && this.sourceBitmap.isReady()) {
                      this.refresh();
                  }
                  console.log('📏 UIImage: 设置高度', value);
              }
          }

          /**
           * 获取图片源路径
           */
          get src() {
              return this._src;
          }

          /**
           * 设置图片源路径 - 自动加载图片
           */
          set src(value) {
              if (this._src !== value) {
                  console.log('🖼️ UIImage: 设置图片源', {
                      old: this._src,
                      new: value
                  });

                  this._src = value;

                  // 自动加载新图片
                  if (value) {
                      this.loadImage();
                  } else {
                      // 如果设置为空，清除图片
                      this.clearImage();
                  }
              }
          }

          /**
           * 🔑 兼容性：imagePath 属性（映射到 src）
           */
          get imagePath() {
              return this._src;
          }

          set imagePath(value) {
              this.src = value;
          }

          /**
           * 创建bitmap - 参考UIButton实现
           */
          createBitmap() {
              this.bitmap = new Bitmap(this.width, this.height);
              console.log('🎨 UIImage: 创建bitmap', this.width, 'x', this.height);
          }

          /**
           * 加载图片 - 参考UIButton的图片加载
           */
          loadImage() {
              if (typeof ImageManager !== 'undefined' && this._src) {
                  console.log('📥 UIImage: 加载图片', this._src);
                  this.sourceBitmap = ImageManager.loadBitmapFromUrl(this._src);
                  this.sourceBitmap.addLoadListener(() => {
                      this.onImageLoaded();
                  });
              }
          }

          /**
           * 图片加载完成回调
           */
          onImageLoaded() {
              console.log('✅ UIImage: 图片加载完成', this.sourceBitmap.width, 'x', this.sourceBitmap.height);

              const originalWidth = this.sourceBitmap.width;
              const originalHeight = this.sourceBitmap.height;

              // 🔧 区分不同情况的尺寸处理
              if (this.regions.length === 0) {
                  // 情况1: 没有预设区域 - 自动设置为图片原始尺寸（新建或更换图片）
                  console.log('📏 UIImage: 没有预设区域，设置为图片原始尺寸', {
                      old: `${this._width}x${this._height}`,
                      new: `${originalWidth}x${originalHeight}`
                  });

                  this._width = originalWidth;
                  this._height = originalHeight;

                  // 创建默认区域（整张图片）
                  this.createDefaultRegion();

                  // 通知ImageModel尺寸变化
                  if (this.onLoadCallback) {
                      this.onLoadCallback(originalWidth, originalHeight, 1, 1);
                  }
              } else {
                  // 情况2: 有预设区域 - 保持当前尺寸（Clone或已有裁切）
                  console.log('📏 UIImage: 有预设区域，保持当前尺寸', {
                      currentSize: `${this._width}x${this._height}`,
                      imageSize: `${originalWidth}x${originalHeight}`,
                      regions: this.regions.length
                  });

                  // 不改变尺寸，只通知ImageModel图片已加载
                  if (this.onLoadCallback) {
                      this.onLoadCallback(this._width, this._height, 1, 1);
                  }
              }

              // 刷新显示 - 参考UIButton
              this.refresh();
          }

          /**
           * 创建默认区域（整张图片）
           */
          createDefaultRegion() {
              if (this.sourceBitmap) {
                  this.regions = [{
                      sx: 0,
                      sy: 0,
                      sw: this.sourceBitmap.width,
                      sh: this.sourceBitmap.height
                  }];
                  this.currentRegionIndex = 0;
                  console.log('🔧 UIImage: 创建默认区域', this.regions[0]);
              }
          }

          /**
           * 刷新图片显示 - 参考UIButton的refresh实现
           */
          refresh() {
              if (!this.bitmap) {
                  console.log('⚠️ UIImage: bitmap不存在，重新创建');
                  this.createBitmap();
              }

              if (!this.sourceBitmap) {
                  console.log('⚠️ UIImage: 源图片不存在');
                  return;
              }

              if (!this.sourceBitmap.isReady || !this.sourceBitmap.isReady()) {
                  console.log('⚠️ UIImage: 图片未准备好，等待加载');
                  return;
              }

              // 🔑 检查bitmap尺寸是否需要重新创建
              const needsResize = !this.bitmap ||
                                 this.bitmap.width !== this.width ||
                                 this.bitmap.height !== this.height;

              if (needsResize) {
                  // 只有在尺寸变化时才清空并重新创建
                  if (this.bitmap) {
                      this.bitmap.clear();
                  }
                  this.bitmap = new Bitmap(this.width, this.height);
                  console.log('🔧 UIImage: 重新创建bitmap', this.width, 'x', this.height);
              }

              console.log('🎨 UIImage: 刷新显示', this.width, 'x', this.height);
              this.setImageBitmap(this.sourceBitmap);
          }

          /**
           * 设置图片bitmap - 支持裁切功能
           */
          setImageBitmap(sourceBitmap) {
              if (!sourceBitmap || !sourceBitmap.canvas) {
                  console.warn('UIImage: 无效的sourceBitmap');
                  return;
              }

              // 确保bitmap存在且尺寸正确
              if (!this.bitmap || this.bitmap.width !== this.width || this.bitmap.height !== this.height) {
                  this.bitmap = new Bitmap(this.width, this.height);
              }

              // 获取当前区域信息
              const region = this.getCurrentRegion();
              if (!region) {
                  console.warn('UIImage: 没有可用的区域');
                  return;
              }

              // 🔑 检查是否需要重新绘制（避免重复绘制相同内容）
              // 包含图片源信息，确保更换图片时能正确重绘
              const imageSource = sourceBitmap.url || sourceBitmap._url || this._src || 'unknown';
              const ninePatchKey = this.enableNinePatch ? `_np_${this.ninePatch.left}_${this.ninePatch.top}_${this.ninePatch.right}_${this.ninePatch.bottom}` : '';
              const currentRegionKey = `${imageSource}_${region.sx}_${region.sy}_${region.sw}_${region.sh}_${this.width}_${this.height}${ninePatchKey}`;

              if (this._lastRegionKey === currentRegionKey && this.bitmap._loadingState === 'loaded') {
                  console.log('🔘 UIImage: 内容未变化，跳过重绘');
                  return;
              }
              this._lastRegionKey = currentRegionKey;

              // 清除bitmap内容（只在需要重绘时清除）
              this.bitmap.clear();

              // 使用高质量的Canvas绘制 - 支持裁切和九宫格
              const context = this.bitmap.canvas.getContext('2d');
              if (context) {
                  // 设置高质量缩放
                  context.imageSmoothingEnabled = true;
                  context.imageSmoothingQuality = 'high';

                  // 🔑 根据是否启用九宫格选择绘制方式
                  if (this.enableNinePatch) {
                      this.drawNinePatch(context, sourceBitmap, region);
                  } else {
                      // 标准绘制：绘制指定区域到目标尺寸
                      context.drawImage(
                          sourceBitmap.canvas,
                          region.sx, region.sy, region.sw, region.sh,  // 源区域
                          0, 0, this.width, this.height                // 目标尺寸
                      );
                  }

                  // 标记bitmap已更新 - 使用RPG Maker MZ方法
                  if (this.bitmap._baseTexture && this.bitmap._baseTexture.update) {
                      this.bitmap._baseTexture.update();
                  } else if (this.bitmap._onLoad) {
                      this.bitmap._onLoad();
                  }
              }

              // 🔧 设置bitmap的url属性，让属性面板能够检测到
              if (this.sourceBitmap && this.sourceBitmap.url) {
                  // 使用Object.defineProperty来设置只读属性
                  Object.defineProperty(this.bitmap, 'url', {
                      value: this.sourceBitmap.url,
                      writable: false,
                      enumerable: true,
                      configurable: true
                  });
                  this.bitmap._loadingState = 'loaded';
              }

              console.log('🎨 UIImage: 设置图片', `区域[${region.sx},${region.sy},${region.sw},${region.sh}]`, '->', this.width, 'x', this.height);
          }

          /**
           * 🔑 九宫格绘制方法
           * @param {CanvasRenderingContext2D} context 绘制上下文
           * @param {Bitmap} sourceBitmap 源图片
           * @param {Object} region 当前区域
           */
          drawNinePatch(context, sourceBitmap, region) {
              const { sx, sy, sw, sh } = region;
              const { left, top, right, bottom } = this.ninePatch;
              const targetWidth = this.width;
              const targetHeight = this.height;

              console.log('🔲 UIImage: 九宫格绘制', {
                  region: `${sx},${sy},${sw},${sh}`,
                  ninePatch: this.ninePatch,
                  target: `${targetWidth}x${targetHeight}`
              });

              // 🔧 确保九宫格参数不超过区域尺寸
              const maxLeft = Math.min(left, sw - right - 1);
              const maxTop = Math.min(top, sh - bottom - 1);
              const maxRight = Math.min(right, sw - maxLeft - 1);
              const maxBottom = Math.min(bottom, sh - maxTop - 1);

              // 计算中间区域尺寸
              const centerWidth = sw - maxLeft - maxRight;
              const centerHeight = sh - maxTop - maxBottom;
              const targetCenterWidth = targetWidth - maxLeft - maxRight;
              const targetCenterHeight = targetHeight - maxTop - maxBottom;

              // 🎨 绘制九个区域

              // 1. 左上角 (固定尺寸)
              if (maxLeft > 0 && maxTop > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx, sy, maxLeft, maxTop,
                      0, 0, maxLeft, maxTop);
              }

              // 2. 上边 (水平拉伸)
              if (centerWidth > 0 && maxTop > 0 && targetCenterWidth > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx + maxLeft, sy, centerWidth, maxTop,
                      maxLeft, 0, targetCenterWidth, maxTop);
              }

              // 3. 右上角 (固定尺寸)
              if (maxRight > 0 && maxTop > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx + sw - maxRight, sy, maxRight, maxTop,
                      targetWidth - maxRight, 0, maxRight, maxTop);
              }

              // 4. 左边 (垂直拉伸)
              if (maxLeft > 0 && centerHeight > 0 && targetCenterHeight > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx, sy + maxTop, maxLeft, centerHeight,
                      0, maxTop, maxLeft, targetCenterHeight);
              }

              // 5. 中心 (双向拉伸)
              if (centerWidth > 0 && centerHeight > 0 && targetCenterWidth > 0 && targetCenterHeight > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx + maxLeft, sy + maxTop, centerWidth, centerHeight,
                      maxLeft, maxTop, targetCenterWidth, targetCenterHeight);
              }

              // 6. 右边 (垂直拉伸)
              if (maxRight > 0 && centerHeight > 0 && targetCenterHeight > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx + sw - maxRight, sy + maxTop, maxRight, centerHeight,
                      targetWidth - maxRight, maxTop, maxRight, targetCenterHeight);
              }

              // 7. 左下角 (固定尺寸)
              if (maxLeft > 0 && maxBottom > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx, sy + sh - maxBottom, maxLeft, maxBottom,
                      0, targetHeight - maxBottom, maxLeft, maxBottom);
              }

              // 8. 下边 (水平拉伸)
              if (centerWidth > 0 && maxBottom > 0 && targetCenterWidth > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx + maxLeft, sy + sh - maxBottom, centerWidth, maxBottom,
                      maxLeft, targetHeight - maxBottom, targetCenterWidth, maxBottom);
              }

              // 9. 右下角 (固定尺寸)
              if (maxRight > 0 && maxBottom > 0) {
                  context.drawImage(sourceBitmap.canvas,
                      sx + sw - maxRight, sy + sh - maxBottom, maxRight, maxBottom,
                      targetWidth - maxRight, targetHeight - maxBottom, maxRight, maxBottom);
              }
          }

          /**
           * 更新显示尺寸 - 完全参考UIButton的updateDisplaySize实现
           */
          updateDisplaySize(newWidth, newHeight) {
              if (this._width !== newWidth || this._height !== newHeight) {
                  this._width = newWidth;
                  this._height = newHeight;

                  // 重新绘制当前状态 - 参考UIButton
                  this.refresh();

                  console.log('📏 UIImage: 更新显示尺寸', newWidth, 'x', newHeight);
              }
          }

          /**
           * 设置尺寸 - 参考UIButton实现
           */
          setSize(width, height) {
              this.updateDisplaySize(width, height);
          }

          /**
           * 设置显示尺寸 - 兼容方法
           */
          setDisplaySize(width, height) {
              this.updateDisplaySize(width, height);
          }

          /**
           * 设置图片路径（兼容方法，推荐使用 src 属性）
           */
          setImagePath(path) {
              this.src = path;
          }

          /**
           * 🔑 设置九宫格启用状态
           * @param {boolean} enabled 是否启用九宫格
           */
          setNinePatchEnabled(enabled) {
              if (this.enableNinePatch !== enabled) {
                  this.enableNinePatch = enabled;
                  this.refresh(); // 重新绘制
                  console.log('🔲 UIImage: 九宫格状态', enabled ? '启用' : '禁用');
              }
          }

          /**
           * 🔑 设置九宫格参数
           * @param {Object} ninePatch 九宫格参数 {left, top, right, bottom}
           */
          setNinePatch(ninePatch) {
              if (ninePatch && typeof ninePatch === 'object') {
                  this.ninePatch = {
                      left: ninePatch.left || 0,
                      top: ninePatch.top || 0,
                      right: ninePatch.right || 0,
                      bottom: ninePatch.bottom || 0
                  };

                  // 如果九宫格已启用，重新绘制
                  if (this.enableNinePatch) {
                      this.refresh();
                  }

                  console.log('🔲 UIImage: 设置九宫格参数', this.ninePatch);
              }
          }

          /**
           * 🔑 获取九宫格参数
           * @returns {Object} 九宫格参数
           */
          getNinePatch() {
              return { ...this.ninePatch };
          }

          /**
           * 清除图片
           */
          clearImage() {
              console.log('🗑️ UIImage: 清除图片');

              // 清除源图片
              this.sourceBitmap = null;

              // 清除当前显示的图片
              if (this.bitmap) {
                  this.bitmap.clear();
              }

              // 重置尺寸为默认值
              this._width = 100;
              this._height = 100;

              // 刷新显示
              this.refresh();
          }

          /**
           * 设置角色 Face 图片
           * @param {string} faceName Face 文件名（不含扩展名）
           * @param {number} faceIndex Face 索引 (0-7)
           */
          setFace(faceName, faceIndex) {
              console.log('👤 UIImage: 设置 Face 图片', {
                  faceName,
                  faceIndex
              });

              if (!faceName) {
                  console.warn('👤 UIImage: faceName 不能为空');
                  return;
              }

              // 设置 face 图片路径
              this.src = `img/faces/${faceName}.png`;

              // 设置 face 裁切区域
              this.setFaceRegion(faceIndex || 0);
          }

          /**
           * 设置 Face 裁切区域
           * @param {number} faceIndex Face 索引 (0-7)
           */
          setFaceRegion(faceIndex) {
              const faceWidth = 144;   // RPG Maker MZ 标准 face 宽度
              const faceHeight = 144;  // RPG Maker MZ 标准 face 高度

              // 计算在 face 图片中的位置 (4列×2行布局)
              const col = faceIndex % 4;
              const row = Math.floor(faceIndex / 4);

              const region = {
                  sx: col * faceWidth,
                  sy: row * faceHeight,
                  sw: faceWidth,
                  sh: faceHeight,
                  label: `Face ${faceIndex}`
              };

              console.log('👤 UIImage: 设置 Face 区域', {
                  faceIndex,
                  col,
                  row,
                  region
              });

              // 设置为单区域模式，显示指定的 face
              this.regions = [region];
              this.currentRegionIndex = 0;

              // 如果图片已加载，立即刷新显示
              if (this.sourceBitmap) {
                  this.refresh();
              }
          }

          /**
           * 设置加载完成回调
           */
          setOnLoadCallback(callback) {
              this.onLoadCallback = callback;
          }

          // ==================== 裁切功能 ====================

          /**
           * 获取当前区域信息
           */
          getCurrentRegion() {
              return this.regions[this.currentRegionIndex] || null;
          }

          /**
           * 设置当前显示区域
           */
          setCurrentRegion(index) {
              if (index >= 0 && index < this.regions.length) {
                  this.currentRegionIndex = index;
                  this.refresh(); // 重新绘制
                  console.log('🔧 UIImage: 切换到区域', index, this.regions[index]);
                  return true;
              }
              return false;
          }

          /**
           * 生成网格区域
           */
          generateGridRegions(rows, cols) {
              if (!this.sourceBitmap) {
                  console.warn('🖼️ UIImage: 图片未加载，无法生成网格区域');
                  return;
              }

              this.gridRows = rows;
              this.gridCols = cols;
              this.regions = [];

              const cellWidth = Math.floor(this.sourceBitmap.width / cols);
              const cellHeight = Math.floor(this.sourceBitmap.height / rows);

              for (let row = 0; row < rows; row++) {
                  for (let col = 0; col < cols; col++) {
                      this.regions.push({
                          sx: col * cellWidth,
                          sy: row * cellHeight,
                          sw: cellWidth,
                          sh: cellHeight
                      });
                  }
              }

              this.currentRegionIndex = 0;
              console.log('🔧 UIImage: 生成网格区域', `${rows}x${cols}`, '共', this.regions.length, '个区域');

              // 重新绘制
              this.refresh();
          }

          /**
           * 添加自定义区域
           */
          addRegion(sx, sy, sw, sh) {
              this.regions.push({ sx, sy, sw, sh });
              console.log('🔧 UIImage: 添加区域', { sx, sy, sw, sh });
          }

          /**
           * 重置为默认区域（完整图片）
           */
          resetToDefaultRegion() {
              if (!this.sourceBitmap) {
                  console.warn('🖼️ UIImage: 图片未加载，无法重置区域');
                  return;
              }

              // 重置网格设置
              this.gridRows = 1;
              this.gridCols = 1;

              // 🔧 重置尺寸为图片原始尺寸
              const originalWidth = this.sourceBitmap.width;
              const originalHeight = this.sourceBitmap.height;

              console.log('📏 UIImage: 重置尺寸为图片原始尺寸', {
                  old: `${this._width}x${this._height}`,
                  new: `${originalWidth}x${originalHeight}`
              });

              this._width = originalWidth;
              this._height = originalHeight;

              // 创建默认区域（完整图片）
              this.regions = [{
                  sx: 0,
                  sy: 0,
                  sw: originalWidth,
                  sh: originalHeight
              }];
              this.currentRegionIndex = 0;

              // 重新绘制
              this.refresh();

              // 🔧 通知ImageModel尺寸已重置
              if (this.onLoadCallback) {
                  this.onLoadCallback(originalWidth, originalHeight, 1, 1);
              }

              console.log('🔄 UIImage: 重置为默认区域', {
                  size: `${originalWidth}x${originalHeight}`,
                  regions: this.regions.length,
                  gridSize: `${this.gridRows}x${this.gridCols}`
              });
          }

          /**
           * 是否为多区域裁切
           */
          isMultiRegion() {
              return this.regions.length > 1;
          }

          /**
           * 获取属性
           */
          getProperties() {
              return {
                  // 基础属性
                  x: this.x,
                  y: this.y,
                  width: this.width,
                  height: this.height,
                  // 图片属性
                  src: this._src,
                  // 裁切属性
                  regions: this.regions,
                  currentRegionIndex: this.currentRegionIndex,
                  gridRows: this.gridRows,
                  gridCols: this.gridCols,
                  // 智能裁切属性
                  smartCropData: this.smartCropData,

                  // 🔑 九宫格属性
                  enableNinePatch: this.enableNinePatch,
                  ninePatch: this.ninePatch
              };
          }

          /**
           * 每帧更新
           */
          update() {
              super.update();

              // 🔑 执行更新脚本
              if (this.executeScript) {
                  this.executeScript('onUpdate');
              }
          }

          /**
           * 销毁图片组件
           */
          destroy() {
              // 🔑 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true; // 防止重复销毁
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UIImage: 销毁脚本执行失败', error);
              }

              // 清理资源
              if (this.sourceBitmap) {
                  this.sourceBitmap.destroy();
                  this.sourceBitmap = null;
              }

              super.destroy();
          }

          /**
           * 克隆当前 UIImage 对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UIImage} 克隆的 UIImage 对象
           */
          clone(options = {}) {
              console.log('🔄 UIImage: 开始克隆对象');

              const {
                  offsetPosition = false,
                  offsetX = 0,
                  offsetY = 0
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = {
                  // 基础属性
                  width: this.width,
                  height: this.height,
                  visible: this.visible,

                  // 🔑 UIComponent 属性（安全访问）
                  name: this.name || '',
                  enabled: this.enabled !== false,

                  // 🔑 深度克隆组件脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : [],

                  // UIImage 特有属性
                  src: this._src,
                  scaleMode: this.scaleMode,
                  preserveAspectRatio: this.preserveAspectRatio,
                  regions: JSON.parse(JSON.stringify(this.regions)), // 深拷贝区域数据
                  currentRegionIndex: this.currentRegionIndex,
                  gridRows: this.gridRows,
                  gridCols: this.gridCols,
                  // 深拷贝智能裁切数据
                  smartCropData: this.smartCropData ? JSON.parse(JSON.stringify(this.smartCropData)) : null,

                  // 🔑 九宫格属性
                  enableNinePatch: this.enableNinePatch,
                  ninePatch: { ...this.ninePatch } // 深拷贝九宫格参数
              };

              // 2. 创建克隆对象
              const clonedImage = new UIImage(cloneProperties);

              // 🔧 调试：验证脚本是否被克隆
              // console.log('🔄 UIImage: 克隆验证', {
              //     原始组件名称: this.name,
              //     克隆组件名称: clonedImage.name,
              //     原始脚本数量: this.componentScripts ? this.componentScripts.length : 0,
              //     克隆脚本数量: clonedImage.componentScripts ? clonedImage.componentScripts.length : 0,
              //     原始脚本详情: this.componentScripts ? this.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : [],
              //     克隆脚本详情: clonedImage.componentScripts ? clonedImage.componentScripts.map(s => `${s.name}(${s.enabled ? '启用' : '禁用'})`) : []
              // });

              // 3. 设置位置和变换属性
              clonedImage.x = this.x + (offsetPosition ? offsetX : 0);
              clonedImage.y = this.y + (offsetPosition ? offsetY : 0);
              clonedImage.scale.x = this.scale.x;
              clonedImage.scale.y = this.scale.y;
              clonedImage.rotation = this.rotation;
              clonedImage.alpha = this.alpha;
              clonedImage.anchor.x = this.anchor.x;
              clonedImage.anchor.y = this.anchor.y;
              clonedImage.pivot.x = this.pivot.x;
              clonedImage.pivot.y = this.pivot.y;
              clonedImage.skew.x = this.skew.x;
              clonedImage.skew.y = this.skew.y;
              clonedImage.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedImage.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              console.log('✅ UIImage: 克隆完成，包含', clonedChildren.length, '个子对象');
              return clonedImage;
          }
      }

      // 导出到全局
      window.UIImage = UIImage;


  // ===== switch.js =====
  //=============================================================================
  // switch.js
  //=============================================================================

  (() => {
// 确保PIXI可用
      if (typeof PIXI === 'undefined') {
          console.error('🔘 UISwitch: PIXI未定义，插件加载失败');
          return;
      }

      /**
       * UISwitch类 - 开关组件
       * 继承自Container，管理背景轨道和滑块按钮
       * 通过属性面板绑定背景精灵和滑块精灵
       * 只负责开/关状态逻辑，文本由用户自定义
       */
      class UISwitch extends PIXI.Container {
          constructor(properties = {}) {
              super();

              // 标识为 UI 组件
              this.isUIComponent = true;
              this.uiComponentType = 'Switch';

              this.initializeSwitch(properties);
          }

          /**
           * 初始化开关
           * @param {Object} properties 开关属性
           */
          initializeSwitch(properties) {
              // 设置默认属性
              this.setupDefaultProperties(properties);

              // 初始化容器
              this.initializeContainer();

              // 绑定事件
              this.setupEventListeners();

              // 初始更新
              this.updateSwitch();

              console.log('🔘 UISwitch: 开关创建完成', {
                  isOn: this.isOn,
                  animationDuration: this.animationDuration
              });
          }

          /**
           * 设置默认属性
           */
          setupDefaultProperties(properties) {
              // 状态属性
              this.isOn = Boolean(properties.isOn || false);
              this.value = this.isOn; // 🔑 添加 value 属性，与 isOn 同步
              this.enabled = properties.enabled !== false;

              // 🔑 核心绑定组件（仅需2个）
              this.boundBackgroundSprite = properties.boundBackgroundSprite || null; // 背景轨道 (UIImage)
              this.boundKnobSprite = properties.boundKnobSprite || null;             // 滑块按钮 (UIImage)

              // 动画属性
              this.animationDuration = properties.animationDuration || 200; // 切换动画时长(ms)
              this.isAnimating = false;

              // 尺寸属性（由背景轨道决定）
              this.switchWidth = properties.width || 60;
              this.switchHeight = properties.height || 30;

              // 回调函数
              this.onChange = properties.onChange || null;
              this.onToggle = properties.onToggle || null;

              // 事件代码属性（用于代码生成）
              this._eventCodes = properties._eventCodes || {};

              // 🔑 统一的脚本系统
              this.componentScripts = properties.componentScripts || [];

              // 🔑 事件执行控制（默认禁用，可在属性面板中启用用于调试）
              this.executeEventsInEditor = properties.executeEventsInEditor || false;

              // 🔑 初始化脚本管理器
              this.initializeScriptManager();

              console.log('🔘 UISwitch: 属性初始化完成', {
                  isOn: this.isOn,
                  enabled: this.enabled,
                  size: `${this.switchWidth}x${this.switchHeight}`
              });
          }

          /**
           * 🔑 初始化脚本管理器
           */
          initializeScriptManager() {
              if (typeof window.UIScriptManager !== 'undefined') {
                  // 使用 UIScriptManager 为 UISwitch 添加脚本功能
                  window.UIScriptManager.applyToObject(this, { componentScripts: this.componentScripts });
                  console.log('✅ UISwitch: 脚本管理器初始化完成');
              } else {
                  console.warn('⚠️ UISwitch: UIScriptManager 不可用');
              }
          }

          /**
           * 初始化容器
           */
          initializeContainer() {
              // 容器本身不需要交互，交互由背景轨道精灵处理
              this.interactive = false;
              this.interactiveChildren = true; // 允许子组件交互

              // 设置容器的基础尺寸
              this.containerWidth = this.switchWidth;
              this.containerHeight = this.switchHeight;

              console.log('✅ UISwitch: 容器初始化完成', {
                  interactive: this.interactive,
                  interactiveChildren: this.interactiveChildren,
                  size: `${this.containerWidth}x${this.containerHeight}`
              });
          }

          /**
           * 设置事件监听器 - 使用RPG Maker MZ的TouchInput系统
           */
          setupEventListeners() {
              // 初始化状态
              this._pressed = false;
              this._wasPressed = false;

              // 🔑 使用 RPG Maker MZ 的循环事件系统（参考 UIButton）
              this.setupRPGMakerEventLoop();

              console.log('🔘 UISwitch: 事件监听器设置完成 - 使用tick系统');
          }

          /**
           * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIButton）
           */
          setupRPGMakerEventLoop() {
              // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
              if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  this._tickerCallback = () => this.processSwitchTouch();
                  Graphics.app.ticker.add(this._tickerCallback);
                  console.log('✅ UISwitch: 已注册到 PIXI ticker 循环');
                  return;
              }

              console.warn('⚠️ UISwitch: 无法注册到 RPG Maker MZ 循环');
          }

          /**
           * 处理开关触摸事件 - 使用RPG Maker MZ的TouchInput系统
           */
          processSwitchTouch() {
              if (!this.enabled || !this.visible) return;

              if (typeof TouchInput === 'undefined') return;

              // 🔑 在编辑器模式下且未启用事件执行时，不处理鼠标事件
              if (window.EDITOR_MODE && !this.executeEventsInEditor) {
                  return;
              }

              const isCurrentlyTouched = this.isBeingTouched();

              // 处理点击事件 - 只在刚按下时触发
              if (isCurrentlyTouched && TouchInput.isTriggered()) {
                  this.onSwitchPress();
              }

              // 处理释放事件
              if (this._pressed && TouchInput.isReleased()) {
                  this.onSwitchRelease();
              }
          }

          /**
           * 检查是否被触摸
           */
          isBeingTouched() {
              if (typeof TouchInput === 'undefined') return false;

              // 获取开关在屏幕上的位置
              const bounds = this.getBounds ? this.getBounds() : {
                  x: this.x,
                  y: this.y,
                  width: this.switchWidth,
                  height: this.switchHeight
              };
              const touchX = TouchInput.x;
              const touchY = TouchInput.y;

              return touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                     touchY >= bounds.y && touchY <= bounds.y + bounds.height;
          }

          /**
           * 碰撞检测
           */
          hitTest(x, y) {
              return x >= 0 && x <= this.switchWidth && y >= 0 && y <= this.switchHeight;
          }

          /**
           * 开关按下事件
           */
          onSwitchPress() {
              if (!this.enabled || this.isAnimating) {
                  console.log('🔘 UISwitch: 开关被禁用或正在动画中，忽略按下');
                  return;
              }

              console.log('🔘 UISwitch: 开关按下');
              this._pressed = true;
          }

          /**
           * 开关释放事件
           */
          onSwitchRelease() {
              if (!this.enabled) return;

              console.log('🔘 UISwitch: 开关释放');

              if (this._pressed) {
                  // 处理点击事件
                  this.handleSwitchClick();
              }

              this._pressed = false;
          }

          /**
           * 处理开关点击
           */
          handleSwitchClick() {
              console.log('🔘 UISwitch: 开关被点击');

              // 🔑 执行 onClick 脚本（新的脚本系统）
              if (this.executeScript && typeof this.executeScript === 'function') {
                  try {
                      this.executeScript('onClick', this.isOn);
                      console.log(`🔄 UISwitch: onClick 脚本执行完成`);
                  } catch (error) {
                      console.error('❌ UISwitch: onClick 脚本执行失败', error);
                  }
              }

              // 切换状态
              this.toggle();

              // 播放音效
              if (typeof SoundManager !== 'undefined') {
                  SoundManager.playCursor();
              }
          }

          /**
           * 🔑 核心方法：更新开关状态
           */
          updateSwitch() {
              // 🔑 执行动画（使用属性面板设置的坐标）
              this.updateKnobPosition();

              // 🔑 更新背景状态
              if (this.boundBackgroundSprite) {
                  requestAnimationFrame(() => {
                      this.updateBackground();
                  });
              }
          }

          /**
           * 🎯 纯动画方法：直接修改绑定对象坐标实现动画
           */
          updateKnobPosition() {
              if (!this.boundKnobSprite) return;

              // 🔑 获取当前坐标作为起点
              const currentX = this.boundKnobSprite.x;
              const currentY = this.boundKnobSprite.y;

              // 🔑 计算目标坐标：根据背景和滑块尺寸
              if (!this.boundBackgroundSprite) return;

              const trackWidth = this.boundBackgroundSprite.width || 100;
              const knobWidth = this.boundKnobSprite.width || 20;
              const trackHeight = this.boundBackgroundSprite.height || 40;
              const knobHeight = this.boundKnobSprite.height || 20;

              const targetX = this.isOn ? Math.max(0, trackWidth - knobWidth) : 0;
              const targetY = (trackHeight - knobHeight) / 2;

              // 🔑 执行动画：直接修改绑定对象坐标
              if (this.animationDuration > 0 && !this.isAnimating) {
                  this.animateKnobTo(targetX, targetY);
              } else {
                  // 直接设置到目标位置
                  this.boundKnobSprite.x = targetX;
                  this.boundKnobSprite.y = targetY;
              }

              console.log('🔘 UISwitch: 滑块动画', {
                  isOn: this.isOn,
                  from: `(${currentX}, ${currentY})`,
                  to: `(${targetX}, ${targetY})`
              });
          }

          /**
           * 更新背景状态（可选，用于状态相关的视觉变化）
           */
          updateBackground() {
              if (!this.boundBackgroundSprite) return;

              // 🔑 避免不必要的alpha变化，减少闪烁
              const targetAlpha = this.enabled ? 1.0 : 0.5;
              if (Math.abs(this.boundBackgroundSprite.alpha - targetAlpha) > 0.01) {
                  this.boundBackgroundSprite.alpha = targetAlpha;

                  console.log('🔘 UISwitch: 背景状态更新', {
                      isOn: this.isOn,
                      alpha: this.boundBackgroundSprite.alpha
                  });
              }
          }

          /**
           * 滑块动画
           */
          animateKnobTo(targetX, targetY) {
              if (!this.boundKnobSprite) return;

              this.isAnimating = true;
              const startX = this.boundKnobSprite.x;
              const startY = this.boundKnobSprite.y;
              const deltaX = targetX - startX;
              const deltaY = targetY - startY;
              const startTime = Date.now();

              console.log('🎬 UISwitch: 开始滑块动画', {
                  from: `(${startX}, ${startY})`,
                  to: `(${targetX}, ${targetY})`,
                  duration: this.animationDuration
              });

              const animate = () => {
                  const elapsed = Date.now() - startTime;
                  const progress = Math.min(elapsed / this.animationDuration, 1);

                  // 使用缓动函数
                  const easeProgress = this.easeInOutCubic(progress);
                  this.boundKnobSprite.x = startX + deltaX * easeProgress;
                  this.boundKnobSprite.y = startY + deltaY * easeProgress;

                  if (progress < 1) {
                      requestAnimationFrame(animate);
                  } else {
                      this.isAnimating = false;
                      console.log('✅ UISwitch: 滑块动画完成');
                  }
              };

              animate();
          }

          /**
           * 缓动函数：三次贝塞尔曲线
           */
          easeInOutCubic(t) {
              return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
          }



          /**
           * 🔑 设置开关状态
           */
          setValue(isOn) {
              const oldValue = this.isOn;
              this.isOn = Boolean(isOn);
              this.value = this.isOn; // 🔑 同步 value 属性

              console.log('🔘 UISwitch: 设置状态', {
                  oldValue: oldValue,
                  newValue: this.isOn,
                  changed: this.isOn !== oldValue
              });

              if (this.isOn !== oldValue) {
                  this.updateSwitch();

                  // 🔑 执行 onChange 脚本（新的脚本系统）
                  if (this.executeScript && typeof this.executeScript === 'function') {
                      try {
                          this.executeScript('onChange', this.isOn, oldValue);
                          console.log(`🔄 UISwitch: onChange 脚本执行完成 ${oldValue} → ${this.isOn}`);
                      } catch (error) {
                          console.error('❌ UISwitch: onChange 脚本执行失败', error);
                      }
                  }

                  // 执行回调函数
                  if (this.onChange) {
                      this.onChange(this.isOn, oldValue);
                  }

                  // 🔑 执行事件代码（保持兼容性）
                  this.executeEvent('onChangeCode');
                  this.executeEvent('onToggleCode');
              }
          }

          /**
           * 🔑 切换开关状态
           */
          toggle() {
              console.log('🔘 UISwitch: 切换状态', { 当前状态: this.isOn });
              this.setValue(!this.isOn);
          }

          /**
           * 获取开关状态
           */
          getValue() {
              return this.isOn;
          }

          /**
           * 执行指定类型的事件 - 参考 UIButton 实现
           */
          executeEvent(eventType) {
              const eventCode = this[eventType];
              if (!eventCode || typeof eventCode !== 'string') return;

              console.log('⚡ UISwitch: 执行事件', eventType, eventCode);

              try {
                  // 创建函数并执行，this指向当前开关，同时传入当前状态
                  const eventFunction = new Function('isOn', eventCode);
                  eventFunction.call(this, this.isOn);
              } catch (error) {
                  console.error(`UISwitch ${eventType} 事件执行失败:`, error);
                  console.error('事件代码:', eventCode);
              }
          }

          // ==================== 🔑 组件绑定方法 ====================

          /**
           * 检查是否为 UIImage 类型
           */
          isUIImageType(sprite) {
              return sprite && (
                  sprite.constructor.name === 'UIImage' ||
                  (sprite.isUIComponent && sprite.uiComponentType === 'UIImage')
              );
          }

          /**
           * 🔑 检查对象是否是当前UISwitch的子集
           */
          isChildOfThisSwitch(obj) {
              if (!obj) return false;
              return this.children.includes(obj);
          }

          /**
           * 🔑 检查对象是否可以绑定到当前UISwitch
           */
          canBindToThisSwitch(obj) {
              if (!this.isUIImageType(obj)) {
                  console.warn('🚨 UISwitch: 绑定对象必须是 UIImage 类型', {
                      provided: obj?.constructor?.name || 'unknown',
                      expected: 'UIImage'
                  });
                  return false;
              }

              if (!this.isChildOfThisSwitch(obj)) {
                  console.warn('🚨 UISwitch: 绑定对象必须是当前UISwitch的子对象', {
                      objectName: obj.name || 'unnamed',
                      switchName: this.name || 'unnamed',
                      isChild: false,
                      hint: '请先将UIImage拖拽到UISwitch下作为子对象，然后再进行绑定'
                  });
                  return false;
              }

              return true;
          }

          /**
           * 绑定背景轨道精灵
           */
          bindBackgroundSprite(sprite) {
              // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
              if (this.boundBackgroundSprite === sprite) {
                  console.log('🔍 UISwitch: 背景轨道已绑定，跳过重复绑定');
                  return;
              }

              // 🔑 检查是否可以绑定
              if (!this.canBindToThisSwitch(sprite)) {
                  console.error('❌ UISwitch: 无法绑定 BackgroundSprite', {
                      reason: '对象必须是UIImage类型且为当前UISwitch的子对象'
                  });
                  return;
              }

              console.log('🔍 UISwitch: 设置 boundBackgroundSprite');
              this.boundBackgroundSprite = sprite;

              // 🔑 移除：不再调用 updateSizeFromBackground()，布局由属性面板控制

              console.log('✅ UISwitch: 成功绑定 BackgroundSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          /**
           * 绑定滑块按钮精灵
           */
          bindKnobSprite(sprite) {
              // 🔑 检查是否重复绑定同一个对象，避免不必要的更新
              if (this.boundKnobSprite === sprite) {
                  console.log('🔍 UISwitch: 滑块按钮已绑定，跳过重复绑定');
                  return;
              }

              // 🔑 检查是否可以绑定
              if (!this.canBindToThisSwitch(sprite)) {
                  console.error('❌ UISwitch: 无法绑定 KnobSprite', {
                      reason: '对象必须是UIImage类型且为当前UISwitch的子对象'
                  });
                  return;
              }

              this.boundKnobSprite = sprite;

              // 🔑 确保滑块按钮不阻挡点击事件 - 让点击穿透到背景
              this.boundKnobSprite.interactive = false;
              this.boundKnobSprite.interactiveChildren = false;

              // 🔑 移除：不再调用 updateKnob()，布局由属性面板控制
              console.log('✅ UISwitch: 成功绑定 KnobSprite', {
                  spriteName: sprite.name || 'unnamed',
                  spriteType: sprite.constructor.name
              });
          }

          // 🔑 移除：updateSizeFromBackground() 方法已删除，布局由属性面板控制

          // ==================== 🎮 状态管理方法 ====================

          /**
           * 设置启用状态
           */
          setEnabled(enabled) {
              this.enabled = enabled;
              this.alpha = enabled ? 1.0 : 0.5;
              this.interactive = enabled;

              console.log('🔘 UISwitch: 设置启用状态', {
                  enabled: this.enabled,
                  alpha: this.alpha,
                  interactive: this.interactive
              });
          }

          /**
           * 设置动画时长
           */
          setAnimationDuration(duration) {
              this.animationDuration = Math.max(0, duration);
              console.log('🔘 UISwitch: 设置动画时长', this.animationDuration);
          }

          // ==================== 📊 属性获取方法 ====================

          /**
           * 获取所有属性（用于模型同步）
           */
          getProperties() {
              return {
                  // 基础属性
                  x: this.x,
                  y: this.y,
                  width: this.switchWidth,
                  height: this.switchHeight,

                  // 状态属性
                  isOn: this.isOn,
                  enabled: this.enabled,

                  // 动画属性
                  animationDuration: this.animationDuration,

                  // 事件代码
                  _eventCodes: this._eventCodes
              };
          }

          /**
           * 获取状态信息（调试用）
           */
          getStatus() {
              return {
                  type: 'UISwitch',
                  isOn: this.isOn,
                  enabled: this.enabled,
                  isAnimating: this.isAnimating,
                  size: `${this.switchWidth}x${this.switchHeight}`,
                  hasBackground: !!this.boundBackgroundSprite,
                  hasKnob: !!this.boundKnobSprite,
                  animationDuration: this.animationDuration
              };
          }

          /**
           * 克隆当前 UISwitch 对象
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UISwitch} 克隆的 UISwitch 对象
           */
          clone(options = {}) {
              console.log('🔄 UISwitch: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 准备克隆的属性
              const cloneProperties = {
                  // 基础属性
                  width: this.switchWidth,
                  height: this.switchHeight,
                  visible: this.visible,

                  // UISwitch 特有属性
                  isOn: this.isOn,
                  enabled: this.enabled,
                  animationDuration: this.animationDuration,

                  // 事件代码
                  onToggle: this.onToggle || '',
                  onTurnOn: this.onTurnOn || '',
                  onTurnOff: this.onTurnOff || '',

                  // 🔑 深度克隆脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : []
              };

              // 2. 创建克隆对象
              const clonedSwitch = new UISwitch(cloneProperties);

              // 3. 设置位置和变换属性
              clonedSwitch.x = this.x + (offsetPosition ? offsetX : 0);
              clonedSwitch.y = this.y + (offsetPosition ? offsetY : 0);
              clonedSwitch.scale.x = this.scale.x;
              clonedSwitch.scale.y = this.scale.y;
              clonedSwitch.rotation = this.rotation;
              clonedSwitch.alpha = this.alpha;
              clonedSwitch.zIndex = this.zIndex;

              // 4. 克隆所有子对象
              const clonedChildren = [];
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false }); // 子对象不偏移位置
                      clonedSwitch.addChild(clonedChild);
                      clonedChildren.push(clonedChild);
                  }
              }

              // 5. 重建绑定关系
              this.rebuildBindingsForClone(clonedSwitch, clonedChildren);

              console.log('✅ UISwitch: 克隆完成，包含', clonedChildren.length, '个子对象');
              return clonedSwitch;
          }

          /**
           * 为克隆对象重建绑定关系
           * @param {UISwitch} clonedSwitch 克隆的开关对象
           * @param {Array} clonedChildren 克隆的子对象数组
           */
          rebuildBindingsForClone(clonedSwitch, clonedChildren) {
              console.log('🔗 UISwitch: 重建克隆对象的绑定关系');

              // 找到原始绑定对象在子对象数组中的索引
              const findChildIndex = (boundObject) => {
                  if (!boundObject) return -1;
                  for (let i = 0; i < this.children.length; i++) {
                      if (this.children[i] === boundObject) {
                          return i;
                      }
                  }
                  return -1;
              };

              // 重新绑定背景精灵
              if (this.boundBackgroundSprite) {
                  const index = findChildIndex(this.boundBackgroundSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedSwitch.bindBackgroundSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定背景精灵');
                  }
              }

              // 重新绑定旋钮精灵
              if (this.boundKnobSprite) {
                  const index = findChildIndex(this.boundKnobSprite);
                  if (index >= 0 && index < clonedChildren.length) {
                      clonedSwitch.bindKnobSprite(clonedChildren[index]);
                      console.log('🔗 重新绑定旋钮精灵');
                  }
              }
          }

          /**
           * 🔑 销毁方法 - 添加 onDestroy 生命周期
           */
          destroy() {
              console.log('🗑️ UISwitch: 开始销毁开关', this.name);

              // 🔑 执行 onDestroy 脚本
              if (this.executeScript && typeof this.executeScript === 'function') {
                  try {
                      this.executeScript('onDestroy');
                      console.log('🔄 UISwitch: onDestroy 脚本执行完成');
                  } catch (error) {
                      console.error('❌ UISwitch: onDestroy 脚本执行失败', error);
                  }
              }

              // 清理动画
              if (this.animationTween) {
                  this.animationTween.stop();
                  this.animationTween = null;
              }

              // 🔑 清理 PIXI ticker 注册
              if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  Graphics.app.ticker.remove(this._tickerCallback);
                  this._tickerCallback = null;
                  console.log('🔧 UISwitch: 已清理 PIXI ticker 注册');
              }

              // 清理绑定的组件引用
              this.boundBackgroundSprite = null;
              this.boundKnobSprite = null;

              // 调用父类销毁方法
              super.destroy();

              console.log('✅ UISwitch: 销毁完成');
          }
      }

      // 将UISwitch类添加到全局作用域
      window.UISwitch = UISwitch;

      console.log('🔘 UISwitch插件已加载');

  })();

  // ===== uiContainer.js =====
  /**
       * UIContainer - 专门的容器UI组件，继承自PIXI.Container
       * 具有UIComponent功能和脚本管理功能
       */
      class UIContainer extends PIXI.Container {
          constructor(properties = {}) {
              super();

              console.log('📦 UIContainer: 创建容器组件', properties);

              // 标识为UI组件
              this.isUIComponent = true;
              this.uiComponentType = 'UIContainer';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 使用UIScriptManager添加脚本功能
              if (window.UIScriptManager) {
                  window.UIScriptManager.applyToObject(this, properties);
                  console.log('🔧 UIContainer: 脚本系统初始化完成', {
                      hasComponentScripts: !!this.componentScripts,
                      scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                      scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                  });
              }

              // 初始化容器组件
              this.initializeContainer(properties);

              // 🔑 标记为已创建，onStart会在添加到父容器时执行
              this._isCreated = true;
          }

          /**
           * 初始化容器组件
           * @param {Object} properties 容器属性
           */
          initializeContainer(properties) {
              // 设置默认属性
              this.setupDefaultProperties(properties);

              // 设置容器的交互属性
              this.interactive = properties.interactive !== false;
              this.interactiveChildren = properties.interactiveChildren !== false;

              console.log('✅ UIContainer: 容器初始化完成', {
                  size: { width: this.containerWidth, height: this.containerHeight },
                  interactive: this.interactive,
                  interactiveChildren: this.interactiveChildren
              });
          }

          /**
           * 设置默认属性
           */
          setupDefaultProperties(properties) {
              // 容器尺寸属性
              this.containerWidth = properties.width || 100;
              this.containerHeight = properties.height || 100;

              // 背景相关属性
              this.backgroundColor = properties.backgroundColor || null;
              this.backgroundAlpha = properties.backgroundAlpha || 1;
              this.borderColor = properties.borderColor || null;
              this.borderWidth = properties.borderWidth || 0;
              this.borderRadius = properties.borderRadius || 0;

              // 布局相关属性
              this.padding = properties.padding || 0;
              this.margin = properties.margin || 0;

              // 如果设置了背景色，创建背景图形
              // if (this.backgroundColor) {
              //     this.createBackground();
              // }
          }

          /**
           * 创建背景图形
           */
          createBackground() {
              if (this.backgroundGraphics) {
                  this.removeChild(this.backgroundGraphics);
              }

              this.backgroundGraphics = new PIXI.Graphics();
              this.backgroundGraphics.beginFill(parseInt(this.backgroundColor.replace('#', ''), 16), this.backgroundAlpha);

              if (this.borderWidth > 0 && this.borderColor) {
                  this.backgroundGraphics.lineStyle(this.borderWidth, parseInt(this.borderColor.replace('#', ''), 16));
              }

              if (this.borderRadius > 0) {
                  this.backgroundGraphics.drawRoundedRect(0, 0, this.containerWidth, this.containerHeight, this.borderRadius);
              } else {
                  this.backgroundGraphics.drawRect(0, 0, this.containerWidth, this.containerHeight);
              }

              this.backgroundGraphics.endFill();
              this.addChildAt(this.backgroundGraphics, 0); // 添加到最底层

              console.log('🎨 UIContainer: 背景图形已创建');
          }

          /**
           * 更新容器尺寸
           */
          updateSize(width, height) {
              this.containerWidth = width;
              this.containerHeight = height;

              // 如果有背景，重新创建
              if (this.backgroundColor) {
                  this.createBackground();
              }

              console.log('📏 UIContainer: 尺寸已更新', { width, height });
          }

          /**
           * 设置背景色
           */
          setBackgroundColor(color, alpha = 1) {
              this.backgroundColor = color;
              this.backgroundAlpha = alpha;
              this.createBackground();
          }

          /**
           * 设置边框
           */
          setBorder(color, width, radius = 0) {
              this.borderColor = color;
              this.borderWidth = width;
              this.borderRadius = radius;
              if (this.backgroundColor) {
                  this.createBackground();
              }
          }

          /**
           * 获取宽度
           */
          get width() {
              return this.containerWidth;
          }

          /**
           * 设置宽度
           */
          set width(value) {
              this.updateSize(value, this.containerHeight);
          }

          /**
           * 获取高度
           */
          get height() {
              return this.containerHeight;
          }

          /**
           * 设置高度
           */
          set height(value) {
              this.updateSize(this.containerWidth, value);
          }

          /**
           * 克隆UIContainer对象
           * @param {Object} options 克隆选项
           * @returns {UIContainer} 克隆的UIContainer对象
           */
          clone(options = {}) {
              console.log('🔄 UIContainer: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 创建新的UIContainer实例
              const clonedContainer = new UIContainer({
                  // 基础属性
                  x: this.x + (offsetPosition ? offsetX : 0),
                  y: this.y + (offsetPosition ? offsetY : 0),
                  width: this.containerWidth,
                  height: this.containerHeight,
                  visible: this.visible,
                  alpha: this.alpha,

                  // 容器特有属性
                  backgroundColor: this.backgroundColor,
                  backgroundAlpha: this.backgroundAlpha,
                  borderColor: this.borderColor,
                  borderWidth: this.borderWidth,
                  borderRadius: this.borderRadius,
                  padding: this.padding,
                  margin: this.margin,
                  interactive: this.interactive,
                  interactiveChildren: this.interactiveChildren,

                  // UIComponent 属性
                  name: (this.name || '') + '_clone',

                  // 克隆脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({ ...script })) : []
              });

              // 2. 克隆所有子对象
              for (let i = 0; i < this.children.length; i++) {
                  const child = this.children[i];
                  // 跳过背景图形，因为它会在构造函数中重新创建
                  if (child === this.backgroundGraphics) continue;

                  if (child && typeof child.clone === 'function') {
                      const clonedChild = child.clone({ offsetPosition: false });
                      clonedContainer.addChild(clonedChild);
                  }
              }

              console.log('✅ UIContainer: 克隆完成');
              return clonedContainer;
          }

          /**
           * 销毁UIContainer并清理资源
           */
          destroy() {
              console.log('🗑️ UIContainer.destroy() 被调用！', this.name || 'unnamed');

              // 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true;
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UIContainer: 销毁脚本执行失败', error);
              }

              // 清理背景图形
              if (this.backgroundGraphics) {
                  this.backgroundGraphics.destroy();
                  this.backgroundGraphics = null;
              }

              // 调用父类销毁方法
              super.destroy();

              console.log('✅ UIContainer: 销毁完成');
          }
      }

      // 导出到全局
      window.UIContainer = UIContainer;

      console.log('✅ UIContainer 插件加载完成');



  // ===== uiInput.js =====
  /**
   * UIInput 插件 - 输入框组件
   *
   * 功能：
   * - 文本输入和编辑
   * - 光标管理和文本选择
   * - 输入验证和类型限制
   * - 占位符支持
   * - 完整的脚本系统支持
   *
   * 使用方法：
   * const input = new UIInput({
   *     width: 200,
   *     height: 40,
   *     placeholder: '请输入文本...',
   *     inputType: 'text'
   * });
   *
   * <AUTHOR> Assistant
   * @version 1.0.0
   */

  (function() {
  console.log('📝 开始加载 UIInput 插件 v1.0.0');

      /**
       * UIInput - 输入框组件
       * 继承自 Sprite，参考UILabel的实现架构
       */
      class UIInput extends Sprite {
          constructor(properties = {}) {
              super();

              console.log('📝 UIInput: 创建输入框组件', properties);

              // 🔑 组件标识 (参考UILabel)
              this.isUIComponent = true;
              this.uiComponentType = 'UIInput';

              // 🔑 使用UIComponentUtils添加UIComponent功能
              if (window.UIComponentUtils) {
                  window.UIComponentUtils.applyToSprite(this, properties);
              }

              // 🔑 初始化脚本管理器（参考UISlider实现）
              this.initializeScriptManager(properties);

              // 🔑 初始化输入框
              this.initializeInput(properties);

              // 🔑 标记为已创建，onStart会在添加到父容器时执行
              this._isCreated = true;

              console.log('✅ UIInput: 输入框组件创建完成', {
                  size: `${this.width}x${this.height}`,
                  inputType: this._inputType,
                  placeholder: this._placeholder
              });
          }

          /**
           * 🔑 初始化脚本管理器（参考UISlider实现）
           */
          initializeScriptManager(properties) {
              if (typeof window.UIScriptManager !== 'undefined') {
                  // 🔑 修复：让UIScriptManager自动创建默认脚本（如果没有提供componentScripts）
                  window.UIScriptManager.applyToObject(this, properties);
                  console.log('✅ UIInput: 脚本管理器初始化完成', {
                      hasComponentScripts: !!this.componentScripts,
                      scriptsCount: this.componentScripts ? this.componentScripts.length : 0,
                      scriptNames: this.componentScripts ? this.componentScripts.map(s => s.name) : []
                  });
              } else {
                  console.warn('⚠️ UIInput: UIScriptManager 不可用');
                  // 🔑 如果UIScriptManager不可用，至少初始化一个空数组
                  this.componentScripts = properties.componentScripts || [];
              }
          }

          /**
           * 🔑 获取默认字体 - 优先使用 rmmz-mainfont
           */
          getDefaultFont() {
              // 检查 FontManager 中是否有 rmmz-mainfont
              if (typeof window !== 'undefined' && window.FontManager) {
                  const FontManager = window.FontManager;
                  if (FontManager._states && FontManager._states['rmmz-mainfont'] === 'loaded') {
                      console.log('🎨 UIInput: 使用 rmmz-mainfont 作为默认字体');
                      return 'rmmz-mainfont';
                  }
              }

              // 如果 rmmz-mainfont 不存在，使用 GameFont
              console.log('🎨 UIInput: 使用 GameFont 作为默认字体');
              return 'GameFont';
          }

          /**
           * 初始化输入框 - 参考UILabel的initializeLabel结构
           */
          initializeInput(properties) {
              // 基础属性
              this._width = properties.width || 200;
              this._height = properties.height || 40;

              // 输入框特有属性
              this._value = properties.value || '';
              this._placeholder = properties.placeholder || '';
              this._inputType = properties.inputType || 'text'; // text, password, number, email
              this._maxLength = properties.maxLength || 9;
              this._readonly = properties.readonly || false;

              // 样式属性
              this._fontSize = properties.fontSize || 16;

              // 🔑 智能字体选择：优先使用 rmmz-mainfont，如果不存在则使用 GameFont
              if (!properties.fontFace) {
                  this._fontFace = this.getDefaultFont();
              } else {
                  this._fontFace = properties.fontFace;
              }

              // 🔑 新增字体样式属性
              this._fontBold = properties.fontBold || false;

              this._textColor = properties.textColor || '#000000';
              this._placeholderColor = properties.placeholderColor || '#999999';

              // 🔑 新增描边属性
              this._outlineColor = properties.outlineColor || '#000000';
              this._outlineWidth = properties.outlineWidth || 0;
              this._backgroundColor = properties.backgroundColor || '#ffffff';
              this._borderColor = properties.borderColor || '#cccccc';
              this._focusBorderColor = properties.focusBorderColor || '#007bff';
              this._borderWidth = properties.borderWidth || 1;
              this._borderRadius = properties.borderRadius || 4;
              this._letterSpacing = properties.letterSpacing || 0; // 🔑 新增字符间距
              this._defaultPadding = 4; // 🔑 默认内边距，防止文字被裁切

              // 光标和选择状态
              this._cursorPosition = 0;
              this._selectionStart = 0;
              this._selectionEnd = 0;
              this._focused = false;
              this._cursorVisible = true;
              this._cursorBlinkTimer = 0;

              // 输入验证
              this._validationPattern = properties.validationPattern || null;
              this._validationMessage = properties.validationMessage || '输入格式不正确';

              // 创建视觉元素
              this.createInputElements();
              this.setupEventListeners();

              console.log('🔧 UIInput: 输入框初始化完成', {
                  value: this._value,
                  type: this._inputType,
                  maxLength: this._maxLength
              });
          }

          /**
           * 创建输入框的视觉元素
           */
          createInputElements() {
              // 🔑 统一绘制：直接在UIInput对象上创建一个bitmap
              this.inputBitmap = new Bitmap(this._width, this._height);
              this.bitmap = this.inputBitmap;

              // 🔑 设置字体属性
              this.inputBitmap.fontSize = this._fontSize;
              this.inputBitmap.fontFace = this._fontFace;

              // 🔑 添加调试信息
              console.log('📝 UIInput: 创建视觉元素完成', {
                  width: this._width,
                  height: this._height,
                  letterSpacing: this._letterSpacing,
                  inputBitmap: !!this.inputBitmap
              });

              // 绘制初始状态
              this.redrawAll();
          }

          /**
           * 设置事件监听器
           */
          setupEventListeners() {
              // 🔑 参考UIButton：使用RPG Maker MZ的TouchInput系统
              // 初始化状态
              this._wasClicked = false;
              this._wasHovered = false;

              // 键盘事件需要在获得焦点时绑定
              this._keydownHandler = this.onKeyDown.bind(this);
              this._keyupHandler = this.onKeyUp.bind(this);

              // 🔑 使用RPG Maker MZ的循环事件系统（参考UIButton）
              this.setupRPGMakerEventLoop();

              console.log('✅ UIInput: 事件监听器设置完成（使用TouchInput系统）');
          }

          /**
           * 🔑 设置 RPG Maker MZ 的循环事件系统（参考 UIButton）
           */
          setupRPGMakerEventLoop() {
              // 🔑 注册到 Graphics.app.ticker（PIXI 渲染循环）
              if (typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                  this._tickerCallback = () => {
                      this.processInputTouch(); // 处理TouchInput事件
                      this.update(); // 处理光标闪烁等
                  };
                  Graphics.app.ticker.add(this._tickerCallback);
                  console.log('✅ UIInput: 已注册到 PIXI ticker 循环');
                  return;
              }

              console.warn('⚠️ UIInput: 无法注册到 RPG Maker MZ 循环');
          }

          /**
           * 🔑 处理输入框触摸事件 - 使用 RPG Maker MZ 的 TouchInput 系统（参考UIButton）
           */
          processInputTouch() {
              if (!this.enabled || !this.visible) {
                  return;
              }

              // 检查TouchInput是否可用
              if (typeof window.TouchInput === 'undefined') {
                  return;
              }

              const isBeingTouched = this.isBeingTouched();
              const isTriggered = window.TouchInput.isTriggered();

              // 🔑 处理失去焦点 - 如果点击了其他地方且当前有焦点
              if (isTriggered && this._focused && !isBeingTouched) {
                  console.log('📝 UIInput: 点击其他地方，失去焦点');
                  this.blur();
                  return;
              }

              // 处理点击事件
              if (isBeingTouched && isTriggered) {
                  console.log('📝 UIInput: TouchInput点击事件触发');
                  this.focus();

                  // 计算相对位置设置光标
                  const touchX = window.TouchInput.x;
                  const bounds = this.getBounds();
                  const localX = touchX - bounds.x;
                  this.setCursorFromPosition(localX);

                  // 执行点击脚本
                  if (this.executeScript) {
                      this.executeScript('onClick');
                  }
              }
          }

          /**
           * 🔑 检查是否被触摸（参考UIButton的实现）
           */
          isBeingTouched() {
              if (typeof window.TouchInput === 'undefined') return false;

              // 获取输入框在屏幕上的位置
              const bounds = this.getBounds();
              const touchX = window.TouchInput.x;
              const touchY = window.TouchInput.y;

              const isHit = touchX >= bounds.x && touchX <= bounds.x + bounds.width &&
                           touchY >= bounds.y && touchY <= bounds.y + bounds.height;

              return isHit;
          }

          /**
           * 获取焦点
           */
          focus() {
              if (this._focused) return;

              this._focused = true;
              // 🔑 修复：重置光标状态
              this._cursorVisible = true;
              this._cursorBlinkTimer = 0;

              // 🔑 统一重绘
              this.redrawAll();

              // 绑定键盘事件
              document.addEventListener('keydown', this._keydownHandler);
              document.addEventListener('keyup', this._keyupHandler);

              // 执行焦点脚本
              if (this.executeScript) {
                  this.executeScript('onFocus');
              }

              console.log('📝 UIInput: 获得焦点');
          }

          /**
           * 失去焦点
           */
          blur() {
              if (!this._focused) return;

              this._focused = false;
              this._cursorVisible = false;
              // 🔑 统一重绘
              this.redrawAll();

              // 解绑键盘事件
              document.removeEventListener('keydown', this._keydownHandler);
              document.removeEventListener('keyup', this._keyupHandler);

              // 执行失焦脚本
              if (this.executeScript) {
                  this.executeScript('onBlur');
              }

              console.log('📝 UIInput: 失去焦点');
          }

          /**
           * 键盘按下事件处理
           */
          onKeyDown(event) {
              if (!this._focused || this._readonly) return;

              const key = event.key;
              const oldValue = this._value;

              console.log('🔧 UIInput: 键盘按下', {
                  key,
                  code: event.code,
                  isComposing: this._isComposing
              });

              // 🔑 修复：在输入法组合状态下，只处理控制键，不处理字符输入
              if (this._isComposing) {
                  // 在输入法组合状态下，只允许某些控制键
                  switch (key) {
                      case 'Escape':
                          // 取消输入法组合
                          this._isComposing = false;
                          event.preventDefault();
                          break;
                      case 'ArrowLeft':
                      case 'ArrowRight':
                      case 'ArrowUp':
                      case 'ArrowDown':
                          // 允许方向键（输入法可能需要）
                          break;
                      default:
                          // 其他键在组合状态下不处理，让输入法处理
                          console.log('🔧 UIInput: 输入法组合状态，跳过键盘处理', { key });
                          return;
                  }
                  return;
              }

              switch (key) {
                  case 'Backspace':
                      this.handleBackspace();
                      event.preventDefault();
                      break;
                  case 'Delete':
                      this.handleDelete();
                      event.preventDefault();
                      break;
                  case 'ArrowLeft':
                      this.moveCursor(-1);
                      event.preventDefault();
                      break;
                  case 'ArrowRight':
                      this.moveCursor(1);
                      event.preventDefault();
                      break;
                  case 'Home':
                      this.setCursorPosition(0);
                      event.preventDefault();
                      break;
                  case 'End':
                      this.setCursorPosition(this._value.length);
                      event.preventDefault();
                      break;
                  case 'Enter':
                      if (this.executeScript) {
                          this.executeScript('onEnterPressed');
                      }
                      event.preventDefault();
                      break;
                  default:
                      // 🔑 修复：只在非输入法组合状态下处理可打印字符
                      if (!this._isComposing && this.isValidInput(key)) {
                          this.insertText(key);
                          event.preventDefault();
                      }
                      break;
              }

              // 如果值发生变化，触发变化事件
              if (this._value !== oldValue) {
                  this.onValueChanged(oldValue);
              }
          }

          /**
           * 键盘释放事件处理
           */
          onKeyUp(event) {
              // 可以在这里处理一些特殊的键盘释放事件
          }



          /**
           * 处理退格键
           */
          handleBackspace() {
              if (this._cursorPosition > 0) {
                  this._value = this._value.slice(0, this._cursorPosition - 1) + this._value.slice(this._cursorPosition);
                  this._cursorPosition--;

                  // 🔑 修复：删除后重新计算文本偏移，确保内容可见
                  this.adjustTextOffsetAfterDeletion();

                  // 🔑 统一重绘
                  this.redrawAll();
              }
          }

          /**
           * 处理删除键
           */
          handleDelete() {
              if (this._cursorPosition < this._value.length) {
                  this._value = this._value.slice(0, this._cursorPosition) + this._value.slice(this._cursorPosition + 1);

                  // 🔑 修复：删除后重新计算文本偏移，确保内容可见
                  this.adjustTextOffsetAfterDeletion();

                  // 🔑 统一重绘
                  this.redrawAll();
              }
          }

          /**
           * 🔑 删除后调整文本偏移，确保内容始终可见
           */
          adjustTextOffsetAfterDeletion() {
              const maxTextWidth = this._width - this._defaultPadding * 2;
              // 🔑 修复：使用显示文本宽度（考虑密码模式）
              const totalTextWidth = this.measureDisplayTextWidth(this._value);

              // 如果文本完全可以显示，重置偏移
              if (totalTextWidth <= maxTextWidth) {
                  this._textOffset = 0;
                  console.log('🔧 UIInput: 文本可完全显示，重置偏移');
                  return;
              }

              // 如果当前偏移导致右侧有空白，调整偏移
              if (this._textOffset > 0 && totalTextWidth - this._textOffset < maxTextWidth) {
                  this._textOffset = Math.max(0, totalTextWidth - maxTextWidth);
                  console.log('🔧 UIInput: 调整偏移以消除右侧空白', {
                      newOffset: this._textOffset,
                      totalTextWidth,
                      maxTextWidth,
                      inputType: this._inputType
                  });
              }
          }

          /**
           * 移动光标
           */
          moveCursor(direction) {
              const newPosition = Math.max(0, Math.min(this._value.length, this._cursorPosition + direction));
              this.setCursorPosition(newPosition);
          }

          /**
           * 设置光标位置
           */
          setCursorPosition(position) {
              this._cursorPosition = Math.max(0, Math.min(this._value.length, position));
              this.updateCursor();
          }

          /**
           * 根据鼠标位置设置光标
           */
          setCursorFromPosition(x) {
              // 🔑 修复：考虑默认内边距和文本偏移
              const relativeX = x - this._defaultPadding + (this._textOffset || 0);
              let position = 0;

              console.log('🔧 UIInput: 计算光标位置', {
                  clickX: x,
                  letterSpacing: this._letterSpacing,
                  textOffset: this._textOffset || 0,
                  relativeX,
                  valueLength: this._value.length
              });

              // 🔑 修复：使用带字符间距的文本宽度测量
              if (this._value && this.inputBitmap) {
                  // 逐个字符测量，找到最接近的位置
                  for (let i = 0; i <= this._value.length; i++) {
                      const textWidth = this.measureDisplayTextWidthWithSpacing(this._value.substring(0, i));

                      if (i === this._value.length) {
                          // 如果是最后一个位置，检查点击是否在文本末尾之后
                          if (relativeX >= textWidth) {
                              position = i;
                          }
                          break;
                      } else {
                          // 计算当前字符的中点
                          const nextTextWidth = this.measureDisplayTextWidthWithSpacing(this._value.substring(0, i + 1));
                          const charMidPoint = (textWidth + nextTextWidth) / 2;

                          if (relativeX <= charMidPoint) {
                              position = i;
                              break;
                          }
                          position = i + 1;
                      }
                  }
              }

              console.log('🔧 UIInput: 设置光标位置', {
                  calculatedPosition: position,
                  finalPosition: Math.max(0, Math.min(this._value.length, position))
              });

              this.setCursorPosition(position);
          }

          /**
           * 🔑 测量文本宽度 - 使用RPG Maker MZ的Bitmap测量
           */
          measureTextWidth(text) {
              if (!text) return 0;

              // 🔑 关键修复：创建临时bitmap来测量文本宽度，使用当前的字体设置
              const tempBitmap = new Bitmap(1, 1);
              tempBitmap.fontSize = this._fontSize;
              tempBitmap.fontFace = this._fontFace; // 直接使用内部字体设置，而不是依赖 inputBitmap

              const width = tempBitmap.measureTextWidth(text);
              tempBitmap.destroy(); // 销毁临时bitmap释放内存
              return width;
          }

          /**
           * 🔑 测量显示文本宽度（考虑密码模式）
           */
          measureDisplayTextWidth(text) {
              if (!text) return 0;

              // 🔑 修复：密码模式下测量星号的宽度
              if (this._inputType === 'password') {
                  const asteriskText = '*'.repeat(text.length);
                  return this.measureTextWidth(asteriskText);
              }

              return this.measureTextWidth(text);
          }

          /**
           * 🔑 测量显示文本宽度（考虑字符间距）
           */
          measureDisplayTextWidthWithSpacing(text) {
              if (!text) return 0;

              const baseWidth = this.measureDisplayTextWidth(text);
              const spacingWidth = (text.length - 1) * this._letterSpacing;
              return baseWidth + spacingWidth;
          }

          /**
           * 🔑 绘制带字符间距的文本
           */
          drawTextWithSpacing(displayText, textOffset) {
              if (!displayText) return;

              // 🔑 关键修复：确保字体属性已正确设置（应该在 drawText 中已设置，但为了安全再次确认）
              this.inputBitmap.fontSize = this._fontSize;
              this.inputBitmap.fontFace = this._fontFace;
              // 注意：textColor 在 drawText 中已设置，这里不重复设置

              const chars = displayText.split('');
              let currentX = this._defaultPadding - textOffset;

              // 🔑 关键修复：使用与普通绘制完全相同的垂直位置计算
              const textY = Math.floor((this._height - this._fontSize) / 2);

              // 🔑 关键修复：使用与普通绘制相同的文本高度
              const textHeight = this._fontSize; // 而不是 this._height

              for (let i = 0; i < chars.length; i++) {
                  const char = chars[i];
                  const charWidth = this.measureTextWidth(char);

                  // 🔑 关键修复：绘制单个字符时使用与普通绘制相同的参数
                  // 使用 textHeight 而不是 this._height，移除不必要的 +10
                  this.inputBitmap.drawText(char, currentX, textY, charWidth, textHeight, 'left');

                  // 移动到下一个字符位置
                  currentX += charWidth + this._letterSpacing;
              }

              console.log('🔧 UIInput: 绘制字符间距文本', {
                  charCount: chars.length,
                  letterSpacing: this._letterSpacing,
                  startX: this._defaultPadding - textOffset,
                  textY,
                  textHeight,
                  fontSize: this._fontSize,
                  inputHeight: this._height
              });
          }



          /**
           * 插入文本
           */
          insertText(text) {
              if (this._value.length >= this._maxLength) return;

              // 🔑 修复：正确处理多字符文本（如中文）
              this._value = this._value.slice(0, this._cursorPosition) + text + this._value.slice(this._cursorPosition);
              this._cursorPosition += text.length; // 使用text.length而不是固定的1
              this.updateCursor();
              this.redrawAll();

              // 🔑 触发值改变事件
              this.onValueChanged(this._value.slice(0, this._cursorPosition - text.length) + this._value.slice(this._cursorPosition));
          }

          /**
           * 验证输入是否有效
           */
          isValidInput(key) {
              // 忽略控制字符
              if (key.length > 1) return false;

              // 根据输入类型验证
              switch (this._inputType) {
                  case 'number':
                      return /[0-9.-]/.test(key);
                  case 'email':
                      return /[a-zA-Z0-9@._-]/.test(key);
                  case 'password':
                  case 'text':
                  default:
                      return true;
              }
          }

          /**
           * 值改变时的处理
           */
          onValueChanged(oldValue) {
              // 🔑 同步值到模型对象（反向同步）
              this.syncValueToModel();

              // 执行值改变脚本
              if (this.executeScript) {
                  this.executeScript('onChange', this._value, oldValue);
              }

              console.log('📝 UIInput: 值改变', { oldValue, newValue: this._value });
          }

          /**
           * 🔑 同步值到模型对象（反向同步）
           * 当用户在输入框中输入时，需要将值同步回模型
           */
          syncValueToModel() {
              // 查找对应的模型对象
              if (this.parent && this.parent.children) {
                  // 遍历父容器的所有子对象，找到对应的模型
                  for (const child of this.parent.children) {
                      if (child._originalObject === this) {
                          // 找到了对应的模型，同步值
                          if (child.value !== this._value) {
                              child.value = this._value;
                              console.log('🔄 UIInput: 同步值到模型', {
                                  inputValue: this._value,
                                  modelValue: child.value
                              });
                          }
                          return;
                      }
                  }
              }

              // 如果没有找到模型，尝试通过全局查找
              if (typeof window !== 'undefined' && window.currentScene) {
                  this.findAndSyncToModel(window.currentScene);
              }
          }

          /**
           * 🔑 递归查找并同步到模型对象
           */
          findAndSyncToModel(container) {
              if (!container || !container.children) return;

              for (const child of container.children) {
                  // 检查是否是对应的模型对象
                  if (child._originalObject === this) {
                      if (child.value !== this._value) {
                          child.value = this._value;
                          console.log('🔄 UIInput: 递归找到并同步值到模型', {
                              inputValue: this._value,
                              modelValue: child.value
                          });
                      }
                      return;
                  }

                  // 递归查找子容器
                  this.findAndSyncToModel(child);
              }
          }

          /**
           * 🔑 统一绘制方法 - 在一个bitmap上绘制所有内容
           */
          redrawAll() {
              if (!this.inputBitmap) return;

              // 清除整个bitmap
              this.inputBitmap.clear();

              // 1. 绘制背景和边框
              this.drawBackground();

              // 2. 绘制文本
              this.drawText();

              // 3. 绘制光标
              this.drawCursor();

              console.log('🔧 UIInput: 统一绘制完成');
          }

          /**
           * 绘制背景和边框
           */
          drawBackground() {
              const borderColor = this._focused ? this._focusBorderColor : this._borderColor;

              // 绘制背景
              this.inputBitmap.fillRect(0, 0, this._width, this._height, this._backgroundColor);

              // 绘制边框
              if (this._borderWidth > 0) {
                  // 上边框
                  this.inputBitmap.fillRect(0, 0, this._width, this._borderWidth, borderColor);
                  // 下边框
                  this.inputBitmap.fillRect(0, this._height - this._borderWidth, this._width, this._borderWidth, borderColor);
                  // 左边框
                  this.inputBitmap.fillRect(0, 0, this._borderWidth, this._height, borderColor);
                  // 右边框
                  this.inputBitmap.fillRect(this._width - this._borderWidth, 0, this._borderWidth, this._height, borderColor);
              }
          }

          /**
           * 绘制文本内容
           */
          drawText() {
              const displayText = this.getDisplayText();
              const textColor = this._value ? this._textColor : this._placeholderColor;

              // 🔑 关键修复：每次绘制文本时都要重新设置所有字体属性
              this.inputBitmap.fontSize = this._fontSize;
              this.inputBitmap.fontFace = this._fontFace;
              this.inputBitmap.textColor = textColor;

              // 🔑 新增：设置字体样式属性
              if (this.inputBitmap.fontBold !== undefined) {
                  this.inputBitmap.fontBold = this._fontBold;
              }

              // 🔑 新增：设置描边属性
              if (this.inputBitmap.outlineColor !== undefined) {
                  this.inputBitmap.outlineColor = this._outlineColor;
              }
              if (this.inputBitmap.outlineWidth !== undefined) {
                  this.inputBitmap.outlineWidth = this._outlineWidth;
              }

              // 🔑 修复：正确应用文本偏移，考虑默认内边距
              const maxTextWidth = this._width - this._defaultPadding * 2;
              const totalTextWidth = this.measureDisplayTextWidthWithSpacing(displayText);

              // 计算文本偏移
              let textOffset = 0;
              if (totalTextWidth > maxTextWidth) {
                  // 🔑 关键修复：让文本右边缘始终紧贴输入框右边缘
                  textOffset = totalTextWidth - maxTextWidth;

                  // 但要确保光标可见
                  const cursorX = this.measureDisplayTextWidth(this._value.substring(0, this._cursorPosition));
                  const visibleCursorX = cursorX - textOffset;
                  const minMargin = 10;

                  if (visibleCursorX < minMargin) {
                      textOffset = cursorX - minMargin;
                      textOffset = Math.max(0, textOffset);
                  }
              }

              // 更新文本偏移
              this._textOffset = textOffset;

              // 🔑 支持字符间距的文本绘制
              if (this._letterSpacing > 0 && displayText) {
                  this.drawTextWithSpacing(displayText, textOffset);
              } else {
                  // 普通绘制（无字符间距）
                  const textX = this._defaultPadding - textOffset;
                  // 🔑 修复：更精确的垂直居中计算
                  const textY = Math.floor((this._height - this._fontSize) / 2);

                  // 🔑 关键修复：让文本以自然宽度绘制，设置合适的textHeight
                  const textWidth = Math.max(totalTextWidth, maxTextWidth);
                  const textHeight = this._fontSize; // 使用字体大小作为高度，避免RPG Maker MZ重新对齐

                  this.inputBitmap.drawText(displayText, textX, textY, textWidth, textHeight, 'left');
              }

              console.log('🔧 UIInput: 绘制文本', {
                  displayText: displayText.substring(0, 10) + (displayText.length > 10 ? '...' : ''),
                  totalTextWidth,
                  maxTextWidth,
                  textOffset,
                  letterSpacing: this._letterSpacing,
                  strategy: totalTextWidth <= maxTextWidth ? 'no-scroll' : 'right-aligned'
              });
          }

          /**
           * 绘制光标
           */
          drawCursor() {
              if (!this._focused || !this._cursorVisible) {
                  return;
              }

              // 🔑 计算光标位置 - 使用带字符间距的测量
              const textBeforeCursor = this._value.substring(0, this._cursorPosition);
              let cursorX = this.measureDisplayTextWidthWithSpacing(textBeforeCursor);

              // 🔑 使用与drawText相同的文本偏移逻辑
              const textOffset = this._textOffset || 0;

              // 调整光标位置
              cursorX = cursorX - textOffset;

              // 🔑 确保光标在输入框边界内，考虑默认内边距
              const maxTextWidth = this._width - this._defaultPadding * 2;
              const minX = 0;
              const maxX = maxTextWidth - 2;
              cursorX = Math.max(minX, Math.min(maxX, cursorX));

              // 绘制光标线条 - 确保完整显示
              const finalCursorX = this._defaultPadding + cursorX;

              // 🔑 修复：计算合适的光标高度和位置，确保完整显示
              const availableHeight = this._height;
              const cursorHeight = Math.min(this._fontSize, availableHeight - 4); // 确保不超出边界
              const finalCursorY = (availableHeight - cursorHeight) / 2; // 垂直居中

              this.inputBitmap.fillRect(finalCursorX, finalCursorY, 1, cursorHeight, this._textColor);

              console.log('🔧 UIInput: 绘制光标', {
                  cursorPosition: this._cursorPosition,
                  textBeforeCursor,
                  inputType: this._inputType,
                  originalCursorX: this.measureDisplayTextWidth(textBeforeCursor),
                  textOffset,
                  adjustedCursorX: cursorX,
                  finalX: finalCursorX,
                  maxTextWidth,
                  focused: this._focused,
                  cursorVisible: this._cursorVisible
              });
          }

          /**
           * 🔑 更新光标 - 兼容旧的API调用
           */
          updateCursor() {
              // 现在光标是在统一绘制中处理的，所以直接重绘全部
              this.redrawAll();
          }

          /**
           * 获取要显示的文本
           */
          getDisplayText() {
              if (this._value) {
                  // 如果是密码类型，显示星号
                  if (this._inputType === 'password') {
                      return '*'.repeat(this._value.length);
                  }
                  return this._value;
              } else {
                  // 显示占位符
                  return this._placeholder;
              }
          }



          /**
           * 🔑 计算文本偏移量（用于文本滚动）
           */
          calculateTextOffset(cursorX, maxTextWidth) {
              if (!this._textOffset) this._textOffset = 0;

              const totalTextWidth = this.measureDisplayTextWidth(this._value);

              // 🔑 修复：如果文本完全可以显示，不需要偏移
              if (totalTextWidth <= maxTextWidth) {
                  this._textOffset = 0;
                  console.log('🔧 UIInput: 文本完全可见，重置偏移', {
                      totalTextWidth,
                      maxTextWidth,
                      textOffset: this._textOffset
                  });
                  return this._textOffset;
              }

              // 🔑 关键修复：当文本超出宽度时，始终让文本右边缘紧贴输入框右边缘
              // 这样最后一个字符就会始终在最右边
              this._textOffset = totalTextWidth - maxTextWidth;

              // 🔑 但是要确保光标可见 - 如果光标在左边界外，调整偏移
              const visibleCursorX = cursorX - this._textOffset;
              const minMargin = 10; // 光标左边的最小边距

              if (visibleCursorX < minMargin) {
                  // 如果光标太靠左，调整偏移让光标可见
                  this._textOffset = cursorX - minMargin;
                  // 但不能小于0
                  this._textOffset = Math.max(0, this._textOffset);
              }

              console.log('🔧 UIInput: 计算文本偏移', {
                  cursorX,
                  totalTextWidth,
                  maxTextWidth,
                  visibleCursorX,
                  textOffset: this._textOffset,
                  strategy: visibleCursorX < minMargin ? 'cursor-visible' : 'text-right-aligned'
              });

              return this._textOffset;
          }

          /**
           * 获取/设置宽度
           */
          get width() {
              return this._width;
          }

          set width(value) {
              if (this._width !== value) {
                  this._width = value;
                  // 🔑 重新创建bitmap并重新设置所有属性
                  if (this.inputBitmap) {
                      this.inputBitmap.destroy();
                      this.inputBitmap = new Bitmap(this._width, this._height);
                      this.bitmap = this.inputBitmap;

                      // 🔑 关键修复：重新设置所有bitmap属性
                      this._setupBitmapProperties();
                  }
                  this.redrawAll();
              }
          }

          /**
           * 获取/设置高度
           */
          get height() {
              return this._height;
          }

          set height(value) {
              if (this._height !== value) {
                  this._height = value;
                  // 🔑 重新创建bitmap并重新设置所有属性
                  if (this.inputBitmap) {
                      this.inputBitmap.destroy();
                      this.inputBitmap = new Bitmap(this._width, this._height);
                      this.bitmap = this.inputBitmap;

                      // 🔑 关键修复：重新设置所有bitmap属性
                      this._setupBitmapProperties();
                  }
                  this.redrawAll();
              }
          }

          /**
           * 🔑 设置bitmap的所有属性 - 修复宽高改变后属性丢失的问题
           */
          _setupBitmapProperties() {
              if (!this.inputBitmap) return;

              // 设置字体属性
              this.inputBitmap.fontSize = this._fontSize;
              this.inputBitmap.fontFace = this._fontFace;

              // 🔑 新增：设置字体样式属性
              if (this.inputBitmap.fontBold !== undefined) {
                  this.inputBitmap.fontBold = this._fontBold;
              }

              // 🔑 新增：设置描边属性
              if (this.inputBitmap.outlineColor !== undefined) {
                  this.inputBitmap.outlineColor = this._outlineColor;
              }
              if (this.inputBitmap.outlineWidth !== undefined) {
                  this.inputBitmap.outlineWidth = this._outlineWidth;
              }

              // 🔑 修复：不要在这里设置 textColor，因为 drawText() 会根据当前状态正确设置
              // 如果输入框为空，应该显示占位符颜色；如果有内容，才显示文本颜色

              console.log('🔧 UIInput: 重新设置bitmap属性', {
                  fontSize: this._fontSize,
                  fontFace: this._fontFace,
                  fontBold: this._fontBold,
                  outlineColor: this._outlineColor,
                  outlineWidth: this._outlineWidth,
                  note: 'textColor 将在 drawText() 中根据内容状态设置'
              });
          }

          /**
           * 获取/设置值
           */
          get value() {
              return this._value;
          }

          set value(newValue) {
              const oldValue = this._value;
              this._value = String(newValue || '');
              this._cursorPosition = Math.min(this._cursorPosition, this._value.length);
              this.redrawAll();

              if (oldValue !== this._value) {
                  this.onValueChanged(oldValue);
              }
          }

          /**
           * 获取/设置占位符
           */
          get placeholder() {
              return this._placeholder;
          }

          set placeholder(value) {
              this._placeholder = String(value || '');
              this.redrawAll();
          }

          /**
           * 获取/设置只读状态
           */
          get readonly() {
              return this._readonly;
          }

          set readonly(value) {
              this._readonly = Boolean(value);
          }

          /**
           * 获取/设置输入类型
           */
          get inputType() {
              return this._inputType;
          }

          set inputType(value) {
              this._inputType = value || 'text';
          }

          /**
           * 清空输入框
           */
          clear() {
              this.value = '';
          }

          /**
           * 🔑 属性面板同步方法 - 支持InputModel的属性同步
           */
          setValue(newValue) {
              this.value = newValue;
          }

          setPlaceholder(newPlaceholder) {
              this.placeholder = newPlaceholder;
          }

          setInputType(newType) {
              this.inputType = newType;
              this.redrawAll(); // 重新绘制以应用新的输入类型
          }

          setMaxLength(newMaxLength) {
              this._maxLength = newMaxLength;
          }

          setReadonly(newReadonly) {
              this.readonly = newReadonly;
          }

          setFontSize(newFontSize) {
              this._fontSize = newFontSize;
              this.redrawAll();
          }

          // 🔑 新增字体族设置方法
          setFontFace(newFontFace) {
              this._fontFace = newFontFace;
              if (this.inputBitmap) {
                  this.inputBitmap.fontFace = newFontFace;
              }
              this.redrawAll();
          }

          get fontFace() {
              return this._fontFace;
          }

          set fontFace(value) {
              this.setFontFace(value);
          }

          setTextColor(newColor) {
              this._textColor = newColor;
              // 🔑 修复：不要在这里设置 inputBitmap.textColor，因为 drawText() 会根据当前状态正确设置
              // 如果输入框为空，应该显示占位符颜色；如果有内容，才显示文本颜色
              this.redrawAll();
          }

          setPlaceholderColor(newColor) {
              this._placeholderColor = newColor;
              this.redrawAll();
          }

          setBackgroundColor(newColor) {
              this._backgroundColor = newColor;
              this.redrawAll();
          }

          setBorderColor(newColor) {
              this._borderColor = newColor;
              this.redrawAll();
          }

          setFocusBorderColor(newColor) {
              this._focusBorderColor = newColor;
              if (this._focused) {
                  this.redrawAll();
              }
          }

          setBorderWidth(newWidth) {
              this._borderWidth = newWidth;
              this.redrawAll();
          }

          setBorderRadius(newRadius) {
              this._borderRadius = newRadius;
              this.redrawAll();
          }

          setLetterSpacing(newSpacing) {
              this._letterSpacing = newSpacing;
              this.redrawAll();
          }

          // 🔑 新增字体样式设置方法
          setFontBold(newBold) {
              this._fontBold = newBold;
              this.redrawAll();
          }

          get fontBold() {
              return this._fontBold;
          }

          set fontBold(value) {
              this.setFontBold(value);
          }

          // 🔑 新增描边设置方法
          setOutlineColor(newColor) {
              this._outlineColor = newColor;
              this.redrawAll();
          }

          get outlineColor() {
              return this._outlineColor;
          }

          set outlineColor(value) {
              this.setOutlineColor(value);
          }

          setOutlineWidth(newWidth) {
              this._outlineWidth = newWidth;
              this.redrawAll();
          }

          get outlineWidth() {
              return this._outlineWidth;
          }

          set outlineWidth(value) {
              this.setOutlineWidth(value);
          }

          setValidationPattern(newPattern) {
              this._validationPattern = newPattern;
          }

          setValidationMessage(newMessage) {
              this._validationMessage = newMessage;
          }

          /**
           * 选择所有文本
           */
          selectAll() {
              this._selectionStart = 0;
              this._selectionEnd = this._value.length;
              this.setCursorPosition(this._value.length);
          }

          /**
           * 验证当前值
           */
          validate() {
              if (!this._validationPattern) return true;

              const isValid = this._validationPattern.test(this._value);
              if (!isValid && this.executeScript) {
                  this.executeScript('onValidationFailed', this._validationMessage);
              }
              return isValid;
          }

          /**
           * 🔑 更新方法 - 支持脚本执行 (参考UILabel)
           */
          update() {
              // 调用父类更新方法（如果存在）
              if (super.update) {
                  super.update();
              }

              // 光标闪烁逻辑
              if (this._focused) {
                  this._cursorBlinkTimer++;
                  if (this._cursorBlinkTimer >= 30) { // 约0.5秒闪烁一次
                      this._cursorVisible = !this._cursorVisible;
                      this._cursorBlinkTimer = 0;
                      this.updateCursor();
                  }
              }

              // 🔑 执行更新脚本
              if (this.executeScript) {
                  this.executeScript('onUpdate');
              }
          }

          /**
           * 🔑 克隆UIInput对象 (参考UILabel的clone实现)
           * @param {Object} options 克隆选项
           * @param {boolean} options.offsetPosition 是否偏移位置 (默认: true)
           * @param {number} options.offsetX 水平偏移量 (默认: 20)
           * @param {number} options.offsetY 垂直偏移量 (默认: 20)
           * @returns {UIInput} 克隆的 UIInput 对象
           */
          clone(options = {}) {
              console.log('🔄 UIInput: 开始克隆对象');

              const {
                  offsetPosition = true,
                  offsetX = 20,
                  offsetY = 20
              } = options;

              // 1. 创建新的UIInput对象
              const clonedInput = new UIInput({
                  // 基础属性
                  width: this.width,
                  height: this.height,
                  visible: this.visible,

                  // 🔑 UIComponent 属性（安全访问）
                  name: this.name || '',
                  enabled: this.enabled !== false,

                  // 🔑 深度克隆组件脚本数组
                  componentScripts: this.componentScripts ?
                      this.componentScripts.map(script => ({
                          id: script.id,
                          name: script.name,
                          type: script.type,
                          enabled: script.enabled,
                          code: script.code,
                          description: script.description
                      })) : [],

                  // UIInput 特有属性
                  value: this._value,
                  placeholder: this._placeholder,
                  inputType: this._inputType,
                  maxLength: this._maxLength,
                  readonly: this._readonly,
                  fontSize: this._fontSize,
                  textColor: this._textColor,
                  placeholderColor: this._placeholderColor,
                  backgroundColor: this._backgroundColor,
                  borderColor: this._borderColor,
                  focusBorderColor: this._focusBorderColor,
                  borderWidth: this._borderWidth,
                  borderRadius: this._borderRadius,
                  letterSpacing: this._letterSpacing,
                  validationPattern: this._validationPattern,
                  validationMessage: this._validationMessage
              });

              // 2. 设置位置和变换属性
              clonedInput.x = this.x + (offsetPosition ? offsetX : 0);
              clonedInput.y = this.y + (offsetPosition ? offsetY : 0);
              clonedInput.scale.x = this.scale.x;
              clonedInput.scale.y = this.scale.y;
              clonedInput.rotation = this.rotation;
              clonedInput.alpha = this.alpha;
              clonedInput.anchor.x = this.anchor.x;
              clonedInput.anchor.y = this.anchor.y;
              clonedInput.pivot.x = this.pivot.x;
              clonedInput.pivot.y = this.pivot.y;
              clonedInput.skew.x = this.skew.x;
              clonedInput.skew.y = this.skew.y;
              clonedInput.zIndex = this.zIndex;

              console.log('✅ UIInput: 克隆完成', {
                  originalValue: this._value,
                  clonedValue: clonedInput._value,
                  position: `(${clonedInput.x}, ${clonedInput.y})`
              });

              return clonedInput;
          }

          /**
           * 🔑 销毁时清理资源 (参考UILabel的destroy实现)
           */
          destroy(options) {
              console.log('🗑️ UIInput: 开始销毁输入框组件');

              // 🔑 安全执行销毁脚本
              try {
                  if (this.executeScript && !this._isDestroying) {
                      this._isDestroying = true; // 防止重复销毁
                      this.executeScript('onDestroy');
                  }
              } catch (error) {
                  console.warn('⚠️ UIInput: 销毁脚本执行失败', error);
              }

              try {
                  // 失去焦点并清理事件监听器
                  this.blur();

                  // 🔑 清理RPG Maker MZ循环事件
                  if (this._tickerCallback && typeof Graphics !== 'undefined' && Graphics.app && Graphics.app.ticker) {
                      Graphics.app.ticker.remove(this._tickerCallback);
                      this._tickerCallback = null;
                      console.log('🗑️ UIInput: 已从 PIXI ticker 循环中移除');
                  }

                  // 🔑 修复：清理Bitmap和Sprite对象
                  if (this.backgroundBitmap) {
                      this.backgroundBitmap.destroy();
                      this.backgroundBitmap = null;
                  }

                  if (this.backgroundSprite) {
                      this.backgroundSprite.destroy();
                      this.backgroundSprite = null;
                  }

                  if (this.cursorBitmap) {
                      this.cursorBitmap.destroy();
                      this.cursorBitmap = null;
                  }

                  if (this.cursorSprite) {
                      this.cursorSprite.destroy();
                      this.cursorSprite = null;
                  }

                  if (this.textBitmap) {
                      this.textBitmap.destroy();
                      this.textBitmap = null;
                  }

                  if (this.textSprite) {
                      this.textSprite.destroy();
                      this.textSprite = null;
                  }

                  // 调用父类销毁方法
                  super.destroy(options);

                  console.log('✅ UIInput: 输入框组件销毁完成');
              } catch (error) {
                  console.error('❌ UIInput: 销毁过程中出错', error);
              }
          }

          /**
           * 获取组件属性信息 (用于调试和序列化)
           */
          getProperties() {
              return {
                  // 基础属性
                  x: this.x,
                  y: this.y,
                  width: this.width,
                  height: this.height,
                  visible: this.visible,

                  // UIInput 特有属性
                  value: this._value,
                  placeholder: this._placeholder,
                  inputType: this._inputType,
                  maxLength: this._maxLength,
                  readonly: this._readonly,
                  fontSize: this._fontSize,
                  textColor: this._textColor,
                  placeholderColor: this._placeholderColor,
                  backgroundColor: this._backgroundColor,
                  borderColor: this._borderColor,
                  focusBorderColor: this._focusBorderColor,
                  borderWidth: this._borderWidth,
                  borderRadius: this._borderRadius,
                  letterSpacing: this._letterSpacing,
                  focused: this._focused,
                  cursorPosition: this._cursorPosition
              };
          }
      }

      // 导出到全局
      window.UIInput = UIInput;
      console.log('✅ UIInput 插件 v1.0.0 加载完成');

  })();

  // ===== customMessageInterceptor.js =====
  /**
       * 🎯 消息系统拦截器 - 简化回调设计
       *
       * 职责：
       * 1. 拦截 $gameMessage.add() 和 $gameMessage.setChoices()
       * 2. 提供简单的回调方法，UI组件直接赋值即可
       * 3. 不同类型的消息调用不同的回调方法
       */

      /**
       * 🔧 消息拦截器
       */
      class MessageInterceptor {
          constructor() {
              this.isEnabled = true;
              this.isSetup = false;

              // 🔑 回调方法 - UI组件直接赋值这些方法
              this._onMessage = null;      // 内部存储
              this._onChoice = null;       // 内部存储
              this._onNumberInput = null;  // 内部存储
              this._onItemChoice = null;   // 内部存储
          }

          // 🔑 设置消息回调时自动初始化拦截器
          set onMessage(callback) {
              console.log('🔧 MessageInterceptor: 设置 onMessage 回调');
              this._onMessage = callback;
              this.ensureSetup();
          }

          get onMessage() {
              return this._onMessage;
          }

          // 🔑 设置选择回调时自动初始化拦截器
          set onChoice(callback) {
              this._onChoice = callback;
              this.ensureSetup();
          }

          get onChoice() {
              return this._onChoice;
          }

          // 🔑 设置数字输入回调时自动初始化拦截器
          set onNumberInput(callback) {
              this._onNumberInput = callback;
              this.ensureSetup();
          }

          get onNumberInput() {
              return this._onNumberInput;
          }

          // 🔑 设置物品选择回调时自动初始化拦截器
          set onItemChoice(callback) {
              this._onItemChoice = callback;
              this.ensureSetup();
          }

          get onItemChoice() {
              return this._onItemChoice;
          }

          /**
           * 🎯 确保拦截器已设置（延迟初始化）
           */
          ensureSetup() {
              if (!this.isSetup) {
                  this.setupInterception();
              }
          }

          /**
           * 🎯 设置拦截
           */
          setupInterception() {
              // 检查 $gameMessage 是否存在
              if (typeof window.$gameMessage === 'undefined' || window.$gameMessage === null) {
                  console.log('MessageInterceptor: $gameMessage 尚未初始化，延迟设置拦截');
                  // 延迟重试
                  setTimeout(() => {
                      this.isSetup = false; // 重置状态
                      this.ensureSetup();
                  }, 100);
                  return;
              }

              if (this.isSetup) {
                  return; // 避免重复设置
              }

              // 保存原始方法
              const originalAdd = window.$gameMessage.add;
              const originalSetChoices = window.$gameMessage.setChoices;
              const self = this;

              this.isSetup = true;

              // 🔑 拦截消息添加
              window.$gameMessage.add = function(text) {
                  if (self.isEnabled) {
                      // 🔑 关键：先调用原生方法保持消息状态
                      originalAdd.call(this, text);

                      // 触发自定义消息事件
                      self.triggerCustomMessageEvent({
                          type: 'message',
                          text: text,
                          faceName: window.$gameMessage.faceName(),
                          faceIndex: window.$gameMessage.faceIndex(),
                          background: window.$gameMessage.background(),
                          positionType: window.$gameMessage.positionType()
                      });

                      // 注意：不阻止原生消息，让$gameMessage.isBusy()正常工作
                  } else {
                      // 使用原生系统
                      originalAdd.call(this, text);
                  }
              };

              // 🔑 拦截选择项设置
              window.$gameMessage.setChoices = function(choices, defaultType, cancelType) {
                  if (self.isEnabled) {
                      // 触发自定义选择事件
                      self.triggerCustomChoiceEvent({
                          type: 'choices',
                          choices: choices,
                          defaultType: defaultType,
                          cancelType: cancelType,
                          background: window.$gameMessage.choiceBackground()
                      });

                      // 阻止原生选择显示
                      return;
                  } else {
                      // 使用原生系统
                      originalSetChoices.call(this, choices, defaultType, cancelType);
                  }
              };

              console.log('✅ 消息拦截器已启用');
          }

          /**
           * 🎯 触发消息回调
           */
          triggerCustomMessageEvent(messageData) {
              console.log('🎯 MessageInterceptor: 触发消息回调:', messageData);
              console.log('🔍 MessageInterceptor: 当前回调状态:', {
                  hasCallback: !!this.onMessage,
                  callbackType: typeof this.onMessage,
                  isEnabled: this.isEnabled,
                  isSetup: this.isSetup
              });

              // 🔑 直接调用消息回调方法
              if (this.onMessage && typeof this.onMessage === 'function') {
                  try {
                      console.log('✅ MessageInterceptor: 执行消息回调');
                      this.onMessage(messageData);
                  } catch (error) {
                      console.error('❌ MessageInterceptor: 消息回调执行失败:', error);
                  }
              } else {
                  console.warn('⚠️ MessageInterceptor: 没有设置消息回调方法');
              }
          }

          /**
           * 🎯 触发选择回调
           */
          triggerCustomChoiceEvent(choiceData) {
              console.log('🎯 触发选择回调:', choiceData);

              // � 直接调用选择回调方法
              if (this.onChoice && typeof this.onChoice === 'function') {
                  try {
                      this.onChoice(choiceData);
                  } catch (error) {
                      console.error('❌ MessageInterceptor: 选择回调执行失败:', error);
                  }
              }
          }

          /**
           * 🎯 处理选择项选中（由UILayout中的按钮调用）
           */
          handleChoiceSelected(index) {
              console.log('选择项被选中:', index);

              // 调用原生的选择处理
              if (window.$gameMessage && window.$gameMessage.onChoice) {
                  window.$gameMessage.onChoice(index);
              }
          }

          /**
           * 🎯 完成消息显示（由UI组件调用）
           */
          completeMessage() {
              console.log('🎯 MessageInterceptor: 完成消息显示');

              // 清除消息文本，让事件继续
              if (window.$gameMessage) {
                  window.$gameMessage.clear();
                  console.log('✅ MessageInterceptor: 消息已清除，事件可以继续');
              }
          }

          /**
           * 🎯 完成选择项（由UI组件调用）
           */
          completeChoice() {
              console.log('🎯 MessageInterceptor: 完成选择项');

              // 清除选择项，让事件继续
              if (window.$gameMessage) {
                  window.$gameMessage._choices = [];
                  window.$gameMessage._choiceDefaultType = 0;
                  window.$gameMessage._choiceCancelType = 0;
                  window.$gameMessage._choiceBackground = 0;
                  console.log('✅ MessageInterceptor: 选择项已清除，事件可以继续');
              }
          }
      }

      // 🚀 创建全局拦截器实例
      window.MessageInterceptor = new MessageInterceptor();

      console.log('✅ 消息拦截器已加载');



  // ===== spriteEventExpand.js =====
  //=============================================================================
      // 重写 Sprite 初始化，处理 bitmap 数据对象
      //=============================================================================
      const _Sprite_initialize = Sprite.prototype.initialize;

      Sprite.prototype.initialize = function (data) {
                 //     // 初始化组件系统
              this._components = new Map();           // 组件映射表
              this._componentList = [];               // 组件列表（用于更新）
              this._componentUpdateEnabled = true;    // 是否启用组件更新
              this._componentDebugMode = false;       // 调试模式
          // 如果是真正的 Bitmap 对象（有 canvas 属性），直接调用原始方法
          // console.log('🔧 Sprite.initialize 被调用，参数:', data);
          let bitmapData = null;
          let eventsData = null;

          // 检查是否是组合数据对象 { bitmap: {...}, events: {...} }
          if (data && typeof data === 'object' && !data._canvas) {
              if (data.bitmap || data.events) {
                  // console.log('✅ 检测到组合数据对象 (bitmap + events)');
                  bitmapData = data.bitmap;
                  eventsData = data.events;
              } else {
                  // 单纯的 bitmap 数据对象
                  // console.log('✅ 检测到 bitmap 数据对象', data);
                  bitmapData = data;
              }
          }

          // // 处理 bitmap 数据对象（能到这里的都是数据对象，不是真正的 Bitmap）
          let processedBitmap = bitmapData;

          if (bitmapData) {
              // console.log('🔄 转换 bitmap 数据对象:', bitmapData);
              if (bitmapData.url && typeof bitmapData.url === 'string') {
                  processedBitmap = ImageManager.loadBitmapFromUrl(bitmapData.url);
                  //   console.log("============================================================.1  utl",processedBitmap)
              } else if (bitmapData && bitmapData.elements && Array.isArray(bitmapData.elements)) {
                  // console.log('从 elements 创建 bitmap');
                  const newBitmap = new Bitmap(Graphics.width, Graphics.height);
                  // 设置其他属性
                  Object.assign(newBitmap, {
                      fontBold: bitmapData.fontBold || false,
                      fontFace: bitmapData.fontFace || 'GameFont',
                      fontItalic: bitmapData.fontItalic || false,
                      fontSize: bitmapData.fontSize || 28,
                      outlineColor: bitmapData.outlineColor || 'rgba(0, 0, 0, 0.5)',
                      outlineWidth: bitmapData.outlineWidth || 4,
                      textColor: bitmapData.textColor || '#ffffff',
                      _paintOpacity: bitmapData._paintOpacity || 255,
                      _smooth: bitmapData._smooth !== undefined ? bitmapData._smooth : true
                  });
                  // 处理 elements 数组，收集需要加载的图片
                  const imagesToLoad = [];
                  const processedElements = bitmapData.elements.map(element => {
                      if (element.type === 'image' && element.sourceUrl) {
                          const processedElement = { ...element };
                          const sourceBitmap = ImageManager.loadBitmapFromUrl(element.sourceUrl);
                          processedElement.source = sourceBitmap;

                          // 收集需要等待加载的图片
                          imagesToLoad.push(sourceBitmap);
                          return processedElement;
                      }
                      return element;
                  });

                  newBitmap.elements = processedElements;

                  // 等待所有图片加载完成后重新绘制
                  if (imagesToLoad.length > 0) {
                      let loadedCount = 0;
                      const checkAllLoaded = () => {
                          loadedCount++;
                          if (loadedCount >= imagesToLoad.length) {
                              // 所有图片都加载完成，重新绘制
                              // console.log('所有图片加载完成，重新绘制 bitmap');
                              if (newBitmap.redrawing) {
                                  newBitmap.redrawing();
                              }
                          }
                      };

                      // 为每个图片添加加载完成监听器
                      imagesToLoad.forEach(sourceBitmap => {
                          if (sourceBitmap.isReady && sourceBitmap.isReady()) {
                              // 图片已经加载完成
                              checkAllLoaded();
                          } else {
                              // 图片还在加载中，添加监听器
                              sourceBitmap.addLoadListener(checkAllLoaded);
                          }
                      });
                  } else {
                      // 没有图片需要加载，直接重新绘制
                      if (newBitmap.redrawing) {
                          newBitmap.redrawing();
                      }
                  }



                  processedBitmap = newBitmap;
              } else {
                  // 处理只有字体属性的 bitmap 数据对象
                  // console.log('处理字体属性 bitmap 数据对象:', bitmapData);
                  const newBitmap = new Bitmap(Graphics.width, Graphics.height);

                  // 应用字体相关属性
                  if (bitmapData.fontBold !== undefined) newBitmap.fontBold = bitmapData.fontBold;
                  if (bitmapData.fontFace) newBitmap.fontFace = bitmapData.fontFace;
                  if (bitmapData.fontItalic !== undefined) newBitmap.fontItalic = bitmapData.fontItalic;
                  if (bitmapData.fontSize !== undefined) newBitmap.fontSize = bitmapData.fontSize;
                  if (bitmapData.outlineColor) newBitmap.outlineColor = bitmapData.outlineColor;
                  if (bitmapData.outlineWidth !== undefined) newBitmap.outlineWidth = bitmapData.outlineWidth;
                  if (bitmapData.textColor) newBitmap.textColor = bitmapData.textColor;
                  if (bitmapData._paintOpacity !== undefined) newBitmap._paintOpacity = bitmapData._paintOpacity;
                  if (bitmapData._smooth !== undefined) newBitmap._smooth = bitmapData._smooth;

                  // console.log('创建的新 bitmap 对象:', newBitmap);
                  processedBitmap = newBitmap;
              }
          } else { processedBitmap = data }



          // //         // 处理事件数据对象
          //         if (eventsData) {
          //             console.log('🎯 设置 Sprite 事件:', eventsData);
          //             this.setupEvents(eventsData);
          //         }
          // console.log("✅ 检测到 Bitmap 对象，直接使用:", processedBitmap);
          _Sprite_initialize.call(this, processedBitmap);
      };

      //=============================================================================
      // 为 Sprite 添加事件处理系统
      //=============================================================================






  console.log("RPG Editor: 插件代码加载完成");
})();




// ===== Scene_Map 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Map");

  // ===== Scene_Map 方法重写 =====
  // 重写 createDisplayObjects - 自定义显示对象创建流程
  Scene_Map.prototype.createDisplayObjects = function() {
    console.log('RPG Editor: 使用自定义的 createDisplayObjects 方法');
    this.createSpriteset();
   // this.createWindowLayer();
    // 注意：不调用 this.createAllWindows(); - 在这里放置编辑器代码
    console.log('RPG Editor: 开始创建编辑器对象');

    const obj_UILabel_1753683738179_0 = new UILabel({
        text: 'New Label',
        componentScripts: [
            {
                    "id": "default_script",
                    "name": "默认脚本",
                    "enabled": true,
                    "code": "/**\n   * 🚀 onStart - 对象启动生命周期\n   * 触发时机: 对象创建并添加到父容器时自动触发\n   * 作用: 初始化对象状态、设置默认值、绑定数据等\n   * 配合方法: 无需手动调用，系统自动触发\n   */\n  function onStart() {\n    console.log(\"对象启动:\", self.name || \"unnamed\");\n    // 在这里添加初始化逻辑\n    // 例如: 设置初始文本、绑定数据源、初始化变量等\n  }\n\n  /**\n   * 🔄 onUpdate - 每帧更新生命周期\n   * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用\n   * 作用: 实现动画、实时数据更新、状态检查等\n   * 配合方法: 无需手动调用，系统自动触发\n   * 注意: 避免在此方法中执行耗时操作\n   */\n  // function onUpdate() {\n  //   console.log(\"每帧更新:\", self.name);\n  //   // 在这里添加每帧更新逻辑\n  //   // 例如: 动画更新、实时数据显示、状态检查等\n  // }\n\n  /**\n   * 📝 onFieldUpdate - 字段更新生命周期\n   * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发\n   * 作用: 响应数据变化、更新UI显示、同步状态等\n   * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)\n   * 参数: ctx - 包含字段更新上下文信息的对象\n   */\n  // function onFieldUpdate(ctx) {\n  //   console.log(\"字段更新:\", self.name, ctx);\n  //   // 在这里添加字段更新时的处理逻辑\n  //   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }\n  //   // 例如: 根据 ctx.field 判断更新哪个属性\n  // }\n\n  /**\n   * 🗑️ onDestroy - 对象销毁生命周期\n   * 触发时机: 对象从父容器移除时自动触发\n   * 作用: 清理资源、保存数据、解除绑定等\n   * 配合方法: 无需手动调用，系统自动触发\n   */\n  // function onDestroy() {\n  //   console.log(\"对象销毁:\", self.name);\n  //   // 在这里添加清理逻辑\n  //   // 例如: 清理定时器、保存状态、解除事件绑定等\n  // }",
                    "description": "默认脚本，包含常用方法模板"
            }
    ],
        prefix: '',
        suffix: '',
        fontSize: 16,
        fontFace: 'GameFont',
        fontBold: false,
        fontItalic: false,
        textColor: '#ffffff',
        outlineColor: '#000000',
        outlineWidth: 4,
        textAlign: 'left',
        verticalAlign: 'middle',
    });
    obj_UILabel_1753683738179_0.name = "UILabel_1753683738179";
    obj_UILabel_1753683738179_0.width = 200;
    obj_UILabel_1753683738179_0.height = 40;
    this.addChild(obj_UILabel_1753683738179_0);


    console.log('RPG Editor: 编辑器对象创建完成');
    // 注意：不调用 this.createButtons(); - 跳过原生按钮创建
    console.log('RPG Editor: createDisplayObjects 完成，已跳过原生窗口和按钮创建');
  };


  // 重写 createAllWindows - 跳过原生窗口创建
  Scene_Map.prototype.createAllWindows = function() {
    console.log('RPG Editor: 跳过原生窗口创建，使用编辑器对象');
  };

  // 重写 createButtons - 跳过原生按钮创建
  Scene_Map.prototype.createButtons = function() {
    console.log('RPG Editor: 跳过原生按钮创建，使用编辑器对象');
  };

  // 重写 update - 保留完整的更新逻辑
  Scene_Map.prototype.update = function() {
    // 调用父类更新（Scene_Message.prototype.update）
    Scene_Message.prototype.update.call(this);
    // 保留完整的更新逻辑以支持点击移动和事件处理
    this.updateDestination();
    this.updateMenuButton();
    this.updateMapNameWindow();
    this.updateMainMultiply();
    if (this.isSceneChangeOk()) {
      this.updateScene();
    } else if (SceneManager.isNextScene(Scene_Battle)) {
      this.updateEncounterEffect();
    }
    this.updateWaitCount();
  };

  // 重写 updateMainMultiply - 保留游戏逻辑更新
  Scene_Map.prototype.updateMainMultiply = function() {
    if (this.isFastForward()) {
      this.cancelMessageWait();
      this.updateMain();
    }
    this.updateMain();
  };

  // 重写 updateMain - 保留核心游戏逻辑
  Scene_Map.prototype.updateMain = function() {
    $gameMap.update(this.isActive());
    $gamePlayer.update(this.isPlayerActive());
    $gameTimer.update(this.isActive());
    $gameScreen.update();
  };

  // 重写 updateMapNameWindow - 安全检查窗口对象
  Scene_Map.prototype.updateMapNameWindow = function() {
   // console.log('RPG Editor: updateMapNameWindow - 跳过地图名窗口更新');
    // 跳过地图名窗口更新，避免访问不存在的窗口对象
  };

  // 重写 updateTransferPlayer - 安全检查
  Scene_Map.prototype.updateTransferPlayer = function() {
    if ($gamePlayer.isTransferring()) {
      SceneManager.goto(Scene_Map);
    }
  };

  // 重写 updateMenuButton - 安全检查按钮对象
  Scene_Map.prototype.updateMenuButton = function() {
   // console.log('RPG Editor: updateMenuButton - 跳过菜单按钮更新');
    // 跳过菜单按钮更新，避免访问不存在的按钮对象
  };

  // 重写 updateDestination - 保留目标点更新和事件处理
  Scene_Map.prototype.updateDestination = function() {
    // 保留完整的目标点更新逻辑，这对事件处理很重要
    if (this.isMapTouchOk()) {
      this.processMapTouch();
    } else {
      // 关键：清除目标点，避免鼠标一直闪烁
      $gameTemp.clearDestination();
      this._touchCount = 0;
    }
  };

  // 重写 isMapTouchOk - 添加事件运行检查
  Scene_Map.prototype.isMapTouchOk = function() {
    // 当事件运行时不允许地图点击，避免冲突
    return this.isActive() && $gamePlayer.canMove() && !this.isAnyButtonPressed() && !$gameMap.isEventRunning();
  };

  // 重写 onTransferEnd - 安全检查窗口对象
  Scene_Map.prototype.onTransferEnd = function() {
    console.log('RPG Editor: onTransferEnd - 跳过窗口操作');
    // 跳过原生窗口操作，避免访问不存在的窗口对象
    this._mapNameWindow = null; // 确保窗口引用为空
  };

  // 重写 start - 安全检查窗口对象
  Scene_Map.prototype.start = function() {
    Scene_Base.prototype.start.call(this);
    console.log('RPG Editor: Scene_Map start - 跳过窗口操作');
    // 跳过地图名窗口显示等操作
  };

  console.log("RPG Editor: Scene_Map 处理完成");
})();

// ===== Scene_Title 场景立即执行方法 =====
(() => {
  "use strict";
  console.log("RPG Editor: 开始处理 Scene_Title");

  // Scene_Title 场景创建方法
  Scene_Title.prototype.create = function() {
    Scene_Base.prototype.create.call(this);
    console.log('RPG Editor: 开始创建 Scene_Title 的自定义对象');

    // 创建 Sprite（包含 bitmap）
    const obj_Sprite_0 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/titles1/Ruins.png'
      });
    obj_Sprite_0.x = 408;
    obj_Sprite_0.y = 312;
    obj_Sprite_0.width = 816;
    obj_Sprite_0.height = 624;
    obj_Sprite_0.anchor.x = 0.5;
    obj_Sprite_0.anchor.y = 0.5;
    this.addChild(obj_Sprite_0);

    // 创建 Sprite（包含 bitmap）
    const obj_Sprite_1 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true
      });
    obj_Sprite_1.x = 408;
    obj_Sprite_1.y = 312;
    obj_Sprite_1.width = 1;
    obj_Sprite_1.height = 1;
    obj_Sprite_1.scale.x = 816;
    obj_Sprite_1.scale.y = 816;
    obj_Sprite_1.anchor.x = 0.5;
    obj_Sprite_1.anchor.y = 0.5;
    this.addChild(obj_Sprite_1);

    // 创建 Sprite（包含 bitmap）
    const obj_Sprite_2 = new Sprite({
        fontBold: false,
        fontFace: 'rmmz-mainfont, Microsoft Yahei, PingFang SC, sans-serif',
        fontItalic: false,
        fontSize: 72,
        outlineColor: 'black',
        outlineWidth: 8,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        elements: [
          {
            type: 'text',
            text: 'Project6',
            x: 20,
            y: 156,
            maxWidth: 776,
            lineHeight: 48,
            align: 'center'
          }
        ]
      });
    obj_Sprite_2.width = 816;
    obj_Sprite_2.height = 624;
    this.addChild(obj_Sprite_2);

    // 创建 Sprite
    const obj_WindowLayer_3 = new Sprite();
    obj_WindowLayer_3.x = 4;
    obj_WindowLayer_3.y = 4;
    obj_WindowLayer_3.width = 240;
    obj_WindowLayer_3.height = 156;
    // 添加子对象
    // 创建 Sprite
    const obj_WindowLayer_3_child0 = new Sprite();
    obj_WindowLayer_3_child0.x = 284;
    obj_WindowLayer_3_child0.y = 364;
    obj_WindowLayer_3_child0.width = 240;
    obj_WindowLayer_3_child0.height = 156;
    // 添加子对象
    const obj_WindowLayer_3_child0_child0 = new UIContainer({
        y: 78,
        width: 240,
        componentScripts: [
            {
                    "id": "default_lifecycle",
                    "name": "生命周期脚本",
                    "type": "lifecycle",
                    "enabled": true,
                    "description": "默认的生命周期脚本",
                    "code": "/**\n * 🚀 onStart - 对象启动生命周期\n * 触发时机: 对象创建并添加到父容器时自动触发\n * 作用: 初始化对象状态、设置默认值、绑定数据等\n * 配合方法: 无需手动调用，系统自动触发\n */\nfunction onStart() {\n  console.log(\"容器启动:\", self.name || \"unnamed\");\n  // 在这里添加初始化逻辑\n  // 例如: 设置初始背景、配置布局、初始化子对象等\n}\n\n/**\n * 🔄 onUpdate - 每帧更新生命周期\n * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用\n * 作用: 实现动画、实时数据更新、状态检查等\n * 配合方法: 无需手动调用，系统自动触发\n * 注意: 避免在此方法中执行耗时操作\n */\n// function onUpdate() {\n//   console.log(\"每帧更新:\", self.name);\n//   // 在这里添加每帧更新逻辑\n//   // 例如: 动画更新、布局调整、状态检查等\n// }\n\n/**\n * 📝 onFieldUpdate - 字段更新生命周期\n * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发\n * 作用: 响应数据变化、更新UI显示、同步状态等\n * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)\n * 参数: ctx - 包含字段更新上下文信息的对象\n */\n// function onFieldUpdate(ctx) {\n//   console.log(\"字段更新:\", self.name, ctx);\n//   // 在这里添加字段更新时的处理逻辑\n//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }\n//   // 例如: 根据 ctx.field 判断更新哪个属性\n// }\n\n/**\n * 🗑️ onDestroy - 对象销毁生命周期\n * 触发时机: 对象从父容器移除时自动触发\n * 作用: 清理资源、保存数据、解除绑定等\n * 配合方法: 无需手动调用，系统自动触发\n */\n// function onDestroy() {\n//   console.log(\"对象销毁:\", self.name);\n//   // 在这里添加清理逻辑\n//   // 例如: 清理定时器、保存状态、解除事件绑定等\n// }"
            }
    ],
        interactive: false,
    });
    obj_WindowLayer_3_child0_child0.x = 0;
    obj_WindowLayer_3_child0_child0.y = 78;
    obj_WindowLayer_3_child0_child0.width = 240;
    obj_WindowLayer_3_child0_child0.height = 100;
    // 添加子对象
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child0 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child0.x = 4;
    obj_WindowLayer_3_child0_child0_child0.y = 4;
    obj_WindowLayer_3_child0_child0_child0.width = 95;
    obj_WindowLayer_3_child0_child0_child0.height = 95;
    obj_WindowLayer_3_child0_child0_child0.scale.x = 2.442105263157895;
    obj_WindowLayer_3_child0_child0_child0.scale.y = 1.5578947368421052;
    obj_WindowLayer_3_child0_child0_child0.alpha = 0.7529411764705882;
    // 添加子对象
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child0_child0 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child0_child0.width = 232;
    obj_WindowLayer_3_child0_child0_child0_child0.height = 148;
    obj_WindowLayer_3_child0_child0_child0_child0.scale.x = 0.40948275862068967;
    obj_WindowLayer_3_child0_child0_child0_child0.scale.y = 0.6418918918918919;
    obj_WindowLayer_3_child0_child0_child0.addChild(obj_WindowLayer_3_child0_child0_child0_child0);
    obj_WindowLayer_3_child0_child0.addChild(obj_WindowLayer_3_child0_child0_child0);
    // 创建 Sprite
    const obj_WindowLayer_3_child0_child0_child1 = new Sprite();
    obj_WindowLayer_3_child0_child0_child1.width = 240;
    obj_WindowLayer_3_child0_child0_child1.height = 156;
    // 添加子对象
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child0 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child0.width = 24;
    obj_WindowLayer_3_child0_child0_child1_child0.height = 24;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child0);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child1 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child1.x = 216;
    obj_WindowLayer_3_child0_child0_child1_child1.y = 0;
    obj_WindowLayer_3_child0_child0_child1_child1.width = 24;
    obj_WindowLayer_3_child0_child0_child1_child1.height = 24;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child1);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child2 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child2.x = 0;
    obj_WindowLayer_3_child0_child0_child1_child2.y = 132;
    obj_WindowLayer_3_child0_child0_child1_child2.width = 24;
    obj_WindowLayer_3_child0_child0_child1_child2.height = 24;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child2);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child3 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child3.x = 216;
    obj_WindowLayer_3_child0_child0_child1_child3.y = 132;
    obj_WindowLayer_3_child0_child0_child1_child3.width = 24;
    obj_WindowLayer_3_child0_child0_child1_child3.height = 24;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child3);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child4 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child4.x = 24;
    obj_WindowLayer_3_child0_child0_child1_child4.y = 0;
    obj_WindowLayer_3_child0_child0_child1_child4.width = 48;
    obj_WindowLayer_3_child0_child0_child1_child4.height = 24;
    obj_WindowLayer_3_child0_child0_child1_child4.scale.x = 4;
    obj_WindowLayer_3_child0_child0_child1_child4.scale.y = 1;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child4);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child5 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child5.x = 24;
    obj_WindowLayer_3_child0_child0_child1_child5.y = 132;
    obj_WindowLayer_3_child0_child0_child1_child5.width = 48;
    obj_WindowLayer_3_child0_child0_child1_child5.height = 24;
    obj_WindowLayer_3_child0_child0_child1_child5.scale.x = 4;
    obj_WindowLayer_3_child0_child0_child1_child5.scale.y = 1;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child5);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child6 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child6.x = 0;
    obj_WindowLayer_3_child0_child0_child1_child6.y = 24;
    obj_WindowLayer_3_child0_child0_child1_child6.width = 24;
    obj_WindowLayer_3_child0_child0_child1_child6.height = 48;
    obj_WindowLayer_3_child0_child0_child1_child6.scale.x = 1;
    obj_WindowLayer_3_child0_child0_child1_child6.scale.y = 2.25;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child6);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child0_child1_child7 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child0_child1_child7.x = 216;
    obj_WindowLayer_3_child0_child0_child1_child7.y = 24;
    obj_WindowLayer_3_child0_child0_child1_child7.width = 24;
    obj_WindowLayer_3_child0_child0_child1_child7.height = 48;
    obj_WindowLayer_3_child0_child0_child1_child7.scale.x = 1;
    obj_WindowLayer_3_child0_child0_child1_child7.scale.y = 2.25;
    obj_WindowLayer_3_child0_child0_child1.addChild(obj_WindowLayer_3_child0_child0_child1_child7);
    obj_WindowLayer_3_child0_child0.addChild(obj_WindowLayer_3_child0_child0_child1);
    obj_WindowLayer_3_child0.addChild(obj_WindowLayer_3_child0_child0);
    // 创建 Sprite
    const obj_WindowLayer_3_child0_child1 = new Sprite();
    obj_WindowLayer_3_child0_child1.x = 12;
    obj_WindowLayer_3_child0_child1.y = 12;
    obj_WindowLayer_3_child0_child1.visible = false;
    // 添加子对象
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child0 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true
      });
    obj_WindowLayer_3_child0_child1_child0.width = 216;
    obj_WindowLayer_3_child0_child1_child0.height = 176;
    obj_WindowLayer_3_child0_child1.addChild(obj_WindowLayer_3_child0_child1_child0);
    // 创建 Sprite
    const obj_WindowLayer_3_child0_child1_child1 = new Sprite();
    obj_WindowLayer_3_child0_child1_child1.x = 4;
    obj_WindowLayer_3_child0_child1_child1.y = 46;
    obj_WindowLayer_3_child0_child1_child1.width = 208;
    obj_WindowLayer_3_child0_child1_child1.height = 40;
    obj_WindowLayer_3_child0_child1_child1.alpha = 0.9375;
    obj_WindowLayer_3_child0_child1_child1.visible = false;
    // 添加子对象
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child0 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child0.width = 4;
    obj_WindowLayer_3_child0_child1_child1_child0.height = 4;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child0);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child1 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child1.x = 204;
    obj_WindowLayer_3_child0_child1_child1_child1.y = 0;
    obj_WindowLayer_3_child0_child1_child1_child1.width = 4;
    obj_WindowLayer_3_child0_child1_child1_child1.height = 4;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child1);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child2 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child2.x = 0;
    obj_WindowLayer_3_child0_child1_child1_child2.y = 36;
    obj_WindowLayer_3_child0_child1_child1_child2.width = 4;
    obj_WindowLayer_3_child0_child1_child1_child2.height = 4;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child2);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child3 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child3.x = 204;
    obj_WindowLayer_3_child0_child1_child1_child3.y = 36;
    obj_WindowLayer_3_child0_child1_child1_child3.width = 4;
    obj_WindowLayer_3_child0_child1_child1_child3.height = 4;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child3);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child4 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child4.x = 4;
    obj_WindowLayer_3_child0_child1_child1_child4.y = 0;
    obj_WindowLayer_3_child0_child1_child1_child4.width = 40;
    obj_WindowLayer_3_child0_child1_child1_child4.height = 4;
    obj_WindowLayer_3_child0_child1_child1_child4.scale.x = 5;
    obj_WindowLayer_3_child0_child1_child1_child4.scale.y = 1;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child4);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child5 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child5.x = 4;
    obj_WindowLayer_3_child0_child1_child1_child5.y = 36;
    obj_WindowLayer_3_child0_child1_child1_child5.width = 40;
    obj_WindowLayer_3_child0_child1_child1_child5.height = 4;
    obj_WindowLayer_3_child0_child1_child1_child5.scale.x = 5;
    obj_WindowLayer_3_child0_child1_child1_child5.scale.y = 1;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child5);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child6 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child6.x = 0;
    obj_WindowLayer_3_child0_child1_child1_child6.y = 4;
    obj_WindowLayer_3_child0_child1_child1_child6.width = 4;
    obj_WindowLayer_3_child0_child1_child1_child6.height = 40;
    obj_WindowLayer_3_child0_child1_child1_child6.scale.x = 1;
    obj_WindowLayer_3_child0_child1_child1_child6.scale.y = 0.8;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child6);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child7 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child7.x = 204;
    obj_WindowLayer_3_child0_child1_child1_child7.y = 4;
    obj_WindowLayer_3_child0_child1_child1_child7.width = 4;
    obj_WindowLayer_3_child0_child1_child1_child7.height = 40;
    obj_WindowLayer_3_child0_child1_child1_child7.scale.x = 1;
    obj_WindowLayer_3_child0_child1_child1_child7.scale.y = 0.8;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child7);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child1_child8 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child1_child1_child8.x = 4;
    obj_WindowLayer_3_child0_child1_child1_child8.y = 4;
    obj_WindowLayer_3_child0_child1_child1_child8.width = 40;
    obj_WindowLayer_3_child0_child1_child1_child8.height = 40;
    obj_WindowLayer_3_child0_child1_child1_child8.scale.x = 5;
    obj_WindowLayer_3_child0_child1_child1_child8.scale.y = 0.8;
    obj_WindowLayer_3_child0_child1_child1.addChild(obj_WindowLayer_3_child0_child1_child1_child8);
    obj_WindowLayer_3_child0_child1.addChild(obj_WindowLayer_3_child0_child1_child1);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child1_child2 = new Sprite({
        fontBold: false,
        fontFace: 'rmmz-mainfont, Microsoft Yahei, PingFang SC, sans-serif',
        fontItalic: false,
        fontSize: 26,
        outlineColor: 'rgba(0, 0, 0, 0.6)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        elements: [
          {
            type: 'text',
            text: '重新开始',
            x: 12,
            y: 4,
            maxWidth: 192,
            lineHeight: 36,
            align: 'center'
          },
          {
            type: 'text',
            text: '继续游戏',
            x: 12,
            y: 48,
            maxWidth: 192,
            lineHeight: 36,
            align: 'center'
          },
          {
            type: 'text',
            text: '选项',
            x: 12,
            y: 92,
            maxWidth: 192,
            lineHeight: 36,
            align: 'center'
          }
        ]
      });
    obj_WindowLayer_3_child0_child1_child2.width = 216;
    obj_WindowLayer_3_child0_child1_child2.height = 176;
    obj_WindowLayer_3_child0_child1.addChild(obj_WindowLayer_3_child0_child1_child2);
    obj_WindowLayer_3_child0.addChild(obj_WindowLayer_3_child0_child1);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child2 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child2.x = 120;
    obj_WindowLayer_3_child0_child2.y = 144;
    obj_WindowLayer_3_child0_child2.width = 24;
    obj_WindowLayer_3_child0_child2.height = 12;
    obj_WindowLayer_3_child0_child2.visible = false;
    obj_WindowLayer_3_child0_child2.anchor.x = 0.5;
    obj_WindowLayer_3_child0_child2.anchor.y = 0.5;
    obj_WindowLayer_3_child0.addChild(obj_WindowLayer_3_child0_child2);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child3 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child3.x = 120;
    obj_WindowLayer_3_child0_child3.y = 12;
    obj_WindowLayer_3_child0_child3.width = 24;
    obj_WindowLayer_3_child0_child3.height = 12;
    obj_WindowLayer_3_child0_child3.visible = false;
    obj_WindowLayer_3_child0_child3.anchor.x = 0.5;
    obj_WindowLayer_3_child0_child3.anchor.y = 0.5;
    obj_WindowLayer_3_child0.addChild(obj_WindowLayer_3_child0_child3);
    // 创建 Sprite（包含 bitmap）
    const obj_WindowLayer_3_child0_child4 = new Sprite({
        fontBold: false,
        fontFace: 'sans-serif',
        fontItalic: false,
        fontSize: 16,
        outlineColor: 'rgba(0, 0, 0, 0.5)',
        outlineWidth: 3,
        textColor: '#ffffff',
        _paintOpacity: 255,
        _smooth: true,
        url: 'img/system/Window.png'
      });
    obj_WindowLayer_3_child0_child4.x = 120;
    obj_WindowLayer_3_child0_child4.y = 156;
    obj_WindowLayer_3_child0_child4.width = 24;
    obj_WindowLayer_3_child0_child4.height = 24;
    obj_WindowLayer_3_child0_child4.alpha = 0;
    obj_WindowLayer_3_child0_child4.visible = false;
    obj_WindowLayer_3_child0_child4.anchor.x = 0.5;
    obj_WindowLayer_3_child0_child4.anchor.y = 1;
    obj_WindowLayer_3_child0.addChild(obj_WindowLayer_3_child0_child4);
    obj_WindowLayer_3.addChild(obj_WindowLayer_3_child0);
    this.addChild(obj_WindowLayer_3);

    // obj_WindowLayer_3 的子对象已在组件创建代码中处理
    const obj_UIInput_1753700989142_4 = new UIInput({
        width: 200,
        height: 40,
        value: 'sadgggss',
        placeholder: '请输入文本...',
        inputType: 'text',
        maxLength: 255,
        readonly: false,
        fontSize: 20,
        fontFace: 'rmmz-mainfont',
        textColor: '#e5d6d6',
        placeholderColor: '#999999',
        backgroundColor: '#473e3e',
        borderColor: '#cccccc',
        focusBorderColor: '#007bff',
        borderWidth: 1,
        borderRadius: 4,
        letterSpacing: 2.5,
        validationMessage: '输入格式不正确',
        componentScripts: [
            {
                    "id": "default_script",
                    "name": "默认脚本",
                    "enabled": true,
                    "code": "/**\n * 🚀 onStart - 对象启动生命周期\n * 触发时机: 对象创建并添加到父容器时自动触发\n * 作用: 初始化对象状态、设置默认值、绑定数据等\n * 配合方法: 无需手动调用，系统自动触发\n */\nfunction onStart() {\n  console.log(\"对象启动:\", self.name || \"unnamed\");\n  // 在这里添加初始化逻辑\n  // 例如: 设置初始文本、绑定数据源、初始化变量等\n}\n\n/**\n * 🔄 onUpdate - 每帧更新生命周期\n * 触发时机: 每帧自动触发（约60FPS），只要对象存在就会持续调用\n * 作用: 实现动画、实时数据更新、状态检查等\n * 配合方法: 无需手动调用，系统自动触发\n * 注意: 避免在此方法中执行耗时操作\n */\n// function onUpdate() {\n//   console.log(\"每帧更新:\", self.name);\n//   // 在这里添加每帧更新逻辑\n//   // 例如: 动画更新、实时数据显示、状态检查等\n// }\n\n/**\n * 📝 onFieldUpdate - 字段更新生命周期\n * 触发时机: 调用 updateFieldsToChildren(ctx) 或 onFieldUpdate(ctx) 时触发\n * 作用: 响应数据变化、更新UI显示、同步状态等\n * 配合方法: parentObject.updateFieldsToChildren(ctx) 或 object.onFieldUpdate(ctx)\n * 参数: ctx - 包含字段更新上下文信息的对象\n */\n// function onFieldUpdate(ctx) {\n//   console.log(\"字段更新:\", self.name, ctx);\n//   // 在这里添加字段更新时的处理逻辑\n//   // ctx 包含: { field, oldValue, newValue, source, timestamp 等 }\n//   // 例如: 根据 ctx.field 判断更新哪个属性\n// }\n\n/**\n * 🗑️ onDestroy - 对象销毁生命周期\n * 触发时机: 对象从父容器移除时自动触发\n * 作用: 清理资源、保存数据、解除绑定等\n * 配合方法: 无需手动调用，系统自动触发\n */\n// function onDestroy() {\n//   console.log(\"对象销毁:\", self.name);\n//   // 在这里添加清理逻辑\n//   // 例如: 清理定时器、保存状态、解除事件绑定等\n// }\n\n/**\n * 🔄 onChange - 文本内容改变事件\n * 触发时机: 用户输入文本导致值改变时触发\n * 作用: 处理文本变化、实时验证、数据同步等\n * 参数: newValue - 新的文本值, oldValue - 旧的文本值\n */\nfunction onChange(newValue, oldValue) {\n  console.log(\"文本改变:\", oldValue, \"→\", newValue);\n  // 在这里添加文本变化处理逻辑\n  // 例如: 实时验证、数据同步、格式化等\n}\n\n/**\n * 🎯 onFocus - 获得焦点事件\n * 触发时机: 输入框获得焦点时触发\n * 作用: 处理焦点获得时的逻辑\n */\n// function onFocus() {\n//   console.log(\"输入框获得焦点:\", self.name || \"unnamed\");\n//   // 在这里添加获得焦点时的处理逻辑\n//   // 例如: 显示提示信息、改变样式等\n// }\n\n/**\n * 🎯 onBlur - 失去焦点事件\n * 触发时机: 输入框失去焦点时触发\n * 作用: 处理焦点失去时的逻辑、数据验证等\n */\n// function onBlur() {\n//   console.log(\"输入框失去焦点:\", self.name || \"unnamed\");\n//   // 在这里添加失去焦点时的处理逻辑\n//   // 例如: 数据验证、保存数据等\n// }\n\n/**\n * ⌨️ onEnterPressed - 回车键按下事件\n * 触发时机: 用户在输入框中按下回车键时触发\n * 作用: 处理回车键确认操作\n */\n// function onEnterPressed() {\n//   console.log(\"回车键按下:\", self.value);\n//   // 在这里添加回车键处理逻辑\n//   // 例如: 提交表单、确认输入等\n// }\n\n/**\n * ❌ onValidationFailed - 输入验证失败事件\n * 触发时机: 输入内容不符合验证规则时触发\n * 作用: 处理验证失败的情况\n * 参数: errorMessage - 错误信息\n */\n// function onValidationFailed(errorMessage) {\n//   console.log(\"输入验证失败:\", errorMessage);\n//   // 在这里添加验证失败处理逻辑\n//   // 例如: 显示错误提示、重置输入等\n// }",
                    "description": "默认脚本，包含常用方法模板"
            }
    ]
    });
    obj_UIInput_1753700989142_4.name = "UIInput_1753700989142";
    obj_UIInput_1753700989142_4.x = 50;
    obj_UIInput_1753700989142_4.y = 50;
    obj_UIInput_1753700989142_4.width = 200;
    obj_UIInput_1753700989142_4.height = 40;
    this.addChild(obj_UIInput_1753700989142_4);

    console.log('RPG Editor: Scene_Title 自定义对象创建完成');
  };

  // ===== Scene_Title 方法重写 =====
  // 重写 createBackground - 跳过原生背景创建
  Scene_Title.prototype.createBackground = function() {
    console.log('RPG Editor: 跳过原生背景创建，使用编辑器对象');
  };

  // 重写 createForeground - 跳过原生前景创建
  Scene_Title.prototype.createForeground = function() {
    console.log('RPG Editor: 跳过原生前景创建，使用编辑器对象');
  };

  // 重写 adjustBackground - 避免访问不存在的背景精灵
  Scene_Title.prototype.adjustBackground = function() {
    console.log('RPG Editor: 跳过背景调整，使用编辑器设置的属性');
  };

  // 重写 createCommandWindow - 跳过原生命令窗口创建
  Scene_Title.prototype.createCommandWindow = function() {
    console.log('RPG Editor: 跳过原生命令窗口创建，使用编辑器对象');
  };

  // 重写 isBusy - 安全检查命令窗口状态
  Scene_Title.prototype.isBusy = function() {
    if (this._commandWindow && this._commandWindow.isClosing) {
      return this._commandWindow.isClosing();
    }
    return Scene_Base.prototype.isBusy.call(this);
  };

  // 重写 update - 安全检查命令窗口
  Scene_Title.prototype.update = function() {
    if (!this.isBusy() && this._commandWindow && this._commandWindow.open) {
      this._commandWindow.open();
    }
    Scene_Base.prototype.update.call(this);
  };

  // 重写 terminate - 安全检查游戏标题精灵
  Scene_Title.prototype.terminate = function() {
    Scene_Base.prototype.terminate.call(this);
    SceneManager.snapForBackground();
    if (this._gameTitleSprite && this._gameTitleSprite.bitmap) {
      this._gameTitleSprite.bitmap.destroy();
    }
  };

  // 重写 scaleSprite - 安全检查精灵对象
  Scene_Title.prototype.scaleSprite = function(sprite) {
    if (sprite && sprite.bitmap) {
      Scene_Base.prototype.scaleSprite.call(this, sprite);
    }
  };

  // 重写 centerSprite - 安全检查精灵对象
  Scene_Title.prototype.centerSprite = function(sprite) {
    if (sprite && sprite.bitmap) {
      Scene_Base.prototype.centerSprite.call(this, sprite);
    }
  };

  console.log("RPG Editor: Scene_Title 处理完成");
})();